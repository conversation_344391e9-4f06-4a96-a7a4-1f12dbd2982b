"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdvancedCodeEditingTool = void 0;
// Import core Node.js modules
const vscode = __importStar(require("vscode"));
const path = __importStar(require("path"));
const crypto_1 = require("crypto");
const zod_1 = require("zod");
// Import the ts-morph library for deep AST manipulation in TypeScript/JavaScript.
// This is a production dependency for any serious structural refactoring tool.
const ts_morph_1 = require("ts-morph");
// Import core framework contracts, classes, and functions from toolFramework.ts
const toolFramework_1 = require("./toolFramework");
// #region --- Zod Schemas for Rigorous Tool Action Validation ---
const FilePathSchema = zod_1.z.string().describe("The workspace-relative path to the file.");
const LineNumberSchema = zod_1.z.number().int().positive().describe("The 1-based line number.");
const CreateBackupSchema = zod_1.z.boolean().optional().default(false).describe("If true, creates a .bak file before modification.");
const ReplaceLinesInputSchema = zod_1.z.object({
    filePath: FilePathSchema,
    startLine: LineNumberSchema,
    endLine: LineNumberSchema,
    newContent: zod_1.z.string().describe("The new content to substitute for the specified lines."),
    createBackup: CreateBackupSchema,
});
const InsertLinesInputSchema = zod_1.z.object({
    filePath: FilePathSchema,
    line: LineNumberSchema,
    newContent: zod_1.z.string().describe("The content to be inserted."),
    createBackup: CreateBackupSchema,
});
const FindAndReplaceInputSchema = zod_1.z.object({
    globPattern: zod_1.z.string().describe("A glob pattern to find files to search within (e.g., 'src/**/*.ts')."),
    findPattern: zod_1.z.string().describe("The text or regex pattern to search for."),
    replacePattern: zod_1.z.string().describe("The text or regex replacement pattern."),
    isRegex: zod_1.z.boolean().optional().default(false).describe("Indicates if the findPattern is a regular expression."),
});
const MoveAndRefactorInputSchema = zod_1.z.object({
    sourcePath: zod_1.z.string().describe("The current workspace-relative path of the file or directory to move."),
    destinationPath: zod_1.z.string().describe("The new workspace-relative path for the file or directory."),
});
const RenameSymbolInputSchema = zod_1.z.object({
    filePath: FilePathSchema,
    line: LineNumberSchema,
    column: zod_1.z.number().int().positive().describe("The 1-based column number where the symbol is located."),
    newName: zod_1.z.string().min(1).describe("The new name for the symbol. All references will be updated."),
});
const ExtractToFunctionInputSchema = zod_1.z.object({
    filePath: FilePathSchema,
    startLine: LineNumberSchema,
    endLine: LineNumberSchema,
    newFunctionName: zod_1.z.string().min(1).describe("The name of the new function to be created."),
});
const EncapsulateFieldInputSchema = zod_1.z.object({
    filePath: FilePathSchema,
    className: zod_1.z.string().describe("The name of the class containing the field."),
    propertyName: zod_1.z.string().describe("The name of the public field to encapsulate."),
});
const UntangleAndRestructureFileInputSchema = zod_1.z.object({
    filePath: FilePathSchema,
    aiRestructure: zod_1.z.boolean().optional().default(false).describe("If true, after cleaning syntax, an AI will attempt to logically reorder code blocks."),
    runLinterFix: zod_1.z.boolean().optional().default(true).describe("If true, attempts to run the project's configured linter with --fix."),
});
const AIRefactorInputSchema = zod_1.z.object({
    filePath: FilePathSchema,
    refactorInstruction: zod_1.z.string().describe("A detailed, natural language instruction for the desired refactoring."),
    startLine: LineNumberSchema.optional(),
    endLine: LineNumberSchema.optional(),
});
const GenerateCodeInputSchema = zod_1.z.object({
    filePath: FilePathSchema,
    generationInstruction: zod_1.z.string().describe("A detailed, natural language instruction for the code to be generated."),
    line: LineNumberSchema.optional().describe("Optional line number for insertion. If omitted, appends to the end."),
});
const GenerateCommitMessageInputSchema = zod_1.z.object({
    conventionalCommit: zod_1.z.boolean().optional().default(true).describe("If true, generates a message following the Conventional Commits specification."),
});
const ManageNpmDependencyInputSchema = zod_1.z.object({
    packageName: zod_1.z.string().describe("The name of the npm package to add or remove."),
    action: zod_1.z.enum(['add', 'remove']).describe("Whether to add or remove the dependency."),
    isDevDependency: zod_1.z.boolean().optional().default(false).describe("True if this is a development dependency."),
});
class TypeScriptStrategy {
    project;
    logger;
    constructor(project, logger) {
        this.project = project;
        this.logger = logger;
    }
    getSourceFileOrThrow(filePath) {
        const absolutePath = this.resolvePath(filePath);
        const sourceFile = this.project.getSourceFile(absolutePath);
        if (!sourceFile)
            throw new Error(`TypeScript strategy could not find file in project: ${filePath}`);
        return sourceFile;
    }
    async renameSymbol(filePath, line, column, newName) {
        this.logger.debug(`TS Strategy: Renaming symbol at ${filePath}:${line}:${column} to '${newName}'`);
        const sourceFile = this.getSourceFileOrThrow(filePath);
        const position = sourceFile.compilerNode.getPositionOfLineAndCharacter(line - 1, column - 1);
        const renameNode = sourceFile.getDescendantAtPos(position);
        if (!renameNode)
            throw new Error(`No symbol found at ${filePath}:${line}:${column}.`);
        renameNode.rename(newName);
        return await this.saveModifiedFiles();
    }
    async extractToFunction(filePath, startLine, endLine, newFunctionName) {
        this.logger.debug(`TS Strategy: Extracting lines ${startLine}-${endLine} of ${filePath} to function '${newFunctionName}'`);
        const sourceFile = this.getSourceFileOrThrow(filePath);
        const statements = sourceFile.getStatementsInRan(startLine, endLine);
        if (statements.length === 0)
            throw new Error("No code statements found in the specified range.");
        const insertionPoint = statements[0].getParentSyntaxList() || sourceFile;
        const insertionIndex = statements[0].getChildIndex();
        const writer = ts_morph_1.Writers.union((w) => w.write(statements.map(s => s.getText()).join('\n')));
        const refactoring = sourceFile.getLanguageService().getApplicableRefactors(statements[0].getStart(), statements[statements.length - 1].getEnd());
        const extractAction = refactoring.find(r => r.name === "Extract Symbol")?.actions.find(a => a.name.includes("function"));
        if (!extractAction)
            throw new Error("Extraction to function is not an available refactoring for the selected code range.");
        const edits = sourceFile.getLanguageService().getEditsForRefactor(statements[0].getStart(), statements[statements.length - 1].getEnd(), extractAction.name, { newName: newFunctionName });
        if (!edits)
            throw new Error("Could not get edits for extraction refactoring.");
        edits.applyChanges();
        await this.saveModifiedFiles();
        const newFunc = sourceFile.getFunction(newFunctionName);
        if (!newFunc)
            throw new Error("Failed to find the newly created function after extraction.");
        return { newFunctionCode: newFunc.getText() };
    }
    async organizeImports(filePath) {
        this.logger.debug(`TS Strategy: Organizing imports for ${filePath}`);
        const sourceFile = this.getSourceFileOrThrow(filePath);
        sourceFile.organizeImports();
        await this.saveModifiedFiles();
    }
    async applyCodeAction(filePath, startLine, endLine, actionName) {
        this.logger.debug(`TS Strategy: Applying code action '${actionName}' to ${filePath}`);
        const sourceFile = this.getSourceFileOrThrow(filePath);
        const startPos = sourceFile.compilerNode.getPositionOfLineAndCharacter(startLine - 1, 0);
        const endPos = sourceFile.compilerNode.getPositionOfLineAndCharacter(endLine, 0);
        const languageService = this.project.getLanguageService();
        const codeFixes = languageService.getCodeFixesAtPosition(sourceFile.getFilePath(), startPos, endPos, [], {}, {});
        const targetFix = codeFixes.find(fix => fix.getDescription() === actionName);
        if (!targetFix)
            throw new Error(`Code action '${actionName}' could not be found for the specified range.`);
        targetFix.getChanges().forEach(change => change.getSourceFile().applyTextChanges(change.getTextChanges()));
        await this.saveModifiedFiles();
    }
    resolvePath(relativePath) {
        return path.resolve(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', relativePath);
    }
    async saveModifiedFiles() {
        const dirtyFiles = this.project.getSourceFiles().filter(sf => sf.isModified());
        await this.project.save();
        return dirtyFiles.map(f => vscode.workspace.asRelativePath(f.getFilePath()));
    }
}
class VSCodeLSPStrategy {
    logger;
    constructor(logger) {
        this.logger = logger;
    }
    async getDoc(filePath) {
        const absPath = this.resolvePath(filePath);
        return await vscode.workspace.openTextDocument(absPath);
    }
    async renameSymbol(filePath, line, column, newName) {
        this.logger.debug(`LSP Strategy: Renaming symbol at ${filePath}:${line}:${column} to '${newName}'`);
        const doc = await this.getDoc(filePath);
        const position = new vscode.Position(line - 1, column - 1);
        const workspaceEdit = await vscode.commands.executeCommand('vscode.executeDocumentRenameProvider', doc.uri, position, newName);
        if (!workspaceEdit)
            throw new Error(`The active language extension for '${doc.languageId}' does not support renaming.`);
        await vscode.workspace.applyEdit(workspaceEdit);
        await vscode.workspace.saveAll();
        return Array.from(workspaceEdit.entries(), ([uri, _]) => vscode.workspace.asRelativePath(uri));
    }
    async extractToFunction(filePath, startLine, endLine, newFunctionName) {
        this.logger.debug(`LSP Strategy: Attempting to extract lines ${startLine}-${endLine} of ${filePath}`);
        const doc = await this.getDoc(filePath);
        const range = new vscode.Range(new vscode.Position(startLine - 1, 0), new vscode.Position(endLine, 0));
        const codeActions = await vscode.commands.executeCommand('vscode.executeCodeActionProvider', doc.uri, range);
        const extractAction = codeActions.find(action => action.kind?.value.includes('refactor.extract'));
        if (!extractAction || !extractAction.edit)
            throw new Error(`The language extension for '${doc.languageId}' does not provide an 'Extract to function' action for the selected range.`);
        await vscode.workspace.applyEdit(extractAction.edit);
        await vscode.workspace.saveAll();
        return { newFunctionCode: `// Function extracted successfully in ${filePath}. Review the file for the result.` };
    }
    async organizeImports(filePath) {
        this.logger.debug(`LSP Strategy: Organizing imports for ${filePath}`);
        const doc = await this.getDoc(filePath);
        const edit = await vscode.commands.executeCommand('_executeCodeActionProvider', doc.uri, new vscode.Range(0, 0, 0, 0), 'source.organizeImports');
        if (!edit)
            throw new Error(`The language extension for '${doc.languageId}' does not support organizing imports.`);
        await vscode.workspace.applyEdit(edit);
        await vscode.workspace.saveAll();
    }
    async applyCodeAction(filePath, startLine, endLine, actionName) {
        this.logger.debug(`LSP Strategy: Applying action '${actionName}' to ${filePath}`);
        const doc = await this.getDoc(filePath);
        const range = new vscode.Range(new vscode.Position(startLine - 1, 0), new vscode.Position(endLine, 0));
        const codeActions = await vscode.commands.executeCommand('vscode.executeCodeActionProvider', doc.uri, range);
        const targetAction = codeActions.find(a => a.title === actionName || a.kind?.value === actionName);
        if (!targetAction || !targetAction.edit)
            throw new Error(`Code action '${actionName}' not found or does not provide an edit.`);
        await vscode.workspace.applyEdit(targetAction.edit);
        await vscode.workspace.saveAll();
    }
    resolvePath(relativePath) {
        return path.resolve(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', relativePath);
    }
}
class LanguageServiceFactory {
    tsStrategy;
    lspStrategy;
    constructor(project, logger) {
        this.tsStrategy = new TypeScriptStrategy(project, logger);
        this.lspStrategy = new VSCodeLSPStrategy(logger);
    }
    async getStrategyForFile(filePath) {
        const uri = vscode.Uri.file(path.resolve(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', filePath));
        const doc = await vscode.workspace.openTextDocument(uri);
        switch (doc.languageId) {
            case 'typescript':
            case 'typescriptreact':
            case 'javascript':
            case 'javascriptreact':
                return this.tsStrategy;
            default:
                return this.lspStrategy;
        }
    }
}
// #endregion
class AdvancedCodeEditingTool extends toolFramework_1.AITerminalTool {
    name = 'AdvancedCodeEditing';
    description = 'Performs advanced, structural code editing, refactoring, and analysis using Abstract Syntax Trees (AST), AI, and workspace-wide operations for any programming language.';
    version = '3.0.0';
    category = 'Development';
    actions = {
        untangleAndRestructureFile: {
            description: "Fixes syntax, cleans up, and logically restructures a convoluted code file.",
            inputSchema: UntangleAndRestructureFileInputSchema,
            outputSchema: zod_1.z.object({ success: zod_1.z.boolean(), message: zod_1.z.string(), stepsTaken: zod_1.z.array(zod_1.z.string()), memoryId: zod_1.z.string() })
        },
        replaceLines: {
            description: 'Replaces a range of lines in a single file with new content. Use for simple text changes.',
            inputSchema: ReplaceLinesInputSchema,
            outputSchema: zod_1.z.object({ success: zod_1.z.boolean(), message: zod_1.z.string(), memoryId: zod_1.z.string() })
        },
        insertLines: {
            description: 'Inserts new content at a specific line in a single file. Use for simple text additions.',
            inputSchema: InsertLinesInputSchema,
            outputSchema: zod_1.z.object({ success: zod_1.z.boolean(), message: zod_1.z.string(), memoryId: zod_1.z.string() })
        },
        findAndReplace: {
            description: 'Performs a workspace-wide search and replace using text or regex patterns.',
            inputSchema: FindAndReplaceInputSchema,
            outputSchema: zod_1.z.object({ success: zod_1.z.boolean(), filesModified: zod_1.z.number(), totalReplacements: zod_1.z.number(), memoryId: zod_1.z.string() })
        },
        renameSymbol: {
            description: 'Safely renames a variable, function, class, or interface and all its references across the entire workspace for any language.',
            inputSchema: RenameSymbolInputSchema,
            outputSchema: zod_1.z.object({ success: zod_1.z.boolean(), filesModified: zod_1.z.array(zod_1.z.string()), memoryId: zod_1.z.string() })
        },
        extractToFunction: {
            description: 'Extracts a block of code into a new function, automatically determining parameters and return values (for supported languages).',
            inputSchema: ExtractToFunctionInputSchema,
            outputSchema: zod_1.z.object({ success: zod_1.z.boolean(), message: zod_1.z.string(), memoryId: zod_1.z.string() })
        },
        encapsulateField: {
            description: 'Makes a public class field private and creates getter and setter methods for it (TypeScript/JavaScript only).',
            inputSchema: EncapsulateFieldInputSchema,
            outputSchema: zod_1.z.object({ success: zod_1.z.boolean(), filesModified: zod_1.z.array(zod_1.z.string()), memoryId: zod_1.z.string() })
        },
        aiRefactor: {
            description: 'Uses an AI model to perform a complex refactoring based on a natural language instruction.',
            inputSchema: AIRefactorInputSchema,
            outputSchema: zod_1.z.object({ success: zod_1.z.boolean(), message: zod_1.z.string(), diff: zod_1.z.string(), memoryId: zod_1.z.string() })
        },
        generateCode: {
            description: 'Uses an AI model to generate a new piece of code (e.g., class, function, test case) from an instruction.',
            inputSchema: GenerateCodeInputSchema,
            outputSchema: zod_1.z.object({ success: zod_1.z.boolean(), generatedCode: zod_1.z.string(), memoryId: zod_1.z.string() })
        },
        moveAndRefactorImports: {
            description: 'Moves a file or directory and automatically updates all relative import statements across the workspace (TypeScript/JavaScript only).',
            inputSchema: MoveAndRefactorInputSchema,
            outputSchema: zod_1.z.object({ success: zod_1.z.boolean(), filesUpdated: zod_1.z.number(), memoryId: zod_1.z.string() })
        },
        generateCommitMessage: {
            description: 'Analyzes staged git changes and generates a descriptive, conventional commit message.',
            inputSchema: GenerateCommitMessageInputSchema,
            outputSchema: zod_1.z.object({ success: zod_1.z.boolean(), commitMessage: zod_1.z.string() })
        },
        manageNpmDependency: {
            description: 'Adds or removes an npm dependency, updating package.json and running the install command.',
            inputSchema: ManageNpmDependencyInputSchema,
            outputSchema: zod_1.z.object({ success: zod_1.z.boolean(), message: zod_1.z.string(), terminalOutput: zod_1.z.string() })
        },
    };
    logger;
    fileSystem;
    workspaceKnowledge;
    memoryOperations;
    languageServiceFactory;
    constructor(services) {
        super({ terminalSession: services.terminalSession, aiContext: services.aiContext, toolContext: services.toolContext });
        this.logger = services.logger;
        this.fileSystem = services.fileSystem;
        this.workspaceKnowledge = services.workspace;
        this.memoryOperations = services.memoryManager;
        const tsProject = new ts_morph_1.Project({ useInMemoryFileSystem: false });
        const workspaceRoot = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '.';
        tsProject.addSourceFilesAtPaths(`${workspaceRoot}/**/*.{ts,tsx,js,jsx}`);
        this.languageServiceFactory = new LanguageServiceFactory(tsProject, this.logger);
        this.logger.info(`[${this.name} v${this.version}] Tool initialized. Language-agnostic capabilities are active.`);
    }
    async invoke(actionName, input, options) {
        const executionId = (0, crypto_1.randomUUID)();
        const actionDef = this.actions[actionName];
        if (!actionDef) {
            const error = { message: `Action '${actionName}' not found.` };
            this.logger.error(error.message, { toolName: this.name });
            return { executionId, toolName: this.name, actionName, status: 'failure', error };
        }
        const memory = (0, toolFramework_1.createToolOperationMemory)(this.name, actionName, input);
        try {
            options.cancellationToken.onCancellationRequested(() => { throw new Error('Operation cancelled.'); });
            const permissions = this.getPermissions(actionName);
            if (permissions.length > 0) {
                const granted = await options.services.permissionManager.requestPermissions(permissions, `Action '${actionName}'`);
                if (!granted)
                    throw new Error(`Permissions denied: ${permissions.join(', ')}`);
            }
            const validatedInput = actionDef.inputSchema.parse(input);
            const { output, memoriesCreated } = await this._execute(actionName, validatedInput, options);
            const validatedOutput = actionDef.outputSchema.parse(output);
            const finalMemory = (0, toolFramework_1.updateToolOperationMemory)(memory, { success: true, output: validatedOutput });
            await this.memoryOperations.storeMemory(finalMemory);
            return {
                executionId,
                toolName: this.name,
                actionName,
                status: 'success',
                output: validatedOutput,
                memoriesCreated: [...(memoriesCreated || []), finalMemory.id]
            };
        }
        catch (error) {
            const errorDetails = error instanceof Error ? { message: error.message, stack: error.stack } : { message: String(error) };
            this.logger.error(`Action '${actionName}' failed.`, { executionId, error: errorDetails });
            const finalMemory = (0, toolFramework_1.updateToolOperationMemory)(memory, { success: false, error: errorDetails });
            await this.memoryOperations.storeMemory(finalMemory);
            return { executionId, toolName: this.name, actionName, status: 'failure', error: errorDetails };
        }
    }
    async _execute(actionName, validatedInput, options) {
        // The core logic now delegates to specific, fully-implemented methods.
        switch (actionName) {
            case 'untangleAndRestructureFile':
                return { output: await this.executeUntangleAndRestructureFile(validatedInput, options) };
            case 'replaceLines':
                return { output: await this.executeReplaceLines(validatedInput) };
            case 'insertLines':
                return { output: await this.executeInsertLines(validatedInput) };
            case 'findAndReplace':
                return { output: await this.executeFindAndReplace(validatedInput) };
            case 'renameSymbol':
                return { output: await this.executeRenameSymbol(validatedInput) };
            case 'extractToFunction':
                return { output: await this.executeExtractToFunction(validatedInput) };
            case 'encapsulateField':
                return { output: await this.executeEncapsulateField(validatedInput) };
            case 'aiRefactor':
                return { output: await this.executeAiRefactor(validatedInput, options) };
            case 'generateCode':
                return { output: await this.executeGenerateCode(validatedInput, options) };
            case 'moveAndRefactorImports':
                return { output: await this.executeMoveAndRefactor(validatedInput) };
            case 'generateCommitMessage':
                return { output: await this.executeGenerateCommitMessage(validatedInput) };
            case 'manageNpmDependency':
                return { output: await this.executeManageNpmDependency(validatedInput) };
            default:
                throw new Error(`Action '${actionName}' has no implementation.`);
        }
    }
    getPermissions(actionName) {
        // Permissions are defined based on the action's requirements on the system.
        if (['generateCommitMessage', 'manageNpmDependency', 'untangleAndRestructureFile'].includes(actionName)) {
            return ['workspace:read', 'workspace:write', 'shell:execute'];
        }
        if (['replaceLines', 'insertLines', 'findAndReplace', 'renameSymbol', 'extractToFunction', 'encapsulateField', 'aiRefactor', 'generateCode', 'moveAndRefactorImports'].includes(actionName)) {
            return ['workspace:read', 'workspace:write'];
        }
        return ['workspace:read'];
    }
    async executeAIOperation(context, input) {
        this.logger.info(`Executing generic AI operation for tool`, { promptLength: input.prompt.length });
        const prompt = [{ type: 'text', text: input.prompt }];
        return context.llm.generateResponse(prompt, {
            cancellationToken: new vscode.CancellationTokenSource().token,
            jsonOutput: false
        });
    }
    getDocumentation() {
        return `# Tool: ${this.name} v${this.version}
**Description:** ${this.description}
This tool performs advanced, language-aware code modifications. It automatically detects the programming language and uses the best available method (AST for TypeScript/JavaScript, VS Code's LSP for others) to ensure refactorings are safe and accurate.

## Actions
${Object.entries(this.actions).map(([name, def]) => this.getActionDocumentation(name, def)).join('\n')}`;
    }
    // #region --- Action Implementations (Delegating to Strategies) ---
    async executeUntangleAndRestructureFile(input, options) {
        const memory = (0, toolFramework_1.createToolOperationMemory)(this.name, 'untangleAndRestructureFile', input, { affectedFiles: [input.filePath], tags: ['cleanup', 'refactor', 'ai'] });
        const stepsTaken = [];
        let currentContent = await this.fileSystem.readFile(input.filePath);
        const originalContent = currentContent;
        // Step 1: Pre-emptive syntax cleaning for common "messy" code issues.
        this.logger.info(`Untangle Step 1: Sanitizing syntax for ${input.filePath}`);
        let sanitizedContent = this.sanitizeBrokenComments(currentContent);
        if (sanitizedContent !== currentContent) {
            stepsTaken.push("Sanitized broken multi-line comments.");
            currentContent = sanitizedContent;
        }
        // Step 2: Attempt to format using the language-specific strategy. This often fixes many small syntax issues.
        try {
            this.logger.info(`Untangle Step 2: Programmatic formatting via LSP for ${input.filePath}`);
            await this.fileSystem.writeFile(input.filePath, currentContent); // Write sanitized content to disk
            const strategy = await this.languageServiceFactory.getStrategyForFile(input.filePath);
            await strategy.organizeImports(input.filePath);
            await vscode.commands.executeCommand('editor.action.formatDocument', vscode.Uri.file(path.resolve(vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || '', input.filePath)));
            currentContent = await this.fileSystem.readFile(input.filePath);
            stepsTaken.push("Formatted document and organized imports using language server.");
        }
        catch (e) {
            const err = e;
            this.logger.warn(`Programmatic formatting failed for ${input.filePath}, proceeding with AI. Error: ${err.message}`);
            stepsTaken.push("Programmatic formatting failed, will rely on AI for cleanup.");
        }
        // Step 3 (Optional): Run an external linter if requested.
        if (input.runLinterFix) {
            try {
                this.logger.info(`Untangle Step 3: Running linter with --fix for ${input.filePath}`);
                const terminal = this.getTerminalSession();
                // This assumes standard linters are in package.json scripts. A more robust solution would detect the linter.
                await terminal.executeCommand(`npx eslint ${input.filePath} --fix`);
                currentContent = await this.fileSystem.readFile(input.filePath);
                stepsTaken.push("Executed linter with auto-fix.");
            }
            catch (e) {
                const err = e;
                this.logger.warn(`Linter command failed for ${input.filePath}: ${err.message}`);
                stepsTaken.push("Linter auto-fix command failed or was not configured.");
            }
        }
        // Step 4 (Optional): AI-driven logical restructuring.
        if (input.aiRestructure) {
            this.logger.info(`Untangle Step 4: Using AI to logically restructure ${input.filePath}`);
            const restructurePrompt = `You are an expert software architect. Your task is to logically restructure the following code file. Do not change the logic, but improve the organization.
            Common tasks include:
            - Grouping related functions or methods.
            - Ordering members within a class (e.g., public fields, private fields, constructor, public methods, private methods).
            - Moving helper functions closer to where they are used.
            - Ensuring a consistent and logical top-to-bottom flow in the file.
            
            IMPORTANT: Output only the complete, restructured code for the entire file. Do not add any explanations or markdown.
            
            Original File Content:
            ---
            ${currentContent}
            ---`;
            currentContent = await this.executeAIOperation(options.services.aiContext, { prompt: restructurePrompt });
            stepsTaken.push("Applied AI-driven logical code restructuring.");
        }
        await this.fileSystem.writeFile(input.filePath, currentContent);
        const finalMemory = (0, toolFramework_1.updateToolOperationMemory)(memory, { success: true, output: { stepsTaken } });
        await this.memoryOperations.storeMemory(finalMemory);
        return { success: true, message: `Successfully untangled and restructured ${input.filePath}.`, stepsTaken, memoryId: finalMemory.id };
    }
    async executeRenameSymbol(input) {
        const memory = (0, toolFramework_1.createToolOperationMemory)(this.name, 'renameSymbol', input, { tags: ['refactor', 'language-aware'] });
        const strategy = await this.languageServiceFactory.getStrategyForFile(input.filePath);
        const filesModified = await strategy.renameSymbol(input.filePath, input.line, input.column, input.newName);
        const finalMemory = (0, toolFramework_1.updateToolOperationMemory)(memory, { success: true, output: { filesModified } });
        await this.memoryOperations.storeMemory(finalMemory);
        return { success: true, filesModified, memoryId: finalMemory.id };
    }
    async executeExtractToFunction(input) {
        const memory = (0, toolFramework_1.createToolOperationMemory)(this.name, 'extractToFunction', input, { affectedFiles: [input.filePath], tags: ['refactor', 'language-aware'] });
        const strategy = await this.languageServiceFactory.getStrategyForFile(input.filePath);
        const { newFunctionCode } = await strategy.extractToFunction(input.filePath, input.startLine, input.endLine, input.newFunctionName);
        const finalMemory = (0, toolFramework_1.updateToolOperationMemory)(memory, { success: true, output: { newFunctionCode } });
        await this.memoryOperations.storeMemory(finalMemory);
        return { success: true, message: `Successfully extracted function in ${input.filePath}.`, memoryId: finalMemory.id };
    }
    async executeEncapsulateField(input) {
        const memory = (0, toolFramework_1.createToolOperationMemory)(this.name, 'encapsulateField', input, { affectedFiles: [input.filePath], tags: ['refactor', 'typescript'] });
        const strategy = await this.languageServiceFactory.getStrategyForFile(input.filePath);
        if (!(strategy instanceof TypeScriptStrategy)) {
            throw new Error("Encapsulate Field is currently only supported for TypeScript/JavaScript files.");
        }
        const sourceFile = strategy.getSourceFileOrThrow(input.filePath);
        const classNode = sourceFile.getClassOrThrow(input.className);
        const prop = classNode.getPropertyOrThrow(input.propertyName);
        if (prop.getScope() === ts_morph_1.Scope.Private) {
            throw new Error(`Property '${input.propertyName}' is already private.`);
        }
        prop.set({ scope: ts_morph_1.Scope.Private });
        const getter = classNode.addGetAccessor({
            name: input.propertyName,
            returnType: prop.getType().getText(),
            statements: `return this.${input.propertyName};`
        });
        const setter = classNode.addSetAccessor({
            name: input.propertyName,
            parameters: [{ name: 'value', type: prop.getType().getText() }],
            statements: `this.${input.propertyName} = value;`
        });
        const references = prop.findReferencesAsNodes();
        for (const ref of references) {
            const parent = ref.getParent();
            if (ts_morph_1.Node.isPropertyAccessExpression(parent) && ts_morph_1.Node.isBinaryExpression(parent.getParent()) && parent.getParent().getOperatorToken().getKind() === ts_morph_1.SyntaxKind.EqualsToken) {
                parent.getParent().replaceWithText(`this.${setter.getName()}(${parent.getParent().getRight().getText()})`);
            }
            else {
                ref.replaceWithText(`this.${getter.getName()}`);
            }
        }
        const filesModified = await strategy.saveModifiedFiles();
        const finalMemory = (0, toolFramework_1.updateToolOperationMemory)(memory, { success: true, output: { filesModified } });
        await this.memoryOperations.storeMemory(finalMemory);
        return { success: true, filesModified, memoryId: finalMemory.id };
    }
    async executeMoveAndRefactor(input) {
        const memory = (0, toolFramework_1.createToolOperationMemory)(this.name, 'moveAndRefactorImports', input, { tags: ['refactor', 'workspace', 'typescript'] });
        const strategy = await this.languageServiceFactory.getStrategyForFile(input.sourcePath);
        if (!(strategy instanceof TypeScriptStrategy)) {
            throw new Error("Move and Refactor Imports is currently only supported for TypeScript/JavaScript projects.");
        }
        const sourceFile = strategy.getSourceFileOrThrow(input.sourcePath);
        sourceFile.move(input.destinationPath);
        const modifiedFiles = await strategy.saveModifiedFiles();
        const filesUpdated = modifiedFiles.length;
        const finalMemory = (0, toolFramework_1.updateToolOperationMemory)(memory, { success: true, output: { filesUpdated } });
        await this.memoryOperations.storeMemory(finalMemory);
        return { success: true, filesUpdated, memoryId: finalMemory.id };
    }
    async executeReplaceLines(input) {
        const memory = (0, toolFramework_1.createToolOperationMemory)(this.name, 'replaceLines', input, { affectedFiles: [input.filePath] });
        const content = await this.fileSystem.readFile(input.filePath);
        const lines = content.split('\n');
        if (input.startLine > input.endLine || input.endLine > lines.length) {
            throw new Error('Invalid line range provided.');
        }
        if (input.createBackup)
            await this.fileSystem.writeFile(`${input.filePath}.bak`, content);
        lines.splice(input.startLine - 1, input.endLine - input.startLine + 1, ...input.newContent.split('\n'));
        await this.fileSystem.writeFile(input.filePath, lines.join('\n'));
        const finalMemory = (0, toolFramework_1.updateToolOperationMemory)(memory, { success: true });
        await this.memoryOperations.storeMemory(finalMemory);
        return { success: true, message: `Successfully replaced lines in ${input.filePath}.`, memoryId: finalMemory.id };
    }
    async executeInsertLines(input) {
        const memory = (0, toolFramework_1.createToolOperationMemory)(this.name, 'insertLines', input, { affectedFiles: [input.filePath] });
        const content = await this.fileSystem.readFile(input.filePath);
        const lines = content.split('\n');
        if (input.line > lines.length + 1 || input.line < 1) {
            throw new Error('Invalid line number for insertion.');
        }
        if (input.createBackup)
            await this.fileSystem.writeFile(`${input.filePath}.bak`, content);
        lines.splice(input.line - 1, 0, ...input.newContent.split('\n'));
        await this.fileSystem.writeFile(input.filePath, lines.join('\n'));
        const finalMemory = (0, toolFramework_1.updateToolOperationMemory)(memory, { success: true });
        await this.memoryOperations.storeMemory(finalMemory);
        return { success: true, message: `Successfully inserted content in ${input.filePath}.`, memoryId: finalMemory.id };
    }
    async executeFindAndReplace(input) {
        const memory = (0, toolFramework_1.createToolOperationMemory)(this.name, 'findAndReplace', input, { tags: ['bulk', 'workspace'] });
        const files = await this.workspaceKnowledge.findFilesByPattern(input.globPattern);
        let filesModified = 0;
        let totalReplacements = 0;
        for (const file of files) {
            const filePath = vscode.workspace.asRelativePath(file);
            const replacements = await this.fileSystem.replaceInFile(filePath, input.isRegex ? new RegExp(input.findPattern, 'g') : input.findPattern, input.replacePattern);
            if (replacements > 0) {
                filesModified++;
                totalReplacements += replacements;
            }
        }
        const output = { filesModified, totalReplacements };
        const finalMemory = (0, toolFramework_1.updateToolOperationMemory)(memory, { success: true, output });
        await this.memoryOperations.storeMemory(finalMemory);
        return { success: true, ...output, memoryId: finalMemory.id };
    }
    async executeAiRefactor(input, options) {
        const memory = (0, toolFramework_1.createToolOperationMemory)(this.name, 'aiRefactor', input, { affectedFiles: [input.filePath], tags: ['ai', 'refactor'] });
        const originalContent = await this.fileSystem.readFile(input.filePath);
        const codeToRefactor = this.extractCodeBlock(originalContent, input.startLine, input.endLine);
        const prompt = `You are an expert AI software engineer. Your task is to refactor a piece of code based on a specific instruction.
        File Path: ${input.filePath}
        Instruction: "${input.refactorInstruction}"

        IMPORTANT: You must only output the new, refactored code for the provided snippet. Do NOT include explanations, markdown, or any other text.

        Original Code Snippet:
        ---
        ${codeToRefactor}
        ---`;
        const refactoredSnippet = await this.executeAIOperation(options.services.aiContext, { prompt });
        const newContent = this.replaceCodeBlock(originalContent, refactoredSnippet, input.startLine, input.endLine);
        await this.fileSystem.writeFile(input.filePath, newContent, { backup: true });
        const diff = await this.fileSystem.diffContent(originalContent, newContent, { format: toolFramework_1.DiffFormat.Unified });
        const finalMemory = (0, toolFramework_1.updateToolOperationMemory)(memory, { success: true, output: { diff } });
        await this.memoryOperations.storeMemory(finalMemory);
        return { success: true, message: 'AI refactoring complete.', diff, memoryId: finalMemory.id };
    }
    async executeGenerateCode(input, options) {
        const memory = (0, toolFramework_1.createToolOperationMemory)(this.name, 'generateCode', input, { affectedFiles: [input.filePath], tags: ['ai', 'generation'] });
        const prompt = `You are an expert AI software engineer. Generate a piece of code based on the following instruction.
        Instruction: "${input.generationInstruction}"

        IMPORTANT: You must only output the raw code. Do NOT include explanations, markdown, or any other text.
        `;
        const generatedCode = await this.executeAIOperation(options.services.aiContext, { prompt });
        await this.executeInsertLines({
            filePath: input.filePath,
            line: input.line || (await this.fileSystem.readFile(input.filePath)).split('\n').length + 1,
            newContent: generatedCode,
            createBackup: false, // Don't backup for a new code insertion
        });
        const finalMemory = (0, toolFramework_1.updateToolOperationMemory)(memory, { success: true, output: { generatedCode } });
        await this.memoryOperations.storeMemory(finalMemory);
        return { success: true, generatedCode, memoryId: finalMemory.id };
    }
    async executeGenerateCommitMessage(input) {
        const terminal = this.getTerminalSession();
        await terminal.executeCommand('git add -A');
        const diff = await terminal.executeCommand('git diff --staged');
        if (!diff.trim())
            return { success: true, commitMessage: 'No changes to commit.' };
        const prompt = `Based on the following git diff, generate a concise commit message.
        ${input.conventionalCommit ? 'Use the Conventional Commits specification (e.g., feat(api): add new endpoint). ' : ''}
        Diff:\n${diff}`;
        const commitMessage = await this.executeAIOperation(this.getAIContext(), { prompt });
        return { success: true, commitMessage };
    }
    async executeManageNpmDependency(input) {
        const terminal = this.getTerminalSession();
        const cmd = `npm ${input.action === 'add' ? 'install' : 'uninstall'} ${input.packageName}${input.isDevDependency ? ' --save-dev' : ''}`;
        const output = await terminal.executeCommand(cmd);
        return { success: true, message: `Successfully executed: ${cmd}`, terminalOutput: output };
    }
    // #endregion
    // #region --- Private Helper Methods ---
    getActionDocumentation(name, def) {
        const inputSchema = def.inputSchema;
        const props = Object.entries(inputSchema.shape).map(([key, val]) => {
            const schema = val;
            return `        - \`${key}\`: \`${schema._def.typeName}\` - ${schema.description || 'No description.'}`;
        }).join('\n');
        return `\n### \`${name}\`\n**Description:** ${def.description}\n**Inputs:**\n${props}`;
    }
    extractCodeBlock(content, startLine, endLine) {
        if (!startLine || !endLine)
            return content;
        return content.split('\n').slice(startLine - 1, endLine).join('\n');
    }
    replaceCodeBlock(originalContent, newSnippet, startLine, endLine) {
        if (!startLine || !endLine)
            return newSnippet; // Replace whole file if no range
        const lines = originalContent.split('\n');
        lines.splice(startLine - 1, endLine - startLine + 1, ...newSnippet.split('\n'));
        return lines.join('\n');
    }
    sanitizeBrokenComments(code) {
        let inComment = false;
        let depth = 0;
        const sanitizedChars = [];
        for (let i = 0; i < code.length; i++) {
            if (code[i] === '/' && code[i + 1] === '*') {
                if (!inComment) {
                    sanitizedChars.push('/', '*');
                    inComment = true;
                    depth = 1;
                    i++;
                }
                else {
                    depth++;
                    // This is a nested /*, we will escape it to prevent syntax break
                    sanitizedChars.push('/', '\\*');
                    i++;
                }
            }
            else if (code[i] === '*' && code[i + 1] === '/') {
                if (inComment) {
                    depth--;
                    if (depth === 0) {
                        sanitizedChars.push('*', '/');
                        inComment = false;
                    }
                    else {
                        // This is a premature */, escape it
                        sanitizedChars.push('\\*', '/');
                    }
                    i++;
                }
                else {
                    // This is a */ without a preceding /*, escape it
                    sanitizedChars.push('\\*', '/');
                    i++;
                }
            }
            else {
                sanitizedChars.push(code[i]);
            }
        }
        return sanitizedChars.join('');
    }
}
exports.AdvancedCodeEditingTool = AdvancedCodeEditingTool;
//# sourceMappingURL=codeEditingTool.js.map