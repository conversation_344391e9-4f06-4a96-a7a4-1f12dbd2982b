{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/managers/index.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;AAwJH,0CAEC;AAKD,0CAEC;AAKD,wDAEC;AAnKD,sCAAsC;AACtC,4CAA4C;AAC5C,4DAA0C;AAC1C,4DAA0C;AAC1C,uEAAqD;AACrD,6DAA2C;AAE3C,iCAAiC;AACjC,+BAA+B;AAC/B,gEAA8C;AAC9C,wEAAsD;AAEtD,wEAAwE;AACxE,iFAakD;AAZhD,qIAAA,6BAA6B,OAAA;AAC7B,+HAAA,uBAAuB,OAAA;AACvB,yHAAA,iBAAiB,OAAA;AACjB,0HAAA,kBAAkB,OAAA;AAClB,6HAAA,qBAAqB,OAAA;AACrB,6HAAA,qBAAqB,OAAA;AACrB,8HAAA,sBAAsB,OAAA;AACtB,4HAAA,oBAAoB,OAAA;AACpB,mIAAA,mBAAmB,OAA+B;AAClD,gIAAA,gBAAgB,OAA4B;AAC5C,mIAAA,mBAAmB,OAA+B;AAClD,uHAAA,eAAe,OAAA;AAGjB,kCAAkC;AAClC,eAAe;AACf,+DAGyC;AAFvC,uHAAA,wBAAwB,OAAA;AACxB,qHAAA,sBAAsB,OAAA;AAGxB,sBAAsB;AACtB,6EAAkF;AAAzE,8HAAA,wBAAwB,OAAA;AACjC,4CAAkD;AAAzC,+GAAA,iBAAiB,OAAA;AAG1B,eAAe;AACf,+DAAoE;AAA3D,gHAAA,iBAAiB,OAAA;AAC1B,8BAAoC;AAA3B,iGAAA,UAAU,OAAA;AAGnB,iCAAiC;AACjC,+EAGiD;AAF/C,uIAAA,gCAAgC,OAAA;AAChC,kIAAA,2BAA2B,OAAA;AAG7B,iBAAiB;AACjB,mEAQ2C;AAPzC,uHAAA,sBAAsB,OAAA;AACtB,2HAAA,0BAA0B,OAAA;AAC1B,qHAAA,oBAAoB,OAAA;AACpB,6HAAA,4BAA4B,OAAA;AAC5B,sHAAA,qBAAqB,OAAA;AACrB,yHAAA,wBAAwB,OAAA;AACxB,0HAAA,yBAAyB,OAAA;AAG3B,wBAAwB;AACxB,iFAMkD;AALhD,2HAAA,mBAAmB,OAAA;AACnB,2HAAA,mBAAmB,OAAA;AACnB,wHAAA,gBAAgB,OAAA;AAChB,+HAAA,uBAAuB,OAAA;AACvB,uIAAA,oBAAoB,OAAmC;AAGzD,qBAAqB;AACrB,yEAG8C;AAF5C,0HAAA,sBAAsB,OAAA;AACtB,kIAAA,8BAA8B,OAAA;AAGhC,kBAAkB;AAClB,iEAG0C;AAFxC,kHAAA,kBAAkB,OAAA;AAClB,yHAAA,yBAAyB,OAAA;AAG3B,0BAA0B;AAC1B,mFAGmD;AAFjD,oIAAA,2BAA2B,OAAA;AAC3B,uIAAA,8BAA8B,OAAA;AAGhC,2BAA2B;AAC3B,mFAGmD;AAFjD,6IAAA,2BAA2B,OAAwC;AACnE,8IAAA,qCAAqC,OAAA;AAGvC,gBAAgB;AAChB,mEAAiD;AAEjD,wBAAwB;AACxB,6EAGgD;AAF9C,8HAAA,wBAAwB,OAAA;AACxB,yHAAA,mBAAmB,OAAA;AAGrB,4BAA4B;AAC5B,qFAEoD;AADlD,sIAAA,4BAA4B,OAAA;AAG9B,gCAAgC;AAChC,6FAGwD;AAFtD,mJAAA,gCAAgC,OAAyC;AACzE,uJAAA,yCAAyC,OAAA;AAG3C,2BAA2B;AAC3B,mFAGmD;AAFjD,oIAAA,2BAA2B,OAAA;AAC3B,yIAAA,gCAAgC,OAAA;AAGlC,6BAA6B;AAC7B,2EAG+C;AAF7C,4HAAA,uBAAuB,OAAA;AACvB,sIAAA,iCAAiC,OAAA;AAGnC,iCAAiC;AACjC,+FAGyD;AAFvD,gJAAA,iCAAiC,OAAA;AACjC,4IAAA,6BAA6B,OAAA;AAG/B,2CAA2C;AAC3C,yCAAyC;AACzC,qEAAqE;AACrE,mEAAiD;AAEjD,yCAAyC;AACzC,mEAAiD;AAEjD,+BAA+B;AAC/B,2EAAwE;AAExE;;GAEG;AACH,SAAgB,eAAe;IAC7B,OAAO,mCAAgB,CAAC,eAAe,EAAE,CAAC;AAC5C,CAAC;AAED;;GAEG;AACH,SAAgB,eAAe,CAAC,EAAU;IACxC,OAAO,mCAAgB,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AAC1C,CAAC;AAED;;GAEG;AACH,SAAgB,sBAAsB,CAAC,EAAU;IAC/C,OAAO,mCAAgB,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC;AACrD,CAAC", "sourcesContent": ["/**\n * Export all manager interfaces, workflows, and shared utilities for easy access\n */\n\n// ===== Manager Interfaces =====\nexport { IWorkflowManager, IMCPManager, IPromptManager, IKnowledgebaseManager } from '../types/managers';\n\n// ===== Workflow Types and Core =====\n// Export types and core workflow components\nexport * from '../agents/workflows/types';\nexport * from '../agents/workflows/graph';\nexport * from '../agents/workflows/workflowRegistry';\nexport * from '../agents/workflows/memory';\n\n// ===== Workflow Templates =====\n// Basic and advanced templates\nexport * from '../agents/workflows/templates';\nexport * from '../agents/workflows/advancedTemplates';\n\n// Specialized templates with explicit exports to avoid naming conflicts\nexport {\n  createCodeRefactoringWorkflow,\n  createDebuggingWorkflow,\n  createAskWorkflow,\n  createEditWorkflow,\n  createCodeGenWorkflow,\n  createAgenticWorkflow,\n  createUXDesignWorkflow,\n  createDevOpsWorkflow,\n  createAgileWorkflow as createAgileTemplateWorkflow,\n  createXPWorkflow as createXPTemplateWorkflow,\n  createScrumWorkflow as createScrumTemplateWorkflow,\n  WorkflowFactory\n} from '../agents/workflows/specializedTemplates';\n\n// ===== Workflow Categories =====\n// PR Workflows\nexport {\n  createPRCreationWorkflow,\n  createPRReviewWorkflow\n} from '../agents/workflows/prWorkflows';\n\n// Checkpoint Workflow\nexport { createCheckpointWorkflow } from '../agents/workflows/checkpointWorkflow';\nexport { checkpointManager } from '../checkpoint';\nexport type { CheckpointManager, Checkpoint } from '../checkpoint';\n\n// MCP Workflow\nexport { createMCPWorkflow } from '../agents/workflows/mcpWorkflow';\nexport { mcpManager } from '../mcp';\nexport type { MCPManager, MCPContext } from '../mcp';\n\n// Advanced Refactoring Workflows\nexport {\n  createPatternRefactoringWorkflow,\n  createTechnicalDebtWorkflow\n} from '../agents/workflows/advancedRefactoring';\n\n// SDLC Workflows\nexport {\n  createPlanningWorkflow,\n  createRequirementsWorkflow,\n  createDesignWorkflow,\n  createImplementationWorkflow,\n  createTestingWorkflow,\n  createDeploymentWorkflow,\n  createMaintenanceWorkflow\n} from '../agents/workflows/sdlcWorkflows';\n\n// Methodology Workflows\nexport {\n  createAgileWorkflow,\n  createScrumWorkflow,\n  createXPWorkflow,\n  createWaterfallWorkflow,\n  createDevOpsWorkflow as createDevOpsMethodologyWorkflow\n} from '../agents/workflows/methodologyWorkflows';\n\n// Research Workflows\nexport {\n  createResearchWorkflow,\n  createAcademicResearchWorkflow\n} from '../agents/workflows/researchWorkflow';\n\n// UI/UX Workflows\nexport {\n  createUIUXWorkflow,\n  createUIComponentWorkflow\n} from '../agents/workflows/uiUxWorkflow';\n\n// Documentation Workflows\nexport {\n  createDocumentationWorkflow,\n  createAPIDocumentationWorkflow\n} from '../agents/workflows/documentationWorkflow';\n\n// Technical Debt Workflows\nexport {\n  createTechnicalDebtWorkflow as createTechnicalDebtReductionWorkflow,\n  createLegacyCodeModernizationWorkflow\n} from '../agents/workflows/technicalDebtWorkflow';\n\n// Vector Stores\nexport * from '../agents/workflows/vectorStores';\n\n// Document QA Workflows\nexport {\n  createDocumentQAWorkflow,\n  createPDFQAWorkflow\n} from '../agents/workflows/documentQAWorkflow';\n\n// Memory-Enhanced Workflows\nexport {\n  createMemoryEnhancedWorkflow\n} from '../agents/workflows/memoryEnhancedWorkflow';\n\n// Pattern-Refactoring Workflows\nexport {\n  createPatternRefactoringWorkflow as createPatternBasedRefactoringWorkflow,\n  createDesignPatternImplementationWorkflow\n} from '../agents/workflows/patternRefactoringWorkflow';\n\n// Knowledge Base Workflows\nexport {\n  createKnowledgeBaseWorkflow,\n  createKnowledgeRetrievalWorkflow\n} from '../agents/workflows/knowledgeBaseWorkflow';\n\n// Multi-Repository Workflows\nexport {\n  createMultiRepoWorkflow,\n  createCrossRepoDependencyWorkflow\n} from '../agents/workflows/multiRepoWorkflow';\n\n// Collaborative Coding Workflows\nexport {\n  createCollaborativeCodingWorkflow,\n  createPairProgrammingWorkflow\n} from '../agents/workflows/collaborativeCodingWorkflow';\n\n// ===== Core Polyfills and Utilities =====\n// Re-export core polyfills for langchain\n// This provides compatibility shims for various langchain components\nexport * from '../agents/workflows/corePolyfill';\n\n// Re-export codessa graph implementation\nexport * from '../agents/workflows/codessaGraph';\n\n// ===== Helper Functions =====\nimport { workflowRegistry } from '../agents/workflows/workflowRegistry';\n\n/**\n * Get all registered workflows\n */\nexport function getAllWorkflows() {\n  return workflowRegistry.getAllWorkflows();\n}\n\n/**\n * Get a workflow by ID\n */\nexport function getWorkflowById(id: string) {\n  return workflowRegistry.getWorkflow(id);\n}\n\n/**\n * Create a workflow instance\n */\nexport function createWorkflowInstance(id: string) {\n  return workflowRegistry.createWorkflowInstance(id);\n}"]}