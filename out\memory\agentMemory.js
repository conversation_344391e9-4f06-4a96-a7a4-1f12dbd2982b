"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentMemory = void 0;
exports.getAgentMemory = getAgentMemory;
const logger_1 = require("../logger");
const memoryManager_1 = require("./memoryManager");
const config_1 = require("../config");
/**
 * Agent memory integration
 */
class AgentMemory {
    agentRef;
    conversationHistory = [];
    relevantMemories = [];
    constructor(agent) {
        this.agentRef = new WeakRef(agent);
    }
    get agent() {
        return this.agentRef.deref();
    }
    /**
       * Add a memory entry to the agent's memory
       */
    async addMemory(entry) {
        try {
            const agent = this.agent;
            if (!agent) {
                throw new Error('Agent reference is no longer available');
            }
            // Create memory entry with agent context
            const memory = await memoryManager_1.memoryManager.addMemory({
                content: entry.content,
                metadata: {
                    ...entry.metadata,
                    agentId: agent.id,
                    agentName: agent.name
                }
            });
            logger_1.logger.debug(`Added memory to agent: ${entry.content.substring(0, 50)}...`);
            return memory;
        }
        catch (error) {
            logger_1.logger.error('Failed to add memory to agent:', error);
            throw error;
        }
    }
    /**
       * Add a message to the agent's memory
       */
    async addMessage(role, content) {
        try {
            const agent = this.agent;
            if (!agent) {
                logger_1.logger.warn('Cannot add message: Agent reference is no longer available');
                return;
            }
            // Create memory entry
            const memory = await memoryManager_1.memoryManager.addMemory({
                content,
                metadata: {
                    source: 'conversation',
                    type: role === 'user' ? 'human' : 'ai',
                    agentId: agent.id,
                    agentName: agent.name
                }
            });
            // Add to conversation history
            this.conversationHistory.push(memory);
            // Trim conversation history if needed
            const maxHistorySize = (0, config_1.getConfig)('memory.conversationHistorySize', 100);
            if (this.conversationHistory.length > maxHistorySize) {
                this.conversationHistory = this.conversationHistory.slice(-maxHistorySize);
            }
            logger_1.logger.debug(`Added ${role} message to agent memory: ${content.substring(0, 50)}...`);
        }
        catch (error) {
            logger_1.logger.error('Failed to add message to agent memory:', error);
        }
    }
    /**
       * Get conversation history
       */
    getConversationHistory() {
        return [...this.conversationHistory];
    }
    /**
       * Get relevant memories for a query
       */
    async getRelevantMemories(query) {
        try {
            const agent = this.agent;
            if (!agent) {
                logger_1.logger.warn('Cannot get relevant memories: Agent reference is no longer available');
                return [];
            }
            // Check if memory is enabled
            const memoryEnabled = (0, config_1.getConfig)('memory.enabled', true);
            if (!memoryEnabled) {
                return [];
            }
            // Search for relevant memories
            this.relevantMemories = await memoryManager_1.memoryManager.searchSimilarMemories(query, {
                limit: (0, config_1.getConfig)('memory.contextWindowSize', 5),
                filter: {
                    source: 'conversation',
                    agentId: agent.id
                }
            });
            return this.relevantMemories;
        }
        catch (error) {
            logger_1.logger.error('Failed to get relevant memories:', error);
            return [];
        }
    }
    /**
       * Clear conversation history
       */
    clearConversationHistory() {
        this.conversationHistory = [];
    }
    /**
       * Format memories for inclusion in prompts
       */
    formatMemoriesForPrompt(memories) {
        if (memories.length === 0) {
            return '';
        }
        const formattedMemories = memories.map(memory => {
            const role = memory.metadata.type === 'human' ? 'User' : 'Assistant';
            return `${role}: ${memory.content}`;
        }).join('\n\n');
        return `\n\nRelevant conversation history:\n${formattedMemories}\n\n`;
    }
    /**
       * Get a summary of the agent's memory
       */
    async getMemorySummary() {
        try {
            const agent = this.agent;
            if (!agent) {
                logger_1.logger.warn('Cannot get memory summary: Agent reference is no longer available');
                return 'Agent reference is no longer available';
            }
            // Get all memories for this agent
            const memories = await memoryManager_1.memoryManager.searchMemories({
                query: '',
                limit: 1000,
                filter: {
                    agentId: agent.id
                }
            });
            if (memories.length === 0) {
                return 'No memories available.';
            }
            // Count by type
            const typeCounts = {};
            for (const memory of memories) {
                const type = memory.metadata.type;
                typeCounts[type] = (typeCounts[type] || 0) + 1;
            }
            // Format summary
            const summary = [
                `Total memories: ${memories.length}`,
                'Memory types:',
                ...Object.entries(typeCounts).map(([type, count]) => `- ${type}: ${count}`)
            ].join('\n');
            return summary;
        }
        catch (error) {
            logger_1.logger.error('Failed to get memory summary:', error);
            return 'Failed to get memory summary.';
        }
    }
}
exports.AgentMemory = AgentMemory;
/**
 * Get agent memory for an agent
 */
const agentMemories = new WeakMap();
function getAgentMemory(agent) {
    if (!agent) {
        throw new Error('Agent cannot be null or undefined');
    }
    if (!agentMemories.has(agent)) {
        agentMemories.set(agent, new AgentMemory(agent));
    }
    const memory = agentMemories.get(agent);
    if (!memory) {
        throw new Error('Failed to create or retrieve agent memory');
    }
    return memory;
}
//# sourceMappingURL=agentMemory.js.map