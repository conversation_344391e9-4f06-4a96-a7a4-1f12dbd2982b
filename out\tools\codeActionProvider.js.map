{"version": 3, "file": "codeActionProvider.js", "sourceRoot": "", "sources": ["../../src/tools/codeActionProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAqDjC,wFAAwF;AACxF,iDAAiD;AAEjD,2DAA2D;AAC3D,MAAa,0BAA0B;IACnC,0DAA0D;IACvC,iBAAiB,GAAsB,EAAE,CAAC;IAC1C,aAAa,GAA8B,IAAI,GAAG,EAAE,CAAC;IACrD,cAAc,GAAuB,EAAE,CAAC;IACxC,cAAc,GAAmC,IAAI,GAAG,EAAE,CAAC;IAC7D,iBAAiB,GAAsB,EAAE,CAAC;IAE3D;QACI,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACnC,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IAED,8FAA8F;IACtF,2BAA2B;QAC/B,0BAA0B;QAC1B,IAAI,CAAC,WAAW,CAAC;YACb,OAAO,EAAE,qCAAqC;YAC9C,MAAM,EAAE,yBAAyB;YACjC,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,UAAU;YACpB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACzB,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,4BAA4B,OAAO,EAAE,EACrC,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,wBAAwB;oBACjC,KAAK,EAAE,WAAW,OAAO,EAAE;oBAC3B,SAAS,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;iBACnC,CAAC;gBACF,MAAM,CAAC,WAAW,GAAG,CAAC;wBAClB,KAAK;wBACL,OAAO,EAAE,YAAY,OAAO,aAAa;wBACzC,QAAQ,EAAE,MAAM,CAAC,kBAAkB,CAAC,KAAK;wBACzC,MAAM,EAAE,UAAU;qBACrB,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,wBAAwB;QACxB,IAAI,CAAC,WAAW,CAAC;YACb,OAAO,EAAE,qCAAqC;YAC9C,MAAM,EAAE,YAAY;YACpB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,SAAS;YACnB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC5B,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACpC,MAAM,eAAe,GAAG,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;oBAClD,SAAS,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,eAAe,UAAU,KAAK,CAAC,CAAC;oBACpE,eAAe,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,UAAU,UAAU,IAAI,CAAC;gBAEvE,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,2BAA2B,UAAU,EAAE,EACvC,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;gBACzC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;gBACrD,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,uBAAuB;oBAChC,KAAK,EAAE,WAAW,UAAU,EAAE;oBAC9B,SAAS,EAAE,CAAC,UAAU,EAAE,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC;iBACjD,CAAC;gBACF,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,4BAA4B;QAC5B,IAAI,CAAC,WAAW,CAAC;YACb,OAAO,EAAE,oDAAoD;YAC7D,MAAM,EAAE,cAAc;YACtB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,YAAY;YACtB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC1B,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,iBAAiB,QAAQ,gBAAgB,EACzC,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,qBAAqB;oBAC9B,KAAK,EAAE,gBAAgB,QAAQ,EAAE;oBACjC,SAAS,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC;iBACpC,CAAC;gBACF,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,wBAAwB;QACxB,IAAI,CAAC,WAAW,CAAC;YACb,OAAO,EAAE,yBAAyB;YAClC,MAAM,EAAE,iBAAiB;YACzB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,QAAQ;YAClB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAC1B,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,oBAAoB,QAAQ,EAAE,EAC9B,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;gBACzC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,EAChE,SAAS,QAAQ,QAAQ,CAAC,CAAC;gBAC/B,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,kCAAkC;QAClC,IAAI,CAAC,WAAW,CAAC;YACb,OAAO,EAAE,4CAA4C;YACrD,MAAM,EAAE,cAAc;YACtB,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,UAAU;YACpB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACvB,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,uBAAuB,KAAK,EAAE,EAC9B,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,qBAAqB;oBAC9B,KAAK,EAAE,OAAO,KAAK,EAAE;oBACrB,SAAS,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC;iBACjC,CAAC;gBACF,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,wBAAwB;QACxB,IAAI,CAAC,WAAW,CAAC;YACb,OAAO,EAAE,qDAAqD;YAC9D,MAAM,EAAE,sBAAsB;YAC9B,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,QAAQ;YAClB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtB,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,yBAAyB,IAAI,EAAE,EAC/B,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,IAAI,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;gBACzC,wEAAwE;gBACxE,MAAM,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC;gBACtF,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,cAAc,EAAE,UAAU,IAAI,IAAI,CAAC,CAAC;gBAChE,MAAM,CAAC,WAAW,GAAG,CAAC;wBAClB,KAAK;wBACL,OAAO,EAAE,SAAS,IAAI,kBAAkB;wBACxC,QAAQ,EAAE,MAAM,CAAC,kBAAkB,CAAC,KAAK;wBACzC,MAAM,EAAE,QAAQ;qBACnB,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAI,CAAC,WAAW,CAAC;YACb,OAAO,EAAE,sCAAsC;YAC/C,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,KAAK;YACf,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,qBAAqB;gBACpD,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,2BAA2B,EAC3B,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,qBAAqB;oBAC9B,KAAK,EAAE,2BAA2B;oBAClC,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC9B,CAAC;gBACF,MAAM,CAAC,WAAW,GAAG,CAAC;wBAClB,KAAK;wBACL,OAAO,EAAE,YAAY;wBACrB,QAAQ,EAAE,MAAM,CAAC,kBAAkB,CAAC,KAAK;wBACzC,MAAM,EAAE,KAAK;qBAChB,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,wBAAwB;QACxB,IAAI,CAAC,WAAW,CAAC;YACb,OAAO,EAAE,gCAAgC;YACzC,MAAM,EAAE,gBAAgB;YACxB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,QAAQ;YAClB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,gDAAgD;gBAChF,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,4BAA4B,aAAa,GAAG,EAC5C,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,uBAAuB;oBAChC,KAAK,EAAE,gBAAgB;oBACvB,SAAS,EAAE,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC7C,CAAC;gBACF,MAAM,CAAC,WAAW,GAAG,CAAC;wBAClB,KAAK;wBACL,OAAO,EAAE,mBAAmB,aAAa,aAAa;wBACtD,QAAQ,EAAE,MAAM,CAAC,kBAAkB,CAAC,KAAK;wBACzC,MAAM,EAAE,QAAQ;qBACnB,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,yBAAyB;QACzB,IAAI,CAAC,WAAW,CAAC;YACb,OAAO,EAAE,mCAAmC;YAC5C,MAAM,EAAE,eAAe;YACvB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,SAAS;YACnB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,6DAA6D;gBAC5F,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,sBAAsB,YAAY,EAAE,EACpC,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,sBAAsB;oBAC/B,KAAK,EAAE,0BAA0B;oBACjC,SAAS,EAAE,CAAC,YAAY,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC;iBAC5C,CAAC;gBACF,MAAM,CAAC,WAAW,GAAG,CAAC;wBAClB,KAAK;wBACL,OAAO,EAAE,kBAAkB,YAAY,EAAE;wBACzC,QAAQ,EAAE,MAAM,CAAC,kBAAkB,CAAC,KAAK;wBACzC,MAAM,EAAE,SAAS;qBACpB,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,iCAAiC;QACjC,IAAI,CAAC,WAAW,CAAC;YACb,OAAO,EAAE,6BAA6B;YACtC,MAAM,EAAE,iBAAiB;YACzB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,aAAa;YACvB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,eAAe,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,kEAAkE;gBACpG,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,yBAAyB,eAAe,EAAE,EAC1C,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,wBAAwB;oBACjC,KAAK,EAAE,sBAAsB;oBAC7B,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC;iBAC/C,CAAC;gBACF,MAAM,CAAC,WAAW,GAAG,CAAC;wBAClB,KAAK;wBACL,OAAO,EAAE,qBAAqB,eAAe,EAAE;wBAC/C,QAAQ,EAAE,MAAM,CAAC,kBAAkB,CAAC,KAAK;wBACzC,MAAM,EAAE,aAAa;qBACxB,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,wBAAwB;QACxB,IAAI,CAAC,WAAW,CAAC;YACb,OAAO,EAAE,iCAAiC;YAC1C,MAAM,EAAE,YAAY;YACpB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,QAAQ;YAClB,GAAG,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE;gBACvB,MAAM,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,kEAAkE;gBAChG,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,qBAAqB,WAAW,EAAE,EAClC,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,mBAAmB;oBAC5B,KAAK,EAAE,kBAAkB;oBACzB,SAAS,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,WAAW,CAAC;iBACvC,CAAC;gBACF,MAAM,CAAC,WAAW,GAAG,CAAC;wBAClB,KAAK;wBACL,OAAO,EAAE,iBAAiB,WAAW,EAAE;wBACvC,QAAQ,EAAE,MAAM,CAAC,kBAAkB,CAAC,KAAK;wBACzC,MAAM,EAAE,QAAQ;qBACnB,CAAC,CAAC;gBACH,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;IACP,CAAC;IAEO,uBAAuB;QAC3B,uBAAuB;QACvB,MAAM,MAAM,GAAG;YACX,KAAK,EAAE;gBACH,KAAK,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC;gBAC/C,IAAI,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,iBAAiB,CAAC;aACjD;YACD,OAAO,EAAE;gBACL,KAAK,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,0BAA0B,CAAC;gBACxD,IAAI,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,0BAA0B,CAAC;aAC1D;YACD,IAAI,EAAE;gBACF,KAAK,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,uBAAuB,CAAC;gBACrD,IAAI,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,uBAAuB,CAAC;aACvD;YACD,OAAO,EAAE;gBACL,KAAK,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,oBAAoB,CAAC;gBAClD,IAAI,EAAE,IAAI,MAAM,CAAC,UAAU,CAAC,oBAAoB,CAAC;aACpD;SACJ,CAAC;QAEF,wDAAwD;QACxD,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,WAAW;YACjB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAC1D,QAAQ,EAAE,YAAY;YACtB,WAAW,EAAE,sCAAsC;YACnD,KAAK,EAAE,MAAM,CAAC,IAAI;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YACvD,QAAQ,EAAE,OAAO;YACjB,WAAW,EAAE,gDAAgD;YAC7D,KAAK,EAAE,MAAM,CAAC,KAAK;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YAC3D,QAAQ,EAAE,SAAS;YACnB,WAAW,EAAE,6CAA6C;YAC1D,KAAK,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACrD,QAAQ,EAAE,MAAM;YAChB,WAAW,EAAE,qCAAqC;YAClD,KAAK,EAAE,MAAM,CAAC,IAAI;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACtD,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE,0CAA0C;YACvD,KAAK,EAAE,MAAM,CAAC,IAAI;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACxD,QAAQ,EAAE,YAAY;YACtB,WAAW,EAAE,mCAAmC;YAChD,KAAK,EAAE,MAAM,CAAC,IAAI;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACzD,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE,2CAA2C;YACxD,KAAK,EAAE,MAAM,CAAC,IAAI;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YACzD,QAAQ,EAAE,OAAO;YACjB,WAAW,EAAE,kCAAkC;YAC/C,KAAK,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,UAAU;YAChB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YACxD,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE,yCAAyC;YACtD,KAAK,EAAE,MAAM,CAAC,KAAK;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAC1D,QAAQ,EAAE,aAAa;YACvB,WAAW,EAAE,sCAAsC;YACnD,KAAK,EAAE,MAAM,CAAC,IAAI;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAC9D,QAAQ,EAAE,eAAe;YACzB,WAAW,EAAE,sCAAsC;YACnD,KAAK,EAAE,MAAM,CAAC,IAAI;SACrB,CAAC,CAAC;QAEH,0BAA0B;QAC1B,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAC7D,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE,kBAAkB;YAC/B,KAAK,EAAE,MAAM,CAAC,IAAI;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAC9D,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE,cAAc;YAC3B,KAAK,EAAE,MAAM,CAAC,IAAI;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,KAAK;YACX,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YAC3D,QAAQ,EAAE,KAAK;YACf,WAAW,EAAE,gBAAgB;YAC7B,KAAK,EAAE,MAAM,CAAC,IAAI;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACvD,QAAQ,EAAE,WAAW;YACrB,WAAW,EAAE,mBAAmB;YAChC,KAAK,EAAE,MAAM,CAAC,IAAI;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YACzD,QAAQ,EAAE,SAAS;YACnB,WAAW,EAAE,eAAe;YAC5B,KAAK,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,aAAa;YACnB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YACxD,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE,kBAAkB;YAC/B,KAAK,EAAE,MAAM,CAAC,KAAK;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YAC9D,QAAQ,EAAE,UAAU;YACpB,WAAW,EAAE,cAAc;YAC3B,KAAK,EAAE,MAAM,CAAC,KAAK;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,SAAS;YACf,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACxD,QAAQ,EAAE,YAAY;YACtB,WAAW,EAAE,cAAc;YAC3B,KAAK,EAAE,MAAM,CAAC,IAAI;SACrB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YAC3D,QAAQ,EAAE,SAAS;YACnB,WAAW,EAAE,cAAc;YAC3B,KAAK,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC,CAAC;QAEH,+CAA+C;QAC/C,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,gBAAgB;YACtB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YAC1D,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,2BAA2B;YACxC,KAAK,EAAE,MAAM,CAAC,KAAK;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,kBAAkB;YACxB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YAC5D,QAAQ,EAAE,kBAAkB;YAC5B,WAAW,EAAE,6BAA6B;YAC1C,KAAK,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,kBAAkB;YACxB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YAC5D,QAAQ,EAAE,kBAAkB;YAC5B,WAAW,EAAE,6BAA6B;YAC1C,KAAK,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC,CAAC;QAEH,IAAI,CAAC,aAAa,CAAC;YACf,IAAI,EAAE,eAAe;YACrB,IAAI,EAAE,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACzD,QAAQ,EAAE,eAAe;YACzB,WAAW,EAAE,iCAAiC;YAC9C,KAAK,EAAE,MAAM,CAAC,IAAI;SACrB,CAAC,CAAC;IACP,CAAC;IAES,aAAa,CAAC,IAAkB;QACtC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAES,WAAW,CAAC,OAAwB;QAC1C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAES,mBAAmB,CAAC,QAAgB,EAAE,SAAgC,QAAQ;QACpF,yBAAyB;QACzB,MAAM,kBAAkB,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;QAElD,0CAA0C;QAC1C,MAAM,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,MAAM,IAAI,kBAAkB,EAAE,CAAC,CAAC;QACrF,IAAI,kBAAkB,EAAE,CAAC;YACrB,OAAO,KAAK,kBAAkB,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;QAC9C,CAAC;QAED,oCAAoC;QACpC,MAAM,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QACxD,IAAI,IAAI,EAAE,CAAC;YACP,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;QAChC,CAAC;QAED,6CAA6C;QAC7C,QAAQ,kBAAkB,EAAE,CAAC;YACzB,KAAK,OAAO;gBACR,OAAO,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAU,CAAC;YACvE,KAAK,SAAS;gBACV,OAAO,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC,CAAC,YAAY,CAAC;YAC3E,KAAK,SAAS;gBACV,OAAO,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,UAAU,CAAC;YACvE,KAAK,MAAM;gBACP,OAAO,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,SAAS,CAAC;YACrE;gBACI,OAAO,MAAM,KAAK,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,eAAe,CAAC;QACvE,CAAC;IACL,CAAC;IAED,uFAAuF;IAChF,kBAAkB,CACrB,QAA6B,EAC7B,KAAsC,EACtC,OAAiC,EACjC,KAA+B;QAE/B,IAAI,KAAK,CAAC,uBAAuB,EAAE,CAAC;YAChC,OAAO,EAAE,CAAC;QACd,CAAC;QACD,MAAM,OAAO,GAAwB,EAAE,CAAC;QACxC,MAAM,iBAAiB,GAAsB;YACzC,QAAQ;YACR,KAAK;YACL,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,OAAO,EAAE,OAAO,CAAC,WAAW,KAAK,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM;YACxF,MAAM,EAAE,UAAU;SACrB,CAAC;QAEF,4BAA4B;QAC5B,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;QACvE,OAAO,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;QAEhC,+BAA+B;QAC/B,MAAM,iBAAiB,GAAG,IAAI,CAAC,0BAA0B,CAAC,iBAAiB,CAAC,CAAC;QAC7E,OAAO,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,CAAC;QAEnC,4BAA4B;QAC5B,MAAM,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,CAAC,CAAC;QACvE,OAAO,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAC;QAEhC,OAAO,OAAO,CAAC;IACnB,CAAC;IAES,uBAAuB,CAAC,OAA0B;QACxD,MAAM,OAAO,GAAwB,EAAE,CAAC;QACxC,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAErD,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC5C,IAAI,OAAO,EAAE,CAAC;gBACV,IAAI,CAAC;oBACD,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;oBACrE,MAAM,CAAC,KAAK,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;oBAC/E,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACzB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;gBACxD,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,0BAA0B,CAAC,OAA0B;QACzD,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,KAAK,MAAM,UAAU,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YAC3C,oCAAoC;YACpC,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,QAAQ,UAAU,CAAC,OAAO,EAAE,EAC5B,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;YAEF,MAAM,CAAC,WAAW,GAAG,CAAC,UAAU,CAAC,CAAC;YAClC,MAAM,CAAC,OAAO,GAAG;gBACb,OAAO,EAAE,4BAA4B;gBACrC,KAAK,EAAE,kBAAkB,UAAU,CAAC,OAAO,EAAE;gBAC7C,SAAS,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC;aAC5C,CAAC;YAEF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzB,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAES,uBAAuB,CAAC,OAA0B;QACxD,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,sDAAsD;QACtD,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;QAExE,qBAAqB;QACrB,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC9D,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,UAAU,CACtC,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,mBAAmB,EAC5D,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;YACF,YAAY,CAAC,OAAO,GAAG;gBACnB,OAAO,EAAE,yBAAyB;gBAClC,KAAK,EAAE,kBAAkB;gBACzB,SAAS,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;aAChC,CAAC;YACF,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAC/B,CAAC;QAED,8BAA8B;QAC9B,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACrF,MAAM,cAAc,GAAG,IAAI,MAAM,CAAC,UAAU,CACxC,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,iBAAiB,EACxD,MAAM,CAAC,cAAc,CAAC,eAAe,CACxC,CAAC;YACF,cAAc,CAAC,OAAO,GAAG;gBACrB,OAAO,EAAE,uBAAuB;gBAChC,KAAK,EAAE,gBAAgB;gBACvB,SAAS,EAAE,CAAC,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC;aAC/C,CAAC;YACF,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACjC,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,4BAA4B;IACrB,aAAa,CAAC,MAAc;QAC/B,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3C,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC9C,IAAI,OAAO,EAAE,CAAC;gBACV,IAAI,CAAC;oBACD,uDAAuD;oBACvD,MAAM,YAAY,GAAG;wBACjB,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC;wBAC1C,OAAO,EAAE,GAAG,EAAE,CAAC,MAAM;qBACD,CAAC;oBAEzB,MAAM,SAAS,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;oBAC3D,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,EAAE,SAAS,CAAC,CAAC;oBAC7D,IAAI,MAAM,EAAE,CAAC;wBACT,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBACzB,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,KAAK,CAAC,8CAA8C,OAAO,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC1F,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,2BAA2B;QAC/B,mCAAmC;QACnC,IAAI,CAAC,mBAAmB,CAAC;YACrB,OAAO,EAAE,0CAA0C;YACnD,MAAM,EAAE,mBAAmB;YAC3B,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,UAAU;YACpB,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;gBACnB,MAAM,cAAc,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,6CAA6C;gBAC9E,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,QAAQ,cAAc,qBAAqB,EAClF,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,yBAAyB;oBAClC,KAAK,EAAE,OAAO,cAAc,QAAQ;oBACpC,SAAS,EAAE,CAAC,MAAM,EAAE,cAAc,CAAC;iBACtC,CAAC;gBACF,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,mBAAmB,CAAC;YACrB,OAAO,EAAE,oCAAoC;YAC7C,MAAM,EAAE,sBAAsB;YAC9B,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,UAAU;YACpB,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;gBACnB,MAAM,cAAc,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,qCAAqC;gBACtE,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,QAAQ,cAAc,CAAC,WAAW,EAAE,EAAE,EAC7E,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,4BAA4B;oBACrC,KAAK,EAAE,OAAO,cAAc,CAAC,WAAW,EAAE,EAAE;oBAC5C,SAAS,EAAE,CAAC,MAAM,EAAE,cAAc,CAAC;iBACtC,CAAC;gBACF,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,mBAAmB,CAAC;YACrB,OAAO,EAAE,0CAA0C;YACnD,MAAM,EAAE,sBAAsB;YAC9B,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,UAAU;YACpB,GAAG,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;gBACnB,MAAM,eAAe,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,2CAA2C;gBAC7E,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,UAAU,CAChC,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,QAAQ,eAAe,EAAE,EAC7D,MAAM,CAAC,cAAc,CAAC,QAAQ,CACjC,CAAC;gBACF,MAAM,CAAC,OAAO,GAAG;oBACb,OAAO,EAAE,4BAA4B;oBACrC,KAAK,EAAE,OAAO,eAAe,EAAE;oBAC/B,SAAS,EAAE,CAAC,MAAM,EAAE,eAAe,CAAC;iBACvC,CAAC;gBACF,OAAO,MAAM,CAAC;YAClB,CAAC;SACJ,CAAC,CAAC;IACP,CAAC;IAEO,mBAAmB,CAAC,OAAwB;QAChD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAEM,qBAAqB,CAAC,MAAc;QACvC,MAAM,OAAO,GAAwB,EAAE,CAAC;QAExC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3C,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAC9C,IAAI,OAAO,EAAE,CAAC;gBACV,IAAI,CAAC;oBACD,MAAM,gBAAgB,GAAG;wBACrB,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,iBAAiB,CAAC;wBACxC,QAAQ,EAAE,iBAAiB;wBAC3B,UAAU,EAAE,IAAI;wBAChB,UAAU,EAAE,WAAW;wBACvB,OAAO,EAAE,CAAC;wBACV,OAAO,EAAE,KAAK;wBACd,QAAQ,EAAE,KAAK;wBACf,IAAI,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;wBAClC,SAAS,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM;wBACpC,MAAM,EAAE,UAAS,cAAwC;4BACrD,MAAM,IAAI,GAAG,OAAO,cAAc,KAAK,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC;4BACvF,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;4BAC5C,OAAO;gCACH,UAAU,EAAE,IAAI;gCAChB,IAAI;gCACJ,KAAK,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;gCACnD,uBAAuB,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC;gCAC/D,gCAAgC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gCACnD,mBAAmB,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;6BACxC,CAAC;wBACN,CAAC;wBACD,QAAQ,EAAE,UAAS,QAAyB;4BACxC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;4BACjC,IAAI,MAAM,GAAG,CAAC,CAAC;4BACf,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;gCACrC,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAE,iBAAiB;4BAC7D,CAAC;4BACD,OAAO,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;wBACpF,CAAC;wBACD,UAAU,EAAE,UAAS,MAAc;4BAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;4BACjC,IAAI,SAAS,GAAG,MAAM,CAAC;4BACvB,IAAI,IAAI,GAAG,CAAC,CAAC;4BAEb,OAAO,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;gCACzB,MAAM,UAAU,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,iBAAiB;gCACpE,IAAI,SAAS,GAAG,UAAU,EAAE,CAAC;oCACzB,OAAO,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gCAChD,CAAC;gCACD,SAAS,IAAI,UAAU,CAAC;gCACxB,IAAI,EAAE,CAAC;4BACX,CAAC;4BAED,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;4BAClC,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC,EAAE,MAAM,IAAI,CAAC,CAAC;4BAC9C,OAAO,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;wBACnD,CAAC;wBACD,OAAO,EAAE,UAAS,KAAoB;4BAClC,IAAI,CAAC,KAAK,EAAE,CAAC;gCACT,OAAO,MAAM,CAAC;4BAClB,CAAC;4BACD,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;4BACjC,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gCACtC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;4BACjG,CAAC;4BACD,MAAM,MAAM,GAAa,EAAE,CAAC;4BAC5B,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;gCACtD,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gCAC5B,IAAI,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oCACzB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;gCACvD,CAAC;qCAAM,IAAI,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;oCAC9B,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;gCACxD,CAAC;qCAAM,CAAC;oCACJ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gCACtB,CAAC;4BACL,CAAC;4BACD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC7B,CAAC;wBACD,sBAAsB,EAAE,GAAG,EAAE,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBAC1D,aAAa,EAAE,CAAC,KAAmB,EAAE,EAAE,CAAC,KAAK;wBAC7C,gBAAgB,EAAE,CAAC,QAAyB,EAAE,EAAE,CAAC,QAAQ;wBACzD,GAAG,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE;qBAC3B,CAAC;oBAEF,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CACtB,OAAO,EACP,gBAAkD,EAClD,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,gBAAgB,CAAC,SAAS,GAAG,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CACnH,CAAC;oBACF,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACzB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;gBACjE,CAAC;YACL,CAAC;QACL,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,mEAAmE;IAC5D,eAAe,CAAC,IAAY;QAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAEM,WAAW;QACd,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;IACnD,CAAC;IAEM,cAAc,CAAC,QAAgB,EAAE,MAAyB;QAC7D,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC9C,CAAC;IAEM,gBAAgB,CAAC,QAAgB;QACpC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACzC,CAAC;IAEM,SAAS,CAAC,QAAgB;QAC7B,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC7C,CAAC;IAEM,gBAAgB;QACnB,OAAO,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC;IACpC,CAAC;IAEM,kBAAkB;QACrB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;IACnC,CAAC;IAEM,KAAK,CAAC,aAAa,CAAC,MAAyB,EAAE,QAA6B;QAC/E,8BAA8B;QAC9B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;YACrB,MAAM;YACN,QAAQ,EAAE,GAAG;YACb,QAAQ,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,IAAI,SAAS;YACzC,UAAU,EAAE,GAAG;YACf,QAAQ,EAAE;gBACN,MAAM,EAAE,UAAU;gBAClB,OAAO,EAAE,QAAQ;gBACjB,OAAO,EAAE;oBACL,QAAQ;oBACR,KAAK,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;oBACrE,UAAU,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;iBACtC;aACJ;SACJ,CAAC,CAAC;QACH,IAAI,CAAC;YACD,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;gBACd,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;gBAC9D,OAAO,OAAO,CAAC;YACnB,CAAC;iBAAM,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACxB,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC;gBAChG,OAAO,IAAI,CAAC;YAChB,CAAC;iBAAM,CAAC;gBACJ,OAAO,KAAK,CAAC;YACjB,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;YAClD,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;CACJ;AAr5BD,gEAq5BC;AAED,6BAA6B;AAC7B,MAAa,iBAAiB;IAClB,MAAM,CAAC,SAAS,CAAoB;IAC3B,UAAU,GAA4C,IAAI,GAAG,EAAE,CAAC;IAChE,cAAc,GAAwB,EAAE,CAAC;IAE1D;QACI,IAAI,CAAC,WAAW,EAAE,CAAC;IACvB,CAAC;IAEM,MAAM,CAAC,WAAW;QACrB,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;YAC/B,iBAAiB,CAAC,SAAS,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAC1D,CAAC;QACD,OAAO,iBAAiB,CAAC,SAAS,CAAC;IACvC,CAAC;IAEO,WAAW;QACf,yCAAyC;QACzC,MAAM,QAAQ,GAAG,IAAI,0BAA0B,EAAE,CAAC;QAClD,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,2BAA2B,CAC3D,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,EACjC,QAAQ,EACR;YACI,uBAAuB,EAAE;gBACrB,MAAM,CAAC,cAAc,CAAC,QAAQ;gBAC9B,MAAM,CAAC,cAAc,CAAC,QAAQ;gBAC9B,MAAM,CAAC,cAAc,CAAC,MAAM;aAC/B;SACJ,CACJ,CAAC;QAEF,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAEtC,sCAAsC;QACtC,MAAM,gBAAgB,GAAG,IAAI,0BAA0B,EAAE,CAAC;QAC1D,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,EAAE,gBAAgB,CAAC,CAAC;IACtD,CAAC;IAEM,WAAW,CAAC,IAAY;QAC3B,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAEM,eAAe;QAClB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;IAChD,CAAC;IAEM,gBAAgB,CAAC,IAAY,EAAE,QAAoC;QACtE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IACxC,CAAC;IAEM,kBAAkB,CAAC,IAAY;QAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,QAAQ,EAAE,CAAC;YACX,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,QAA6B,EAAE,KAAmB;QACvE,MAAM,UAAU,GAAwB,EAAE,CAAC;QAC3C,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,uBAAuB,EAAE,CAAC,KAAK,CAAC;QAEzD,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9C,MAAM,OAAO,GAA6B;gBACtC,WAAW,EAAE,EAAE;gBACf,WAAW,EAAE,MAAM,CAAC,qBAAqB,CAAC,MAAM;gBAChD,IAAI,EAAE,SAAS;aAClB,CAAC;YAEF,IAAI,CAAC;gBACD,MAAM,OAAO,GAAG,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC7E,IAAI,OAAO,EAAE,CAAC;oBACV,IAAI,OAAO,YAAY,OAAO,EAAE,CAAC;wBAC7B,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC;wBACtC,IAAI,eAAe,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC;4BACpD,UAAU,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,CAAC;wBACxC,CAAC;oBACL,CAAC;yBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;wBAChC,gDAAgD;wBAChD,MAAM,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAA+B,EAAE,CACvE,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,IAAI,MAAM,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,QAAQ,CAChG,CAAC;wBACF,UAAU,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;oBACpC,CAAC;gBACL,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YACtE,CAAC;QACL,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;IAEM,OAAO;QACV,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;QAClD,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;IAC5B,CAAC;CACJ;AAlGD,8CAkGC;AAED,4BAA4B;AACf,QAAA,iBAAiB,GAAG,iBAAiB,CAAC,WAAW,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\r\n\r\n/**\r\n * Comprehensive Code Action and Quick Fix Integration System\r\n * Supports all VS Code Code Action indicators and provides advanced quick fix capabilities\r\n */\r\n\r\nexport interface CodeActionResult {\r\n    action: vscode.CodeAction;\r\n    priority: number;\r\n    category: string;\r\n    confidence: number;\r\n    metadata: {\r\n        source: string;\r\n        trigger: string;\r\n        context: {\r\n            document: vscode.TextDocument;\r\n            range: vscode.Range;\r\n            diagnostic?: vscode.Diagnostic;\r\n        };\r\n    };\r\n}\r\n\r\nexport interface QuickFixPattern {\r\n    pattern: RegExp;\r\n    action: string;\r\n    priority: number;\r\n    category: string;\r\n    fix: (match: RegExpMatchArray, document: vscode.TextDocument, range: vscode.Range) => vscode.CodeAction;\r\n}\r\n\r\nexport interface CodeActionContext {\r\n    document: vscode.TextDocument;\r\n    range: vscode.Range;\r\n    diagnostics: readonly vscode.Diagnostic[];\r\n    trigger: 'manual' | 'auto' | 'onSave' | 'onType';\r\n    source: string;\r\n    only?: vscode.CodeActionKind;\r\n}\r\n\r\nexport interface IconTheme {\r\n    light: vscode.ThemeColor;\r\n    dark: vscode.ThemeColor;\r\n}\r\n\r\nexport interface QuickFixIcon {\r\n    name: string;\r\n    icon: vscode.ThemeIcon;\r\n    category: string;\r\n    description: string;\r\n    theme?: IconTheme;\r\n}\r\n\r\n// EnhancedCodeActionProvider functionality consolidated into TerminalCodeActionProvider\r\n// This class is removed to eliminate duplication\r\n\r\n// Terminal Code Action Integration - Consolidated Provider\r\nexport class TerminalCodeActionProvider implements vscode.CodeActionProvider {\r\n    // Consolidated properties from EnhancedCodeActionProvider\r\n    protected readonly _quickFixPatterns: QuickFixPattern[] = [];\r\n    protected readonly _iconRegistry: Map<string, QuickFixIcon> = new Map();\r\n    protected readonly _actionHistory: CodeActionResult[] = [];\r\n    protected readonly _activeActions: Map<string, vscode.CodeAction> = new Map();\r\n    private readonly _terminalPatterns: QuickFixPattern[] = [];\r\n\r\n    constructor() {\r\n        this._initializeQuickFixPatterns();\r\n        this._initializeIconRegistry();\r\n        this._initializeTerminalPatterns();\r\n    }\r\n\r\n    // Comprehensive pattern initialization with ALL functionality from EnhancedCodeActionProvider\r\n    private _initializeQuickFixPatterns(): void {\r\n        // Terminal Error Patterns\r\n        this._addPattern({\r\n            pattern: /bash:\\s+(.+?):\\s+command not found/i,\r\n            action: 'install-missing-command',\r\n            priority: 100,\r\n            category: 'terminal',\r\n            fix: (match, doc, range) => {\r\n                const command = match[1];\r\n                const action = new vscode.CodeAction(\r\n                    `Install missing command: ${command}`,\r\n                    vscode.CodeActionKind.QuickFix\r\n                );\r\n                action.command = {\r\n                    command: 'codessa.installCommand',\r\n                    title: `Install ${command}`,\r\n                    arguments: [command, 'terminal']\r\n                };\r\n                action.diagnostics = [{\r\n                    range,\r\n                    message: `Command '${command}' not found`,\r\n                    severity: vscode.DiagnosticSeverity.Error,\r\n                    source: 'terminal'\r\n                }];\r\n                return action;\r\n            }\r\n        });\r\n\r\n        // Import Error Patterns\r\n        this._addPattern({\r\n            pattern: /Cannot find module\\s+['\"](.+?)['\"]/i,\r\n            action: 'fix-import',\r\n            priority: 95,\r\n            category: 'imports',\r\n            fix: (match, doc, range) => {\r\n                const moduleName = match[1];\r\n                const lineText = doc.getText(range);\r\n                const importStatement = lineText.includes('require') ?\r\n                    `const ${moduleName.split('/').pop()} = require('${moduleName}');` :\r\n                    `import * as ${moduleName.split('/').pop()} from '${moduleName}';`;\r\n\r\n                const action = new vscode.CodeAction(\r\n                    `Install missing module: ${moduleName}`,\r\n                    vscode.CodeActionKind.QuickFix\r\n                );\r\n                action.edit = new vscode.WorkspaceEdit();\r\n                action.edit.replace(doc.uri, range, importStatement);\r\n                action.command = {\r\n                    command: 'codessa.installModule',\r\n                    title: `Install ${moduleName}`,\r\n                    arguments: [moduleName, 'npm', doc.uri.fsPath]\r\n                };\r\n                return action;\r\n            }\r\n        });\r\n\r\n        // TypeScript Error Patterns\r\n        this._addPattern({\r\n            pattern: /Property\\s+['\"](.+?)['\"]\\s+does not exist on type/i,\r\n            action: 'add-property',\r\n            priority: 90,\r\n            category: 'typescript',\r\n            fix: (match, doc, range) => {\r\n                const property = match[1];\r\n                const action = new vscode.CodeAction(\r\n                    `Add property '${property}' to interface`,\r\n                    vscode.CodeActionKind.QuickFix\r\n                );\r\n                action.command = {\r\n                    command: 'codessa.addProperty',\r\n                    title: `Add property ${property}`,\r\n                    arguments: [property, doc, range]\r\n                };\r\n                return action;\r\n            }\r\n        });\r\n\r\n        // ESLint Error Patterns\r\n        this._addPattern({\r\n            pattern: /(.+?)\\s+is not defined/i,\r\n            action: 'define-variable',\r\n            priority: 85,\r\n            category: 'eslint',\r\n            fix: (match, doc, range) => {\r\n                const variable = match[1];\r\n                const action = new vscode.CodeAction(\r\n                    `Define variable: ${variable}`,\r\n                    vscode.CodeActionKind.QuickFix\r\n                );\r\n                action.edit = new vscode.WorkspaceEdit();\r\n                action.edit.insert(doc.uri, new vscode.Position(range.start.line, 0),\r\n                    `const ${variable} = ;\\n`);\r\n                return action;\r\n            }\r\n        });\r\n\r\n        // Security Vulnerability Patterns\r\n        this._addPattern({\r\n            pattern: /Potential security vulnerability:\\s+(.+?)/i,\r\n            action: 'fix-security',\r\n            priority: 100,\r\n            category: 'security',\r\n            fix: (match, doc, range) => {\r\n                const issue = match[1];\r\n                const action = new vscode.CodeAction(\r\n                    `Fix security issue: ${issue}`,\r\n                    vscode.CodeActionKind.QuickFix\r\n                );\r\n                action.command = {\r\n                    command: 'codessa.fixSecurity',\r\n                    title: `Fix ${issue}`,\r\n                    arguments: [issue, doc, range]\r\n                };\r\n                return action;\r\n            }\r\n        });\r\n\r\n        // Python Error Patterns\r\n        this._addPattern({\r\n            pattern: /NameError:\\s+name\\s+['\"](.+?)['\"]\\s+is not defined/i,\r\n            action: 'import-python-module',\r\n            priority: 80,\r\n            category: 'python',\r\n            fix: (match, doc, range) => {\r\n                const name = match[1];\r\n                const action = new vscode.CodeAction(\r\n                    `Import Python module: ${name}`,\r\n                    vscode.CodeActionKind.QuickFix\r\n                );\r\n                action.edit = new vscode.WorkspaceEdit();\r\n                // Insert import at the beginning of the file, but use range for context\r\n                const insertPosition = range.start.line > 0 ? new vscode.Position(0, 0) : range.start;\r\n                action.edit.insert(doc.uri, insertPosition, `import ${name}\\n`);\r\n                action.diagnostics = [{\r\n                    range,\r\n                    message: `Name '${name}' is not defined`,\r\n                    severity: vscode.DiagnosticSeverity.Error,\r\n                    source: 'python'\r\n                }];\r\n                return action;\r\n            }\r\n        });\r\n\r\n        // Git Error Patterns\r\n        this._addPattern({\r\n            pattern: /fatal:\\s+not\\s+a\\s+git\\s+repository/i,\r\n            action: 'init-git-repo',\r\n            priority: 75,\r\n            category: 'git',\r\n            fix: (match, doc, range) => {\r\n                const errorMessage = match[0]; // Full error message\r\n                const action = new vscode.CodeAction(\r\n                    'Initialize Git repository',\r\n                    vscode.CodeActionKind.QuickFix\r\n                );\r\n                action.command = {\r\n                    command: 'codessa.initGitRepo',\r\n                    title: 'Initialize Git repository',\r\n                    arguments: [doc.uri.fsPath]\r\n                };\r\n                action.diagnostics = [{\r\n                    range,\r\n                    message: errorMessage,\r\n                    severity: vscode.DiagnosticSeverity.Error,\r\n                    source: 'git'\r\n                }];\r\n                return action;\r\n            }\r\n        });\r\n\r\n        // Docker Error Patterns\r\n        this._addPattern({\r\n            pattern: /docker:\\s+(.+?)\\s+not\\s+found/i,\r\n            action: 'install-docker',\r\n            priority: 70,\r\n            category: 'docker',\r\n            fix: (match, doc, range) => {\r\n                const dockerCommand = match[1]; // The specific docker command that wasn't found\r\n                const action = new vscode.CodeAction(\r\n                    `Install Docker (missing: ${dockerCommand})`,\r\n                    vscode.CodeActionKind.QuickFix\r\n                );\r\n                action.command = {\r\n                    command: 'codessa.installDocker',\r\n                    title: 'Install Docker',\r\n                    arguments: [dockerCommand, doc.uri.fsPath]\r\n                };\r\n                action.diagnostics = [{\r\n                    range,\r\n                    message: `Docker command '${dockerCommand}' not found`,\r\n                    severity: vscode.DiagnosticSeverity.Error,\r\n                    source: 'docker'\r\n                }];\r\n                return action;\r\n            }\r\n        });\r\n\r\n        // Network Error Patterns\r\n        this._addPattern({\r\n            pattern: /ENOTFOUND|ECONNREFUSED|ETIMEDOUT/i,\r\n            action: 'check-network',\r\n            priority: 65,\r\n            category: 'network',\r\n            fix: (match, doc, range) => {\r\n                const networkError = match[0]; // The specific network error (ENOTFOUND, ECONNREFUSED, etc.)\r\n                const action = new vscode.CodeAction(\r\n                    `Fix network issue: ${networkError}`,\r\n                    vscode.CodeActionKind.QuickFix\r\n                );\r\n                action.command = {\r\n                    command: 'codessa.checkNetwork',\r\n                    title: 'Check network connection',\r\n                    arguments: [networkError, doc.uri.fsPath]\r\n                };\r\n                action.diagnostics = [{\r\n                    range,\r\n                    message: `Network error: ${networkError}`,\r\n                    severity: vscode.DiagnosticSeverity.Error,\r\n                    source: 'network'\r\n                }];\r\n                return action;\r\n            }\r\n        });\r\n\r\n        // File Permission Error Patterns\r\n        this._addPattern({\r\n            pattern: /EACCES|permission\\s+denied/i,\r\n            action: 'fix-permissions',\r\n            priority: 60,\r\n            category: 'permissions',\r\n            fix: (match, doc, range) => {\r\n                const permissionError = match[0]; // The specific permission error (EACCES, permission denied, etc.)\r\n                const action = new vscode.CodeAction(\r\n                    `Fix file permissions: ${permissionError}`,\r\n                    vscode.CodeActionKind.QuickFix\r\n                );\r\n                action.command = {\r\n                    command: 'codessa.fixPermissions',\r\n                    title: 'Fix file permissions',\r\n                    arguments: [doc.uri.fsPath, permissionError]\r\n                };\r\n                action.diagnostics = [{\r\n                    range,\r\n                    message: `Permission error: ${permissionError}`,\r\n                    severity: vscode.DiagnosticSeverity.Error,\r\n                    source: 'permissions'\r\n                }];\r\n                return action;\r\n            }\r\n        });\r\n\r\n        // Syntax Error Patterns\r\n        this._addPattern({\r\n            pattern: /SyntaxError|Unexpected\\s+token/i,\r\n            action: 'fix-syntax',\r\n            priority: 55,\r\n            category: 'syntax',\r\n            fix: (match, doc, range) => {\r\n                const syntaxError = match[0]; // The specific syntax error (SyntaxError, Unexpected token, etc.)\r\n                const action = new vscode.CodeAction(\r\n                    `Fix syntax error: ${syntaxError}`,\r\n                    vscode.CodeActionKind.QuickFix\r\n                );\r\n                action.command = {\r\n                    command: 'codessa.fixSyntax',\r\n                    title: 'Fix syntax error',\r\n                    arguments: [doc, range, syntaxError]\r\n                };\r\n                action.diagnostics = [{\r\n                    range,\r\n                    message: `Syntax error: ${syntaxError}`,\r\n                    severity: vscode.DiagnosticSeverity.Error,\r\n                    source: 'syntax'\r\n                }];\r\n                return action;\r\n            }\r\n        });\r\n    }\r\n\r\n    private _initializeIconRegistry(): void {\r\n        // Define common themes\r\n        const themes = {\r\n            error: {\r\n                light: new vscode.ThemeColor('errorForeground'),\r\n                dark: new vscode.ThemeColor('errorForeground')\r\n            },\r\n            warning: {\r\n                light: new vscode.ThemeColor('editorWarning.foreground'),\r\n                dark: new vscode.ThemeColor('editorWarning.foreground')\r\n            },\r\n            info: {\r\n                light: new vscode.ThemeColor('editorInfo.foreground'),\r\n                dark: new vscode.ThemeColor('editorInfo.foreground')\r\n            },\r\n            success: {\r\n                light: new vscode.ThemeColor('testing.iconPassed'),\r\n                dark: new vscode.ThemeColor('testing.iconPassed')\r\n            }\r\n        };\r\n\r\n        // Standard VS Code Quick Fix Icons with unified theming\r\n        this._registerIcon({\r\n            name: 'lightbulb',\r\n            icon: new vscode.ThemeIcon('lightbulb', themes.info.light),\r\n            category: 'suggestion',\r\n            description: 'General suggestions and improvements',\r\n            theme: themes.info\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'error',\r\n            icon: new vscode.ThemeIcon('error', themes.error.light),\r\n            category: 'error',\r\n            description: 'Error conditions requiring immediate attention',\r\n            theme: themes.error\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'warning',\r\n            icon: new vscode.ThemeIcon('warning', themes.warning.light),\r\n            category: 'warning',\r\n            description: 'Warning conditions that should be addressed',\r\n            theme: themes.warning\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'info',\r\n            icon: new vscode.ThemeIcon('info', themes.info.light),\r\n            category: 'info',\r\n            description: 'Informational suggestions and hints',\r\n            theme: themes.info\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'tools',\r\n            icon: new vscode.ThemeIcon('tools', themes.info.light),\r\n            category: 'refactor',\r\n            description: 'Refactoring and code improvement actions',\r\n            theme: themes.info\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'package',\r\n            icon: new vscode.ThemeIcon('package', themes.info.light),\r\n            category: 'dependency',\r\n            description: 'Package and dependency management',\r\n            theme: themes.info\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'terminal',\r\n            icon: new vscode.ThemeIcon('terminal', themes.info.light),\r\n            category: 'terminal',\r\n            description: 'Terminal and command-line related actions',\r\n            theme: themes.info\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'debug',\r\n            icon: new vscode.ThemeIcon('debug', themes.warning.light),\r\n            category: 'debug',\r\n            description: 'Debugging and diagnostic actions',\r\n            theme: themes.warning\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'security',\r\n            icon: new vscode.ThemeIcon('shield', themes.error.light),\r\n            category: 'security',\r\n            description: 'Security-related fixes and improvements',\r\n            theme: themes.error\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'performance',\r\n            icon: new vscode.ThemeIcon('dashboard', themes.info.light),\r\n            category: 'performance',\r\n            description: 'Performance optimization suggestions',\r\n            theme: themes.info\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'accessibility',\r\n            icon: new vscode.ThemeIcon('accessibility', themes.info.light),\r\n            category: 'accessibility',\r\n            description: 'Accessibility improvements and fixes',\r\n            theme: themes.info\r\n        });\r\n\r\n        // Language-specific icons\r\n        this._registerIcon({\r\n            name: 'typescript',\r\n            icon: new vscode.ThemeIcon('symbol-class', themes.info.light),\r\n            category: 'language',\r\n            description: 'TypeScript fixes',\r\n            theme: themes.info\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'python',\r\n            icon: new vscode.ThemeIcon('symbol-method', themes.info.light),\r\n            category: 'language',\r\n            description: 'Python fixes',\r\n            theme: themes.info\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'git',\r\n            icon: new vscode.ThemeIcon('git-branch', themes.info.light),\r\n            category: 'vcs',\r\n            description: 'Git operations',\r\n            theme: themes.info\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'docker',\r\n            icon: new vscode.ThemeIcon('server', themes.info.light),\r\n            category: 'container',\r\n            description: 'Docker operations',\r\n            theme: themes.info\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'network',\r\n            icon: new vscode.ThemeIcon('globe', themes.warning.light),\r\n            category: 'network',\r\n            description: 'Network fixes',\r\n            theme: themes.warning\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'permissions',\r\n            icon: new vscode.ThemeIcon('shield', themes.error.light),\r\n            category: 'security',\r\n            description: 'Permission fixes',\r\n            theme: themes.error\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'syntax',\r\n            icon: new vscode.ThemeIcon('symbol-color', themes.error.light),\r\n            category: 'language',\r\n            description: 'Syntax fixes',\r\n            theme: themes.error\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'imports',\r\n            icon: new vscode.ThemeIcon('package', themes.info.light),\r\n            category: 'dependency',\r\n            description: 'Import fixes',\r\n            theme: themes.info\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'eslint',\r\n            icon: new vscode.ThemeIcon('warning', themes.warning.light),\r\n            category: 'linting',\r\n            description: 'ESLint fixes',\r\n            theme: themes.warning\r\n        });\r\n\r\n        // Terminal-specific icons with unified theming\r\n        this._registerIcon({\r\n            name: 'terminal-error',\r\n            icon: new vscode.ThemeIcon('terminal', themes.error.light),\r\n            category: 'terminal-error',\r\n            description: 'Terminal error indicators',\r\n            theme: themes.error\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'terminal-warning',\r\n            icon: new vscode.ThemeIcon('terminal', themes.warning.light),\r\n            category: 'terminal-warning',\r\n            description: 'Terminal warning indicators',\r\n            theme: themes.warning\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'terminal-success',\r\n            icon: new vscode.ThemeIcon('terminal', themes.success.light),\r\n            category: 'terminal-success',\r\n            description: 'Terminal success indicators',\r\n            theme: themes.success\r\n        });\r\n\r\n        this._registerIcon({\r\n            name: 'terminal-info',\r\n            icon: new vscode.ThemeIcon('terminal', themes.info.light),\r\n            category: 'terminal-info',\r\n            description: 'Terminal information indicators',\r\n            theme: themes.info\r\n        });\r\n    }\r\n\r\n    protected _registerIcon(icon: QuickFixIcon): void {\r\n        this._iconRegistry.set(icon.name, icon);\r\n    }\r\n\r\n    protected _addPattern(pattern: QuickFixPattern): void {\r\n        this._quickFixPatterns.push(pattern);\r\n    }\r\n\r\n    protected _getIconForCategory(category: string, source: 'terminal' | 'editor' = 'editor'): string {\r\n        // Normalize the category\r\n        const normalizedCategory = category.toLowerCase();\r\n\r\n        // Try to get a source-specific icon first\r\n        const sourceSpecificIcon = this._iconRegistry.get(`${source}-${normalizedCategory}`);\r\n        if (sourceSpecificIcon) {\r\n            return `$(${sourceSpecificIcon.icon.id})`;\r\n        }\r\n\r\n        // Fallback to general category icon\r\n        const icon = this._iconRegistry.get(normalizedCategory);\r\n        if (icon) {\r\n            return `$(${icon.icon.id})`;\r\n        }\r\n\r\n        // Map common categories to appropriate icons\r\n        switch (normalizedCategory) {\r\n            case 'error':\r\n                return source === 'terminal' ? '$(terminal) $(error)' : '$(error)';\r\n            case 'warning':\r\n                return source === 'terminal' ? '$(terminal) $(warning)' : '$(warning)';\r\n            case 'success':\r\n                return source === 'terminal' ? '$(terminal) $(check)' : '$(check)';\r\n            case 'info':\r\n                return source === 'terminal' ? '$(terminal) $(info)' : '$(info)';\r\n            default:\r\n                return source === 'terminal' ? '$(terminal)' : '$(light-bulb)';\r\n        }\r\n    }\r\n\r\n    // VS Code CodeActionProvider interface implementation with comprehensive functionality\r\n    public provideCodeActions(\r\n        document: vscode.TextDocument,\r\n        range: vscode.Range | vscode.Selection,\r\n        context: vscode.CodeActionContext,\r\n        token: vscode.CancellationToken\r\n    ): vscode.ProviderResult<(vscode.CodeAction | vscode.Command)[]> {\r\n        if (token.isCancellationRequested) {\r\n            return [];\r\n        }\r\n        const actions: vscode.CodeAction[] = [];\r\n        const codeActionContext: CodeActionContext = {\r\n            document,\r\n            range,\r\n            diagnostics: context.diagnostics,\r\n            trigger: context.triggerKind === vscode.CodeActionTriggerKind.Invoke ? 'manual' : 'auto',\r\n            source: 'provider'\r\n        };\r\n\r\n        // Get pattern-based actions\r\n        const patternActions = this._getPatternBasedActions(codeActionContext);\r\n        actions.push(...patternActions);\r\n\r\n        // Get diagnostic-based actions\r\n        const diagnosticActions = this._getDiagnosticBasedActions(codeActionContext);\r\n        actions.push(...diagnosticActions);\r\n\r\n        // Get context-aware actions\r\n        const contextActions = this._getContextAwareActions(codeActionContext);\r\n        actions.push(...contextActions);\r\n\r\n        return actions;\r\n    }\r\n\r\n    protected _getPatternBasedActions(context: CodeActionContext): vscode.CodeAction[] {\r\n        const actions: vscode.CodeAction[] = [];\r\n        const text = context.document.getText(context.range);\r\n\r\n        for (const pattern of this._quickFixPatterns) {\r\n            const matches = text.match(pattern.pattern);\r\n            if (matches) {\r\n                try {\r\n                    const action = pattern.fix(matches, context.document, context.range);\r\n                    action.title = `${this._getIconForCategory(pattern.category)} ${action.title}`;\r\n                    actions.push(action);\r\n                } catch (error) {\r\n                    console.error('Error creating code action:', error);\r\n                }\r\n            }\r\n        }\r\n\r\n        return actions;\r\n    }\r\n\r\n    private _getDiagnosticBasedActions(context: CodeActionContext): vscode.CodeAction[] {\r\n        const actions: vscode.CodeAction[] = [];\r\n\r\n        for (const diagnostic of context.diagnostics) {\r\n            // Create action based on diagnostic\r\n            const action = new vscode.CodeAction(\r\n                `Fix: ${diagnostic.message}`,\r\n                vscode.CodeActionKind.QuickFix\r\n            );\r\n\r\n            action.diagnostics = [diagnostic];\r\n            action.command = {\r\n                command: 'codessa.applyDiagnosticFix',\r\n                title: `Apply fix for: ${diagnostic.message}`,\r\n                arguments: [diagnostic, context.document]\r\n            };\r\n\r\n            actions.push(action);\r\n        }\r\n\r\n        return actions;\r\n    }\r\n\r\n    protected _getContextAwareActions(context: CodeActionContext): vscode.CodeAction[] {\r\n        const actions: vscode.CodeAction[] = [];\r\n\r\n        // Context-aware suggestions based on document content\r\n        const lineText = context.document.lineAt(context.range.start.line).text;\r\n\r\n        // Import suggestions\r\n        if (lineText.includes('import') || lineText.includes('require')) {\r\n            const importAction = new vscode.CodeAction(\r\n                `${this._getIconForCategory('dependency')} Optimize imports`,\r\n                vscode.CodeActionKind.Refactor\r\n            );\r\n            importAction.command = {\r\n                command: 'codessa.optimizeImports',\r\n                title: 'Optimize imports',\r\n                arguments: [context.document]\r\n            };\r\n            actions.push(importAction);\r\n        }\r\n\r\n        // Function/method suggestions\r\n        if (lineText.includes('function') || lineText.includes('=>') || lineText.includes('(')) {\r\n            const refactorAction = new vscode.CodeAction(\r\n                `${this._getIconForCategory('refactor')} Extract method`,\r\n                vscode.CodeActionKind.RefactorExtract\r\n            );\r\n            refactorAction.command = {\r\n                command: 'codessa.extractMethod',\r\n                title: 'Extract method',\r\n                arguments: [context.document, context.range]\r\n            };\r\n            actions.push(refactorAction);\r\n        }\r\n\r\n        return actions;\r\n    }\r\n\r\n    // Terminal-specific methods\r\n    public analyzeOutput(output: string): vscode.CodeAction[] {\r\n        const actions: vscode.CodeAction[] = [];\r\n\r\n        for (const pattern of this._quickFixPatterns) {\r\n            const matches = output.match(pattern.pattern);\r\n            if (matches) {\r\n                try {\r\n                    // Create a mock document and range for terminal output\r\n                    const mockDocument = {\r\n                        uri: vscode.Uri.parse('terminal://output'),\r\n                        getText: () => output\r\n                    } as vscode.TextDocument;\r\n\r\n                    const mockRange = new vscode.Range(0, 0, 0, output.length);\r\n                    const action = pattern.fix(matches, mockDocument, mockRange);\r\n                    if (action) {\r\n                        actions.push(action);\r\n                    }\r\n                } catch (error) {\r\n                    console.error(`Error creating terminal action for pattern ${pattern.action}:`, error);\r\n                }\r\n            }\r\n        }\r\n\r\n        return actions;\r\n    }\r\n\r\n    private _initializeTerminalPatterns(): void {\r\n        // Terminal-specific error patterns\r\n        this._addTerminalPattern({\r\n            pattern: /(npm ERR!|yarn error|pip install error)/i,\r\n            action: 'fix-package-error',\r\n            priority: 100,\r\n            category: 'terminal',\r\n            fix: (match, output) => {\r\n                const packageManager = match[1]; // npm ERR!, yarn error, or pip install error\r\n                const action = new vscode.CodeAction(\r\n                    `${this._getIconForCategory('terminal')} Fix ${packageManager} installation error`,\r\n                    vscode.CodeActionKind.QuickFix\r\n                );\r\n                action.command = {\r\n                    command: 'codessa.fixPackageError',\r\n                    title: `Fix ${packageManager} error`,\r\n                    arguments: [output, packageManager]\r\n                };\r\n                return action;\r\n            }\r\n        });\r\n\r\n        this._addTerminalPattern({\r\n            pattern: /(Permission denied|access denied)/i,\r\n            action: 'fix-permission-error',\r\n            priority: 95,\r\n            category: 'terminal',\r\n            fix: (match, output) => {\r\n                const permissionType = match[1]; // Permission denied or access denied\r\n                const action = new vscode.CodeAction(\r\n                    `${this._getIconForCategory('security')} Fix ${permissionType.toLowerCase()}`,\r\n                    vscode.CodeActionKind.QuickFix\r\n                );\r\n                action.command = {\r\n                    command: 'codessa.fixPermissionError',\r\n                    title: `Fix ${permissionType.toLowerCase()}`,\r\n                    arguments: [output, permissionType]\r\n                };\r\n                return action;\r\n            }\r\n        });\r\n\r\n        this._addTerminalPattern({\r\n            pattern: /(connection refused|connection timeout)/i,\r\n            action: 'fix-connection-error',\r\n            priority: 90,\r\n            category: 'terminal',\r\n            fix: (match, output) => {\r\n                const connectionError = match[1]; // connection refused or connection timeout\r\n                const action = new vscode.CodeAction(\r\n                    `${this._getIconForCategory('debug')} Fix ${connectionError}`,\r\n                    vscode.CodeActionKind.QuickFix\r\n                );\r\n                action.command = {\r\n                    command: 'codessa.fixConnectionError',\r\n                    title: `Fix ${connectionError}`,\r\n                    arguments: [output, connectionError]\r\n                };\r\n                return action;\r\n            }\r\n        });\r\n    }\r\n\r\n    private _addTerminalPattern(pattern: QuickFixPattern): void {\r\n        this._terminalPatterns.push(pattern);\r\n    }\r\n\r\n    public analyzeTerminalOutput(output: string): vscode.CodeAction[] {\r\n        const actions: vscode.CodeAction[] = [];\r\n\r\n        for (const pattern of this._terminalPatterns) {\r\n            const matches = output.match(pattern.pattern);\r\n            if (matches) {\r\n                try {\r\n                    const terminalDocument = {\r\n                        uri: vscode.Uri.parse('terminal-output'),\r\n                        fileName: 'terminal-output',\r\n                        isUntitled: true,\r\n                        languageId: 'plaintext',\r\n                        version: 1,\r\n                        isDirty: false,\r\n                        isClosed: false,\r\n                        save: () => Promise.resolve(false),\r\n                        lineCount: output.split('\\n').length,\r\n                        lineAt: function(lineOrPosition: number | vscode.Position): vscode.TextLine {\r\n                            const line = typeof lineOrPosition === 'number' ? lineOrPosition : lineOrPosition.line;\r\n                            const text = output.split('\\n')[line] || '';\r\n                            return {\r\n                                lineNumber: line,\r\n                                text,\r\n                                range: new vscode.Range(line, 0, line, text.length),\r\n                                rangeIncludingLineBreak: new vscode.Range(line, 0, line + 1, 0),\r\n                                firstNonWhitespaceCharacterIndex: text.search(/\\S/),\r\n                                isEmptyOrWhitespace: !/\\S/.test(text)\r\n                            };\r\n                        },\r\n                        offsetAt: function(position: vscode.Position): number {\r\n                            const lines = output.split('\\n');\r\n                            let offset = 0;\r\n                            for (let i = 0; i < position.line; i++) {\r\n                                offset += (lines[i]?.length || 0) + 1;  // +1 for newline\r\n                            }\r\n                            return offset + Math.min(position.character, lines[position.line]?.length || 0);\r\n                        },\r\n                        positionAt: function(offset: number): vscode.Position {\r\n                            const lines = output.split('\\n');\r\n                            let remaining = offset;\r\n                            let line = 0;\r\n                            \r\n                            while (line < lines.length) {\r\n                                const lineLength = (lines[line]?.length || 0) + 1; // +1 for newline\r\n                                if (remaining < lineLength) {\r\n                                    return new vscode.Position(line, remaining);\r\n                                }\r\n                                remaining -= lineLength;\r\n                                line++;\r\n                            }\r\n                            \r\n                            const lastLine = lines.length - 1;\r\n                            const lastChar = lines[lastLine]?.length || 0;\r\n                            return new vscode.Position(lastLine, lastChar);\r\n                        },\r\n                        getText: function(range?: vscode.Range): string {\r\n                            if (!range) {\r\n                                return output;\r\n                            }\r\n                            const lines = output.split('\\n');\r\n                            if (range.start.line === range.end.line) {\r\n                                return (lines[range.start.line] || '').substring(range.start.character, range.end.character);\r\n                            }\r\n                            const result: string[] = [];\r\n                            for (let i = range.start.line; i <= range.end.line; i++) {\r\n                                const line = lines[i] || '';\r\n                                if (i === range.start.line) {\r\n                                    result.push(line.substring(range.start.character));\r\n                                } else if (i === range.end.line) {\r\n                                    result.push(line.substring(0, range.end.character));\r\n                                } else {\r\n                                    result.push(line);\r\n                                }\r\n                            }\r\n                            return result.join('\\n');\r\n                        },\r\n                        getWordRangeAtPosition: () => new vscode.Range(0, 0, 0, 0),\r\n                        validateRange: (range: vscode.Range) => range,\r\n                        validatePosition: (position: vscode.Position) => position,\r\n                        eol: vscode.EndOfLine.LF\r\n                    };\r\n                    \r\n                    const action = pattern.fix(\r\n                        matches,\r\n                        terminalDocument as unknown as vscode.TextDocument,\r\n                        new vscode.Range(0, 0, terminalDocument.lineCount - 1, output.split('\\n')[output.split('\\n').length - 1].length)\r\n                    );\r\n                    actions.push(action);\r\n                } catch (error) {\r\n                    console.error('Error creating terminal code action:', error);\r\n                }\r\n            }\r\n        }\r\n\r\n        return actions;\r\n    }\r\n\r\n    // Additional comprehensive methods from EnhancedCodeActionProvider\r\n    public getQuickFixIcon(name: string): QuickFixIcon | undefined {\r\n        return this._iconRegistry.get(name);\r\n    }\r\n\r\n    public getAllIcons(): QuickFixIcon[] {\r\n        return Array.from(this._iconRegistry.values());\r\n    }\r\n\r\n    public registerAction(actionId: string, action: vscode.CodeAction): void {\r\n        this._activeActions.set(actionId, action);\r\n    }\r\n\r\n    public unregisterAction(actionId: string): void {\r\n        this._activeActions.delete(actionId);\r\n    }\r\n\r\n    public getAction(actionId: string): vscode.CodeAction | undefined {\r\n        return this._activeActions.get(actionId);\r\n    }\r\n\r\n    public getActionHistory(): CodeActionResult[] {\r\n        return [...this._actionHistory];\r\n    }\r\n\r\n    public clearActionHistory(): void {\r\n        this._actionHistory.length = 0;\r\n    }\r\n\r\n    public async applyQuickFix(action: vscode.CodeAction, document: vscode.TextDocument): Promise<boolean> {\r\n        // Track the action in history\r\n        this._actionHistory.push({\r\n            action,\r\n            priority: 100,\r\n            category: action.kind?.value || 'unknown',\r\n            confidence: 1.0,\r\n            metadata: {\r\n                source: 'quickfix',\r\n                trigger: 'manual',\r\n                context: {\r\n                    document,\r\n                    range: action.diagnostics?.[0]?.range || new vscode.Range(0, 0, 0, 0),\r\n                    diagnostic: action.diagnostics?.[0]\r\n                }\r\n            }\r\n        });\r\n        try {\r\n            if (action.edit) {\r\n                const success = await vscode.workspace.applyEdit(action.edit);\r\n                return success;\r\n            } else if (action.command) {\r\n                await vscode.commands.executeCommand(action.command.command, ...action.command.arguments || []);\r\n                return true;\r\n            } else {\r\n                return false;\r\n            }\r\n        } catch (error) {\r\n            console.error('Error applying quick fix:', error);\r\n            return false;\r\n        }\r\n    }\r\n}\r\n\r\n// Global Code Action Manager\r\nexport class CodeActionManager {\r\n    private static _instance: CodeActionManager;\r\n    private readonly _providers: Map<string, TerminalCodeActionProvider> = new Map();\r\n    private readonly _subscriptions: vscode.Disposable[] = [];\r\n\r\n    private constructor() {\r\n        this._initialize();\r\n    }\r\n\r\n    public static getInstance(): CodeActionManager {\r\n        if (!CodeActionManager._instance) {\r\n            CodeActionManager._instance = new CodeActionManager();\r\n        }\r\n        return CodeActionManager._instance;\r\n    }\r\n\r\n    private _initialize(): void {\r\n        // Register the main code action provider\r\n        const provider = new TerminalCodeActionProvider();\r\n        const disposable = vscode.languages.registerCodeActionsProvider(\r\n            { scheme: 'file', language: '*' },\r\n            provider,\r\n            {\r\n                providedCodeActionKinds: [\r\n                    vscode.CodeActionKind.QuickFix,\r\n                    vscode.CodeActionKind.Refactor,\r\n                    vscode.CodeActionKind.Source\r\n                ]\r\n            }\r\n        );\r\n\r\n        this._subscriptions.push(disposable);\r\n        this._providers.set('main', provider);\r\n\r\n        // Register terminal-specific provider\r\n        const terminalProvider = new TerminalCodeActionProvider();\r\n        this._providers.set('terminal', terminalProvider);\r\n    }\r\n\r\n    public getProvider(name: string): TerminalCodeActionProvider | undefined {\r\n        return this._providers.get(name);\r\n    }\r\n\r\n    public getAllProviders(): TerminalCodeActionProvider[] {\r\n        return Array.from(this._providers.values());\r\n    }\r\n\r\n    public registerProvider(name: string, provider: TerminalCodeActionProvider): void {\r\n        this._providers.set(name, provider);\r\n    }\r\n\r\n    public unregisterProvider(name: string): void {\r\n        const provider = this._providers.get(name);\r\n        if (provider) {\r\n            this._providers.delete(name);\r\n        }\r\n    }\r\n\r\n    public async analyzeCode(document: vscode.TextDocument, range: vscode.Range): Promise<vscode.CodeAction[]> {\r\n        const allActions: vscode.CodeAction[] = [];\r\n        const token = new vscode.CancellationTokenSource().token;\r\n\r\n        for (const provider of this._providers.values()) {\r\n            const context: vscode.CodeActionContext = {\r\n                diagnostics: [],\r\n                triggerKind: vscode.CodeActionTriggerKind.Invoke,\r\n                only: undefined\r\n            };\r\n\r\n            try {\r\n                const actions = provider.provideCodeActions(document, range, context, token);\r\n                if (actions) {\r\n                    if (actions instanceof Promise) {\r\n                        const providerActions = await actions;\r\n                        if (providerActions && Array.isArray(providerActions)) {\r\n                            allActions.push(...providerActions);\r\n                        }\r\n                    } else if (Array.isArray(actions)) {\r\n                        // Filter out Commands and only keep CodeActions\r\n                        const codeActions = actions.filter((action): action is vscode.CodeAction =>\r\n                            action && typeof action === 'object' && 'title' in action && typeof action.title === 'string'\r\n                        );\r\n                        allActions.push(...codeActions);\r\n                    }\r\n                }\r\n            } catch (error) {\r\n                console.error('Error getting code actions from provider:', error);\r\n            }\r\n        }\r\n\r\n        return allActions;\r\n    }\r\n\r\n    public dispose(): void {\r\n        this._subscriptions.forEach(sub => sub.dispose());\r\n        this._subscriptions.length = 0;\r\n        this._providers.clear();\r\n    }\r\n}\r\n\r\n// Export singleton instance\r\nexport const codeActionManager = CodeActionManager.getInstance();\r\n"]}