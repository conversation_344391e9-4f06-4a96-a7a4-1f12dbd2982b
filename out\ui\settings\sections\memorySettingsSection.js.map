{"version": 3, "file": "memorySettingsSection.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/sections/memorySettingsSection.ts"], "names": [], "mappings": ";AAAA,gDAAgD;;AA2ChD,kEA6MC;AA9MD,0CAA0C;AAC1C,SAAgB,2BAA2B,CAAC,SAAsB,EAAE,QAAa;IAC/E,MAAM,cAAc,GAAG;QACrB,OAAO,EAAE,OAAO,QAAQ,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI;QACxE,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,SAAS;QACpC,WAAW,EAAE,OAAO,QAAQ,CAAC,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI;QACnF,kBAAkB,EAAE,OAAO,QAAQ,CAAC,kBAAkB,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC,GAAG;QACvG,iBAAiB,EAAE,OAAO,QAAQ,CAAC,iBAAiB,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;QAClG,uBAAuB,EAAE,OAAO,QAAQ,CAAC,uBAAuB,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,uBAAuB,CAAC,CAAC,CAAC,GAAG;QACtH,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,QAAQ;QAC7C,mBAAmB,EAAE,QAAQ,CAAC,mBAAmB,IAAI;YACnD,MAAM,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE;YAC7C,QAAQ,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE;SACzD;QACD,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,QAAQ;QACvC,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB,IAAI;YAC7C,MAAM,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE;YACxB,KAAK,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;YAChF,QAAQ,EAAE,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;YAC9C,OAAO,EAAE,EAAE,gBAAgB,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE;YAC/D,KAAK,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE;SAClC;QACD,YAAY,EAAE,QAAQ,CAAC,YAAY,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,EAAE,gBAAgB,EAAE,GAAG,EAAE;KACrG,CAAC;IAEF,SAAS,CAAC,SAAS,GAAG;;;;;;;;;;;;;;;;gEAgBwC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;;;;kDAIrD,cAAc,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;gDACvD,cAAc,CAAC,MAAM,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;;;;;;2EAMxB,cAAc,CAAC,WAAW;;;sGAGC,cAAc,CAAC,kBAAkB;;;iFAGtD,cAAc,CAAC,iBAAiB;;;uFAG1B,cAAc,CAAC,uBAAuB;;;;;;iDAM5E,cAAc,CAAC,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;iDACzD,cAAc,CAAC,WAAW,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;mDACvD,cAAc,CAAC,WAAW,KAAK,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;mDAC3D,cAAc,CAAC,WAAW,KAAK,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;kDAC5D,cAAc,CAAC,WAAW,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;;;;;;;;iDAQ3D,cAAc,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;gDACvD,cAAc,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;mDAClD,cAAc,CAAC,QAAQ,KAAK,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;kDACzD,cAAc,CAAC,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;gDACzD,cAAc,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;;;;;;;yFAOZ,cAAc,CAAC,YAAY,CAAC,SAAS;6FACjC,cAAc,CAAC,YAAY,CAAC,YAAY;uGAC9B,cAAc,CAAC,YAAY,CAAC,gBAAgB;;;;;;;;KAQ9I,CAAC;IACJ,2BAA2B;IAC3B,8BAA8B,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;IAC1D,2BAA2B,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;IACvD,+BAA+B;IAC/B;QACE,eAAe,EAAE,cAAc,EAAE,aAAa,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,yBAAyB;QACpH,aAAa,EAAE,UAAU,EAAE,WAAW,EAAE,cAAc,EAAE,kBAAkB;KAC3E,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;QACf,MAAM,EAAE,GAAG,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAC7C,IAAI,EAAE;YAAE,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,0BAA0B,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC;QACjG,IAAI,EAAE,IAAI,EAAE,CAAC,OAAO,KAAK,QAAQ;YAAE,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,0BAA0B,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC;IAC/H,CAAC,CAAC,CAAC;IACH,yBAAyB;IACzB,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,wBAAwB,CAAC,CAAC;IAClE,IAAI,OAAO;QAAE,OAAO,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YAClD,0BAA0B,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YACrD,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE,CAAC;gBAC3C,MAAM,GAAG,GAAG,EAAE,OAAO,EAAE,oBAAoB,EAAE,QAAQ,EAAE,cAAc,EAAE,CAAC;gBACxE,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC;gBAC5C,gBAAgB,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YACtC,CAAC;QACH,CAAC,CAAC,CAAC;IACH,MAAM,QAAQ,GAAG,SAAS,CAAC,aAAa,CAAC,yBAAyB,CAAC,CAAC;IACpE,IAAI,QAAQ;QAAE,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACpD,2BAA2B,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,0BAA0B;QAC9E,CAAC,CAAC,CAAC;IACH,4BAA4B;IAC5B,MAAM,QAAQ,GAAG,SAAS,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;IACjE,IAAI,QAAQ;QAAE,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACpD,IAAI,OAAO,CAAC,4CAA4C,CAAC,EAAE,CAAC;gBAC1D,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE,CAAC;oBAC3C,gBAAgB,EAAE,CAAC,WAAW,CAAC,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC,CAAC;gBAClE,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACH,2BAA2B;IAC3B,SAAS,8BAA8B,CAAC,SAAsB,EAAE,cAAmB;QACjF,MAAM,KAAK,GAAG,SAAS,CAAC,aAAa,CAAC,2BAA2B,CAAC,CAAC;QACnE,IAAI,CAAC,KAAK;YAAE,OAAO;QACnB,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,IAAI,cAAc,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;YAC5C,IAAI,GAAG;kFACqE,cAAc,CAAC,mBAAmB,CAAC,MAAM,CAAC,SAAS;6FACxC,cAAc,CAAC,mBAAmB,CAAC,MAAM,CAAC,cAAc;aACxI,CAAC;QACV,CAAC;aAAM,IAAI,cAAc,CAAC,WAAW,KAAK,UAAU,EAAE,CAAC;YACrD,IAAI,GAAG;+EACkE,cAAc,CAAC,mBAAmB,CAAC,QAAQ,CAAC,MAAM;wFACzC,cAAc,CAAC,mBAAmB,CAAC,QAAQ,CAAC,WAAW;qFAC1D,cAAc,CAAC,mBAAmB,CAAC,QAAQ,CAAC,SAAS;aAC7H,CAAC;QACV,CAAC;aAAM,CAAC;YACN,IAAI,GAAG,8EAA8E,CAAC;QACxF,CAAC;QACD,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;QACvB,CAAE,iBAAiB,EAAE,sBAAsB,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,mBAAmB,CAAE,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YACvH,MAAM,EAAE,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACzC,IAAI,EAAE;gBAAE,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,0BAA0B,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC;IACL,CAAC;IACD,SAAS,2BAA2B,CAAC,SAAsB,EAAE,cAAmB;QAC9E,MAAM,KAAK,GAAG,SAAS,CAAC,aAAa,CAAC,wBAAwB,CAAC,CAAC;QAChE,IAAI,CAAC,KAAK;YAAE,OAAO;QACnB,IAAI,IAAI,GAAG,EAAE,CAAC;QACd,IAAI,cAAc,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YACzC,IAAI,GAAG,iEAAiE,cAAc,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,cAAc,CAAC;QACxI,CAAC;aAAM,IAAI,cAAc,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YAC/C,IAAI,GAAG;uEAC0D,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI;yEACxC,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI;uEAC5C,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI;mFAC9B,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ;+EAClD,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,QAAQ;yEACpD,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,KAAK;aACvG,CAAC;QACV,CAAC;aAAM,IAAI,cAAc,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YAClD,IAAI,GAAG;mGACsF,cAAc,CAAC,gBAAgB,CAAC,QAAQ,CAAC,gBAAgB;8EAC9E,cAAc,CAAC,gBAAgB,CAAC,QAAQ,CAAC,MAAM;aAChH,CAAC;QACV,CAAC;aAAM,IAAI,cAAc,CAAC,QAAQ,KAAK,SAAS,EAAE,CAAC;YACjD,IAAI,GAAG;kGACqF,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,gBAAgB;iFACzE,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ;qFAC5C,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAU;aAC1H,CAAC;QACV,CAAC;aAAM,IAAI,cAAc,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;YAC/C,IAAI,GAAG;qEACwD,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG;kFAC5B,cAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,SAAS;aACpH,CAAC;QACV,CAAC;aAAM,CAAC;YACN,IAAI,GAAG,0EAA0E,CAAC;QACpF,CAAC;QACD,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;QACvB,CAAE,gBAAgB,EAAE,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,eAAe,EAAE,YAAY,EAAE,0BAA0B,EAAE,gBAAgB,EAAE,yBAAyB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,UAAU,EAAE,gBAAgB,CAAE,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YACtQ,MAAM,EAAE,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACzC,IAAI,EAAE;gBAAE,EAAE,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,0BAA0B,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC;QACnG,CAAC,CAAC,CAAC;IACL,CAAC;IACD,wDAAwD;IACxD,MAAM,iBAAiB,GAAG,SAAS,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;IAClE,IAAI,iBAAiB;QAAE,iBAAiB,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,8BAA8B,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC,CAAC;IACrI,MAAM,cAAc,GAAG,SAAS,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;IAC5D,IAAI,cAAc;QAAE,cAAc,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,2BAA2B,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC,CAAC;AAC9H,CAAC;AAED,SAAS,0BAA0B,CAAC,QAAa,EAAE,cAAmB;IACpE,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAA4B,CAAC;IACzF,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAA6B,CAAC;IACxF,MAAM,gBAAgB,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAA4B,CAAC;IAC3F,MAAM,uBAAuB,GAAG,QAAQ,CAAC,cAAc,CAAC,oBAAoB,CAA4B,CAAC;IACzG,MAAM,sBAAsB,GAAG,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAA4B,CAAC;IACvG,MAAM,4BAA4B,GAAG,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAA4B,CAAC;IACnH,MAAM,gBAAgB,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAA6B,CAAC;IAC5F,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAA6B,CAAC;IACtF,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,WAAW,CAA4B,CAAC;IACvF,MAAM,iBAAiB,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAA4B,CAAC;IAC7F,MAAM,qBAAqB,GAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAA4B,CAAC;IACrG,kBAAkB;IAClB,MAAM,oBAAoB,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAA4B,CAAC;IACnG,MAAM,yBAAyB,GAAG,QAAQ,CAAC,cAAc,CAAC,sBAAsB,CAA4B,CAAC;IAC7G,MAAM,mBAAmB,GAAG,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAA4B,CAAC;IACjG,MAAM,wBAAwB,GAAG,QAAQ,CAAC,cAAc,CAAC,qBAAqB,CAA4B,CAAC;IAC3G,MAAM,sBAAsB,GAAG,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAA4B,CAAC;IACvG,MAAM,mBAAmB,GAAG,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAA4B,CAAC;IACjG,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,WAAW,CAA4B,CAAC;IACvF,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,WAAW,CAA4B,CAAC;IACvF,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,WAAW,CAA4B,CAAC;IACvF,MAAM,kBAAkB,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAA4B,CAAC;IAC/F,MAAM,kBAAkB,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAA4B,CAAC;IAC/F,MAAM,eAAe,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAA4B,CAAC;IACzF,MAAM,6BAA6B,GAAG,QAAQ,CAAC,cAAc,CAAC,0BAA0B,CAA4B,CAAC;IACrH,MAAM,mBAAmB,GAAG,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAA4B,CAAC;IACjG,MAAM,4BAA4B,GAAG,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAA4B,CAAC;IACnH,MAAM,oBAAoB,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAA4B,CAAC;IACnG,MAAM,sBAAsB,GAAG,QAAQ,CAAC,cAAc,CAAC,mBAAmB,CAA4B,CAAC;IACvG,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAA4B,CAAC;IACrF,MAAM,mBAAmB,GAAG,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAA4B,CAAC;IACjG,cAAc,GAAG;QACf,OAAO,EAAE,CAAC,CAAC,YAAY,EAAE,OAAO;QAChC,MAAM,EAAG,WAAW,EAAE,KAA6B,IAAI,SAAS;QAChE,WAAW,EAAE,QAAQ,CAAC,gBAAgB,EAAE,KAAK,IAAI,MAAM,EAAE,EAAE,CAAC;QAC5D,kBAAkB,EAAE,UAAU,CAAC,uBAAuB,EAAE,KAAK,IAAI,KAAK,CAAC;QACvE,iBAAiB,EAAE,QAAQ,CAAC,sBAAsB,EAAE,KAAK,IAAI,GAAG,EAAE,EAAE,CAAC;QACrE,uBAAuB,EAAE,QAAQ,CAAC,4BAA4B,EAAE,KAAK,IAAI,KAAK,EAAE,EAAE,CAAC;QACnF,WAAW,EAAG,gBAAgB,EAAE,KAAuC,IAAI,QAAQ;QACnF,mBAAmB,EAAE;YACnB,MAAM,EAAE;gBACN,SAAS,EAAE,oBAAoB,EAAE,KAAK,IAAI,EAAE;gBAC5C,cAAc,EAAE,yBAAyB,EAAE,KAAK,IAAI,EAAE;aACvD;YACD,QAAQ,EAAE;gBACR,MAAM,EAAE,mBAAmB,EAAE,KAAK,IAAI,EAAE;gBACxC,WAAW,EAAE,wBAAwB,EAAE,KAAK,IAAI,EAAE;gBAClD,SAAS,EAAE,sBAAsB,EAAE,KAAK,IAAI,EAAE;aAC/C;SACF;QACD,QAAQ,EAAG,aAAa,EAAE,KAAoC,IAAI,QAAQ;QAC1E,gBAAgB,EAAE;YAChB,MAAM,EAAE,EAAE,QAAQ,EAAE,mBAAmB,EAAE,KAAK,IAAI,EAAE,EAAE;YACtD,KAAK,EAAE;gBACL,IAAI,EAAE,cAAc,EAAE,KAAK,IAAI,EAAE;gBACjC,IAAI,EAAE,QAAQ,CAAC,cAAc,EAAE,KAAK,IAAI,MAAM,EAAE,EAAE,CAAC;gBACnD,IAAI,EAAE,cAAc,EAAE,KAAK,IAAI,EAAE;gBACjC,QAAQ,EAAE,kBAAkB,EAAE,KAAK,IAAI,EAAE;gBACzC,QAAQ,EAAE,kBAAkB,EAAE,KAAK,IAAI,EAAE;gBACzC,KAAK,EAAE,eAAe,EAAE,KAAK,IAAI,EAAE;aACpC;YACD,QAAQ,EAAE;gBACR,gBAAgB,EAAE,6BAA6B,EAAE,KAAK,IAAI,EAAE;gBAC5D,MAAM,EAAE,mBAAmB,EAAE,KAAK,IAAI,EAAE;aACzC;YACD,OAAO,EAAE;gBACP,gBAAgB,EAAE,4BAA4B,EAAE,KAAK,IAAI,EAAE;gBAC3D,QAAQ,EAAE,oBAAoB,EAAE,KAAK,IAAI,EAAE;gBAC3C,UAAU,EAAE,sBAAsB,EAAE,KAAK,IAAI,EAAE;aAChD;YACD,KAAK,EAAE;gBACL,GAAG,EAAE,aAAa,EAAE,KAAK,IAAI,EAAE;gBAC/B,SAAS,EAAE,mBAAmB,EAAE,KAAK,IAAI,EAAE;aAC5C;SACF;QACD,YAAY,EAAE;YACZ,SAAS,EAAE,QAAQ,CAAC,cAAc,EAAE,KAAK,IAAI,MAAM,EAAE,EAAE,CAAC;YACxD,YAAY,EAAE,QAAQ,CAAC,iBAAiB,EAAE,KAAK,IAAI,KAAK,EAAE,EAAE,CAAC;YAC7D,gBAAgB,EAAE,QAAQ,CAAC,qBAAqB,EAAE,KAAK,IAAI,KAAK,EAAE,EAAE,CAAC;SACtE;KACF,CAAC;IACF,oCAAoC;IACpC,QAAQ,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;IAC1C,QAAQ,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;IACxC,QAAQ,CAAC,WAAW,GAAG,cAAc,CAAC,WAAW,CAAC;IAClD,QAAQ,CAAC,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;IAChE,QAAQ,CAAC,iBAAiB,GAAG,cAAc,CAAC,iBAAiB,CAAC;IAC9D,QAAQ,CAAC,uBAAuB,GAAG,cAAc,CAAC,uBAAuB,CAAC;IAC1E,QAAQ,CAAC,WAAW,GAAG,cAAc,CAAC,WAAW,CAAC;IAClD,QAAQ,CAAC,mBAAmB,GAAG,cAAc,CAAC,mBAAmB,CAAC;IAClE,QAAQ,CAAC,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC;IAC5C,QAAQ,CAAC,gBAAgB,GAAG,cAAc,CAAC,gBAAgB,CAAC;IAC5D,QAAQ,CAAC,YAAY,GAAG,cAAc,CAAC,YAAY,CAAC;AACtD,CAAC", "sourcesContent": ["// Memory Management section logic and rendering\n\n// Modern, accessible, production-ready memory settings UI\n\ndeclare function acquireVsCodeApi(): { postMessage: (msg: any) => void };\n\n\n\nexport type MemorySettings = {\n    enabled: boolean;\n    system: 'basic' | 'codessa';\n    maxMemories: number;\n    relevanceThreshold: number;\n    contextWindowSize: number;\n    conversationHistorySize: number;\n    vectorStore: 'memory' | 'chroma' | 'pinecone' | 'weaviate' | 'hnswlib';\n    vectorStoreSettings: {\n        chroma: {\n            directory: string;\n            collectionName: string;\n        };\n        pinecone: {\n            apiKey: string;\n            environment: string;\n            indexName: string;\n        };\n    };\n    database: 'sqlite' | 'mysql' | 'postgres' | 'mongodb' | 'redis';\n    databaseSettings: {\n        sqlite: { filename: string };\n        mysql: { host: string; port: number; user: string; password: string; database: string; table: string };\n        postgres: { connectionString: string; schema: string };\n        mongodb: { connectionString: string; database: string; collection: string };\n        redis: { url: string; keyPrefix: string };\n    };\n    fileChunking: {\n        chunkSize: number;\n        chunkOverlap: number;\n        maxChunksPerFile: number;\n    };\n};\n\n// Modern memory settings section renderer\nexport function renderMemorySettingsSection(container: HTMLElement, settings: any) {\n  const memorySettings = {\n    enabled: typeof settings.enabled === 'boolean' ? settings.enabled : true,\n    system: settings.system || 'codessa',\n    maxMemories: typeof settings.maxMemories === 'number' ? settings.maxMemories : 1000,\n    relevanceThreshold: typeof settings.relevanceThreshold === 'number' ? settings.relevanceThreshold : 0.7,\n    contextWindowSize: typeof settings.contextWindowSize === 'number' ? settings.contextWindowSize : 5,\n    conversationHistorySize: typeof settings.conversationHistorySize === 'number' ? settings.conversationHistorySize : 100,\n    vectorStore: settings.vectorStore || 'chroma',\n    vectorStoreSettings: settings.vectorStoreSettings || {\n      chroma: { directory: '', collectionName: '' },\n      pinecone: { apiKey: '', environment: '', indexName: '' }\n    },\n    database: settings.database || 'sqlite',\n    databaseSettings: settings.databaseSettings || {\n      sqlite: { filename: '' },\n      mysql: { host: '', port: 3306, user: '', password: '', database: '', table: '' },\n      postgres: { connectionString: '', schema: '' },\n      mongodb: { connectionString: '', database: '', collection: '' },\n      redis: { url: '', keyPrefix: '' }\n    },\n    fileChunking: settings.fileChunking || { chunkSize: 1000, chunkOverlap: 200, maxChunksPerFile: 100 }\n  };\n\n  container.innerHTML = `\n        <style>\n            .mem-section { max-width:520px; margin:0 auto; padding:20px 0; }\n            .mem-title { font-size:1.18em; font-weight:600; margin-bottom:14px; color:#222; }\n            .mem-group { margin-bottom:20px; padding:14px 16px; background:#f8fafc; border-radius:8px; }\n            .mem-label { font-weight:500; display:block; margin-bottom:5px; color:#374151; }\n            .mem-btn-row { display:flex; gap:14px; margin-top:22px; }\n            .mem-btn { padding:7px 22px; border-radius:6px; border:none; font-weight:500; font-size:1em; cursor:pointer; }\n            .mem-btn.save { background:#2563eb; color:#fff; }\n            .mem-btn.reset { background:#f3f4f6; color:#222; border:1px solid #e5e7eb; }\n            .mem-btn.clear { background:#fff0f0; color:#b91c1c; border:1px solid #e5e7eb; margin-left:auto; }\n        </style>\n        <div class=\"mem-section\" role=\"region\" aria-labelledby=\"mem-title\">\n            <div class=\"mem-title\" id=\"mem-title\">Memory Settings</div>\n            <div class=\"mem-group\">\n                <label class=\"mem-label\" for=\"memoryEnabled\">Enable Memory\n                    <input id=\"memoryEnabled\" type=\"checkbox\" ${memorySettings.enabled ? 'checked' : ''} />\n                </label>\n                <label class=\"mem-label\" for=\"memorySystem\">Memory System\n                    <select id=\"memorySystem\">\n                        <option value=\"codessa\" ${memorySettings.system === 'codessa' ? 'selected' : ''}>Codessa</option>\n                        <option value=\"basic\" ${memorySettings.system === 'basic' ? 'selected' : ''}>Basic</option>\n                    </select>\n                </label>\n            </div>\n            <div class=\"mem-group\">\n                <label class=\"mem-label\" for=\"maxMemories\">Max Memories\n                    <input id=\"maxMemories\" type=\"number\" min=\"1\" value=\"${memorySettings.maxMemories}\" />\n                </label>\n                <label class=\"mem-label\" for=\"relevanceThreshold\">Relevance Threshold\n                    <input id=\"relevanceThreshold\" type=\"number\" min=\"0\" max=\"1\" step=\"0.01\" value=\"${memorySettings.relevanceThreshold}\" />\n                </label>\n                <label class=\"mem-label\" for=\"contextWindowSize\">Context Window Size\n                    <input id=\"contextWindowSize\" type=\"number\" min=\"1\" value=\"${memorySettings.contextWindowSize}\" />\n                </label>\n                <label class=\"mem-label\" for=\"conversationHistorySize\">Conversation History Size\n                    <input id=\"conversationHistorySize\" type=\"number\" min=\"1\" value=\"${memorySettings.conversationHistorySize}\" />\n                </label>\n            </div>\n            <div class=\"mem-group\">\n                <label class=\"mem-label\" for=\"vectorStore\">Vector Store\n                    <select id=\"vectorStore\">\n                        <option value=\"memory\" ${memorySettings.vectorStore === 'memory' ? 'selected' : ''}>In-Memory</option>\n                        <option value=\"chroma\" ${memorySettings.vectorStore === 'chroma' ? 'selected' : ''}>Chroma</option>\n                        <option value=\"pinecone\" ${memorySettings.vectorStore === 'pinecone' ? 'selected' : ''}>Pinecone</option>\n                        <option value=\"weaviate\" ${memorySettings.vectorStore === 'weaviate' ? 'selected' : ''}>Weaviate</option>\n                        <option value=\"hnswlib\" ${memorySettings.vectorStore === 'hnswlib' ? 'selected' : ''}>HNSWLib</option>\n                    </select>\n                </label>\n                <div id=\"vectorStoreSettingsPanel\"></div>\n            </div>\n            <div class=\"mem-group\">\n                <label class=\"mem-label\" for=\"database\">Database\n                    <select id=\"database\">\n                        <option value=\"sqlite\" ${memorySettings.database === 'sqlite' ? 'selected' : ''}>SQLite</option>\n                        <option value=\"mysql\" ${memorySettings.database === 'mysql' ? 'selected' : ''}>MySQL</option>\n                        <option value=\"postgres\" ${memorySettings.database === 'postgres' ? 'selected' : ''}>Postgres</option>\n                        <option value=\"mongodb\" ${memorySettings.database === 'mongodb' ? 'selected' : ''}>MongoDB</option>\n                        <option value=\"redis\" ${memorySettings.database === 'redis' ? 'selected' : ''}>Redis</option>\n                    </select>\n                </label>\n                <div id=\"databaseSettingsPanel\"></div>\n            </div>\n            <div class=\"mem-group\">\n                <label class=\"mem-label\">File Chunking</label>\n                <label>Chunk Size <input id=\"chunkSize\" type=\"number\" min=\"100\" value=\"${memorySettings.fileChunking.chunkSize}\" /></label>\n                <label>Chunk Overlap <input id=\"chunkOverlap\" type=\"number\" min=\"0\" value=\"${memorySettings.fileChunking.chunkOverlap}\" /></label>\n                <label>Max Chunks Per File <input id=\"maxChunksPerFile\" type=\"number\" min=\"1\" value=\"${memorySettings.fileChunking.maxChunksPerFile}\" /></label>\n            </div>\n            <div class=\"mem-btn-row\">\n                <button class=\"mem-btn save\" id=\"saveMemorySettingsBtn\" type=\"button\">Save</button>\n                <button class=\"mem-btn reset\" id=\"resetMemorySettingsBtn\" type=\"button\">Reset</button>\n                <button class=\"mem-btn clear\" id=\"clearAllMemoriesBtn\" type=\"button\">Clear All Memories</button>\n            </div>\n        </div>\n    `;\n  // Advanced settings panels\n  renderVectorStoreSettingsPanel(container, memorySettings);\n  renderDatabaseSettingsPanel(container, memorySettings);\n  // Add listeners for all fields\n  [\n    'memoryEnabled', 'memorySystem', 'maxMemories', 'relevanceThreshold', 'contextWindowSize', 'conversationHistorySize',\n    'vectorStore', 'database', 'chunkSize', 'chunkOverlap', 'maxChunksPerFile'\n  ].forEach((id) => {\n    const el = container.querySelector(`#${id}`);\n    if (el) el.addEventListener('input', () => updateMemorySettingsFromUI(settings, memorySettings));\n    if (el && el.tagName === 'SELECT') el.addEventListener('change', () => updateMemorySettingsFromUI(settings, memorySettings));\n  });\n  // Save and Reset buttons\n  const saveBtn = container.querySelector('#saveMemorySettingsBtn');\n  if (saveBtn) saveBtn.addEventListener('click', () => {\n    updateMemorySettingsFromUI(settings, memorySettings);\n    if (typeof acquireVsCodeApi === 'function') {\n      const msg = { command: 'saveMemorySettings', settings: memorySettings };\n      console.log('Saving memory settings:', msg);\n      acquireVsCodeApi().postMessage(msg);\n    }\n  });\n  const resetBtn = container.querySelector('#resetMemorySettingsBtn');\n  if (resetBtn) resetBtn.addEventListener('click', () => {\n    renderMemorySettingsSection(container, settings); // Re-render from settings\n  });\n  // Clear all memories button\n  const clearBtn = container.querySelector('#clearAllMemoriesBtn');\n  if (clearBtn) clearBtn.addEventListener('click', () => {\n    if (confirm('Clear all memories? This cannot be undone.')) {\n      if (typeof acquireVsCodeApi === 'function') {\n        acquireVsCodeApi().postMessage({ command: 'clearAllMemories' });\n      }\n    }\n  });\n  // Advanced panel listeners\n  function renderVectorStoreSettingsPanel(container: HTMLElement, memorySettings: any) {\n    const panel = container.querySelector('#vectorStoreSettingsPanel');\n    if (!panel) return;\n    let html = '';\n    if (memorySettings.vectorStore === 'chroma') {\n      html = `\n                <label>Directory <input id=\"chromaDirectory\" type=\"text\" value=\"${memorySettings.vectorStoreSettings.chroma.directory}\" /></label>\n                <label>Collection Name <input id=\"chromaCollectionName\" type=\"text\" value=\"${memorySettings.vectorStoreSettings.chroma.collectionName}\" /></label>\n            `;\n    } else if (memorySettings.vectorStore === 'pinecone') {\n      html = `\n                <label>API Key <input id=\"pineconeApiKey\" type=\"text\" value=\"${memorySettings.vectorStoreSettings.pinecone.apiKey}\" /></label>\n                <label>Environment <input id=\"pineconeEnvironment\" type=\"text\" value=\"${memorySettings.vectorStoreSettings.pinecone.environment}\" /></label>\n                <label>Index Name <input id=\"pineconeIndexName\" type=\"text\" value=\"${memorySettings.vectorStoreSettings.pinecone.indexName}\" /></label>\n            `;\n    } else {\n      html = '<div style=\"color:#888;\">No additional settings for this vector store.</div>';\n    }\n    panel.innerHTML = html;\n    [ 'chromaDirectory', 'chromaCollectionName', 'pineconeApiKey', 'pineconeEnvironment', 'pineconeIndexName' ].forEach(id => {\n      const el = panel.querySelector(`#${id}`);\n      if (el) el.addEventListener('input', () => updateMemorySettingsFromUI(settings, memorySettings));\n    });\n  }\n  function renderDatabaseSettingsPanel(container: HTMLElement, memorySettings: any) {\n    const panel = container.querySelector('#databaseSettingsPanel');\n    if (!panel) return;\n    let html = '';\n    if (memorySettings.database === 'sqlite') {\n      html = `<label>Filename <input id=\"sqliteFilename\" type=\"text\" value=\"${memorySettings.databaseSettings.sqlite.filename}\" /></label>`;\n    } else if (memorySettings.database === 'mysql') {\n      html = `\n                <label>Host <input id=\"mysqlHost\" type=\"text\" value=\"${memorySettings.databaseSettings.mysql.host}\" /></label>\n                <label>Port <input id=\"mysqlPort\" type=\"number\" value=\"${memorySettings.databaseSettings.mysql.port}\" /></label>\n                <label>User <input id=\"mysqlUser\" type=\"text\" value=\"${memorySettings.databaseSettings.mysql.user}\" /></label>\n                <label>Password <input id=\"mysqlPassword\" type=\"password\" value=\"${memorySettings.databaseSettings.mysql.password}\" /></label>\n                <label>Database <input id=\"mysqlDatabase\" type=\"text\" value=\"${memorySettings.databaseSettings.mysql.database}\" /></label>\n                <label>Table <input id=\"mysqlTable\" type=\"text\" value=\"${memorySettings.databaseSettings.mysql.table}\" /></label>\n            `;\n    } else if (memorySettings.database === 'postgres') {\n      html = `\n                <label>Connection String <input id=\"postgresConnectionString\" type=\"text\" value=\"${memorySettings.databaseSettings.postgres.connectionString}\" /></label>\n                <label>Schema <input id=\"postgresSchema\" type=\"text\" value=\"${memorySettings.databaseSettings.postgres.schema}\" /></label>\n            `;\n    } else if (memorySettings.database === 'mongodb') {\n      html = `\n                <label>Connection String <input id=\"mongodbConnectionString\" type=\"text\" value=\"${memorySettings.databaseSettings.mongodb.connectionString}\" /></label>\n                <label>Database <input id=\"mongodbDatabase\" type=\"text\" value=\"${memorySettings.databaseSettings.mongodb.database}\" /></label>\n                <label>Collection <input id=\"mongodbCollection\" type=\"text\" value=\"${memorySettings.databaseSettings.mongodb.collection}\" /></label>\n            `;\n    } else if (memorySettings.database === 'redis') {\n      html = `\n                <label>URL <input id=\"redisUrl\" type=\"text\" value=\"${memorySettings.databaseSettings.redis.url}\" /></label>\n                <label>Key Prefix <input id=\"redisKeyPrefix\" type=\"text\" value=\"${memorySettings.databaseSettings.redis.keyPrefix}\" /></label>\n            `;\n    } else {\n      html = '<div style=\"color:#888;\">No additional settings for this database.</div>';\n    }\n    panel.innerHTML = html;\n    [ 'sqliteFilename', 'mysqlHost', 'mysqlPort', 'mysqlUser', 'mysqlPassword', 'mysqlDatabase', 'mysqlTable', 'postgresConnectionString', 'postgresSchema', 'mongodbConnectionString', 'mongodbDatabase', 'mongodbCollection', 'redisUrl', 'redisKeyPrefix' ].forEach(id => {\n      const el = panel.querySelector(`#${id}`);\n      if (el) el.addEventListener('input', () => updateMemorySettingsFromUI(settings, memorySettings));\n    });\n  }\n  // Re-render advanced settings panels on dropdown change\n  const vectorStoreSelect = container.querySelector('#vectorStore');\n  if (vectorStoreSelect) vectorStoreSelect.addEventListener('change', () => renderVectorStoreSettingsPanel(container, memorySettings));\n  const databaseSelect = container.querySelector('#database');\n  if (databaseSelect) databaseSelect.addEventListener('change', () => renderDatabaseSettingsPanel(container, memorySettings));\n}\n\nfunction updateMemorySettingsFromUI(settings: any, memorySettings: any) {\n  const enabledInput = document.getElementById('memoryEnabled') as HTMLInputElement | null;\n  const systemInput = document.getElementById('memorySystem') as HTMLSelectElement | null;\n  const maxMemoriesInput = document.getElementById('maxMemories') as HTMLInputElement | null;\n  const relevanceThresholdInput = document.getElementById('relevanceThreshold') as HTMLInputElement | null;\n  const contextWindowSizeInput = document.getElementById('contextWindowSize') as HTMLInputElement | null;\n  const conversationHistorySizeInput = document.getElementById('conversationHistorySize') as HTMLInputElement | null;\n  const vectorStoreInput = document.getElementById('vectorStore') as HTMLSelectElement | null;\n  const databaseInput = document.getElementById('database') as HTMLSelectElement | null;\n  const chunkSizeInput = document.getElementById('chunkSize') as HTMLInputElement | null;\n  const chunkOverlapInput = document.getElementById('chunkOverlap') as HTMLInputElement | null;\n  const maxChunksPerFileInput = document.getElementById('maxChunksPerFile') as HTMLInputElement | null;\n  // Advanced panels\n  const chromaDirectoryInput = document.getElementById('chromaDirectory') as HTMLInputElement | null;\n  const chromaCollectionNameInput = document.getElementById('chromaCollectionName') as HTMLInputElement | null;\n  const pineconeApiKeyInput = document.getElementById('pineconeApiKey') as HTMLInputElement | null;\n  const pineconeEnvironmentInput = document.getElementById('pineconeEnvironment') as HTMLInputElement | null;\n  const pineconeIndexNameInput = document.getElementById('pineconeIndexName') as HTMLInputElement | null;\n  const sqliteFilenameInput = document.getElementById('sqliteFilename') as HTMLInputElement | null;\n  const mysqlHostInput = document.getElementById('mysqlHost') as HTMLInputElement | null;\n  const mysqlPortInput = document.getElementById('mysqlPort') as HTMLInputElement | null;\n  const mysqlUserInput = document.getElementById('mysqlUser') as HTMLInputElement | null;\n  const mysqlPasswordInput = document.getElementById('mysqlPassword') as HTMLInputElement | null;\n  const mysqlDatabaseInput = document.getElementById('mysqlDatabase') as HTMLInputElement | null;\n  const mysqlTableInput = document.getElementById('mysqlTable') as HTMLInputElement | null;\n  const postgresConnectionStringInput = document.getElementById('postgresConnectionString') as HTMLInputElement | null;\n  const postgresSchemaInput = document.getElementById('postgresSchema') as HTMLInputElement | null;\n  const mongodbConnectionStringInput = document.getElementById('mongodbConnectionString') as HTMLInputElement | null;\n  const mongodbDatabaseInput = document.getElementById('mongodbDatabase') as HTMLInputElement | null;\n  const mongodbCollectionInput = document.getElementById('mongodbCollection') as HTMLInputElement | null;\n  const redisUrlInput = document.getElementById('redisUrl') as HTMLInputElement | null;\n  const redisKeyPrefixInput = document.getElementById('redisKeyPrefix') as HTMLInputElement | null;\n  memorySettings = {\n    enabled: !!enabledInput?.checked,\n    system: (systemInput?.value as 'basic' | 'codessa') || 'codessa',\n    maxMemories: parseInt(maxMemoriesInput?.value || '1000', 10),\n    relevanceThreshold: parseFloat(relevanceThresholdInput?.value || '0.7'),\n    contextWindowSize: parseInt(contextWindowSizeInput?.value || '5', 10),\n    conversationHistorySize: parseInt(conversationHistorySizeInput?.value || '100', 10),\n    vectorStore: (vectorStoreInput?.value as MemorySettings['vectorStore']) || 'chroma',\n    vectorStoreSettings: {\n      chroma: {\n        directory: chromaDirectoryInput?.value || '',\n        collectionName: chromaCollectionNameInput?.value || ''\n      },\n      pinecone: {\n        apiKey: pineconeApiKeyInput?.value || '',\n        environment: pineconeEnvironmentInput?.value || '',\n        indexName: pineconeIndexNameInput?.value || ''\n      }\n    },\n    database: (databaseInput?.value as MemorySettings['database']) || 'sqlite',\n    databaseSettings: {\n      sqlite: { filename: sqliteFilenameInput?.value || '' },\n      mysql: {\n        host: mysqlHostInput?.value || '',\n        port: parseInt(mysqlPortInput?.value || '3306', 10),\n        user: mysqlUserInput?.value || '',\n        password: mysqlPasswordInput?.value || '',\n        database: mysqlDatabaseInput?.value || '',\n        table: mysqlTableInput?.value || ''\n      },\n      postgres: {\n        connectionString: postgresConnectionStringInput?.value || '',\n        schema: postgresSchemaInput?.value || ''\n      },\n      mongodb: {\n        connectionString: mongodbConnectionStringInput?.value || '',\n        database: mongodbDatabaseInput?.value || '',\n        collection: mongodbCollectionInput?.value || ''\n      },\n      redis: {\n        url: redisUrlInput?.value || '',\n        keyPrefix: redisKeyPrefixInput?.value || ''\n      }\n    },\n    fileChunking: {\n      chunkSize: parseInt(chunkSizeInput?.value || '1000', 10),\n      chunkOverlap: parseInt(chunkOverlapInput?.value || '200', 10),\n      maxChunksPerFile: parseInt(maxChunksPerFileInput?.value || '100', 10)\n    }\n  };\n  // Sync back to main settings object\n  settings.enabled = memorySettings.enabled;\n  settings.system = memorySettings.system;\n  settings.maxMemories = memorySettings.maxMemories;\n  settings.relevanceThreshold = memorySettings.relevanceThreshold;\n  settings.contextWindowSize = memorySettings.contextWindowSize;\n  settings.conversationHistorySize = memorySettings.conversationHistorySize;\n  settings.vectorStore = memorySettings.vectorStore;\n  settings.vectorStoreSettings = memorySettings.vectorStoreSettings;\n  settings.database = memorySettings.database;\n  settings.databaseSettings = memorySettings.databaseSettings;\n  settings.fileChunking = memorySettings.fileChunking;\n}\n\n"]}