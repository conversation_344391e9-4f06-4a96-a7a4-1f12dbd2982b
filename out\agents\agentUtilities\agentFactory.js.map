{"version": 3, "file": "agentFactory.js", "sourceRoot": "", "sources": ["../../../src/agents/agentUtilities/agentFactory.ts"], "names": [], "mappings": ";;AAYA,0CA0BC;AAtCD,mCAAgC;AAEhC,yCAAsC;AACtC,uDAAoD;AACpD,uDAAoD;AACpD,yDAAsD;AACtD,+DAA4D;AAC5D,+DAA4D;AAE5D;;GAEG;AACH,SAAgB,eAAe,CAAC,IAAY,EAAE,OAAgC;IAC5E,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,MAAM;YACT,OAAO,IAAI,qBAAS,CAAC,OAAO,CAAC,CAAC;QAChC,KAAK,MAAM;YACT,OAAO,IAAI,qBAAS,CAAC,OAAO,CAAC,CAAC;QAChC,KAAK,OAAO;YACV,OAAO,IAAI,uBAAU,CAAC,OAAO,CAAC,CAAC;QACjC,KAAK,UAAU;YACb,OAAO,IAAI,6BAAa,CAAC,OAAO,CAAC,CAAC;QACpC,KAAK,UAAU;YACb,OAAO,IAAI,6BAAa,CAAC,OAAO,CAAC,CAAC;QACpC;YACE,0CAA0C;YAC1C,eAAM,CAAC,IAAI,CAAC,8CAA8C,IAAI,wBAAwB,CAAC,CAAC;YACxF,OAAO,IAAI,aAAK,CAAC;gBACf,EAAE,EAAG,OAAmC,CAAC,EAAY,IAAI,SAAS,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC9E,IAAI,EAAG,OAAmC,CAAC,IAAc,IAAI,SAAS,IAAI,EAAE;gBAC5E,gBAAgB,EAAG,OAAmC,CAAC,gBAA0B,IAAI,SAAS;gBAC9F,GAAG,OAAO;gBACV,IAAI,EAAE,WAAW;gBACjB,YAAY,EAAE,CAAC,MAAM,CAAC;gBACtB,WAAW,EAAE,QAAQ;gBACrB,QAAQ,EAAE,OAAO;aACe,CAAC,CAAC;IACxC,CAAC;AACH,CAAC", "sourcesContent": ["import { Agent } from './agent';\n\nimport { logger } from '../../logger';\nimport { ChatAgent } from '../agentTypes/chatAgent';\nimport { EditAgent } from '../agentTypes/editAgent';\nimport { DebugAgent } from '../agentTypes/debugAgent';\nimport { RefactorAgent } from '../agentTypes/refactorAgent';\nimport { DocumentAgent } from '../agentTypes/documentAgent';\n\n/**\n * Factory function to create the appropriate specialized agent for a mode\n */\nexport function createModeAgent(mode: string, _config: Record<string, unknown>): Agent {\n  switch (mode) {\n    case 'chat':\n      return new ChatAgent(_config);\n    case 'edit':\n      return new EditAgent(_config);\n    case 'debug':\n      return new DebugAgent(_config);\n    case 'refactor':\n      return new RefactorAgent(_config);\n    case 'document':\n      return new DocumentAgent(_config);\n    default:\n      // For other modes, create a generic agent\n      logger.info(`No specialized agent class found for mode: ${mode}. Using generic Agent.`);\n      return new Agent({\n        id: (_config as Record<string, unknown>).id as string || `agent-${Date.now()}`,\n        name: (_config as Record<string, unknown>).name as string || `Agent-${mode}`,\n        systemPromptName: (_config as Record<string, unknown>).systemPromptName as string || 'default',\n        ..._config,\n        role: 'assistant',\n        capabilities: ['chat'],\n        llmProvider: 'openai',\n        llmModel: 'gpt-4'\n      } as import('./agent').AgentConfig);\n  }\n}"]}