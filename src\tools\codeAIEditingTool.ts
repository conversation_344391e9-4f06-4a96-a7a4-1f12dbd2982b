// ai-universal-code-editor.ts
import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { createPatch, applyPatch, parsePatch, ParsedDiff } from 'diff';
import * as readline from 'readline';
import * as fsPromises from 'fs/promises';
import * as ParserModule from 'web-tree-sitter';
import { string } from 'zod';
import type * as Prettier from 'prettier';


// Re-export types from web-tree-sitter
type Parser = ParserModule.Parser;
type Language = ParserModule.Language;
type SyntaxNode = ParserModule.SyntaxNode;
type Tree = ParserModule.Tree;
type TreeCursor = ParserModule.TreeCursor;
type Point = ParserModule.Point;
type Range = ParserModule.Range;
type Edit = ParserModule.Edit;
type Query = ParserModule.Query;
type QueryMatch = ParserModule.QueryMatch;
type QueryCapture = ParserModule.QueryCapture;
type Logger = ParserModule.Logger;

// Initialize the parser
let isParserInitialized = false;

async function ensureParserInitialized(): Promise<void> {
  if (!isParserInitialized) {
    await ParserModule.init();
    isParserInitialized = true;
  }
}

// Global parser instance
let parser: Parser | null = null;
let language: Language | null = null;

// Type declarations for optional dependencies
type Escodegen = {
  generate: (ast: any, options?: any) => string;
};
type LezerPython = {
  parse: (input: string) => any;
};
type Prettier = {
  format: (code: string, options?: any) => Promise<string>;
};

// Try to import optional dependencies
let escodegen: Escodegen | undefined;
let lezerPython: LezerPython | undefined;
let prettier: Prettier | undefined;

try {
  // @ts-ignore - Dynamic import for optional dependencies
  escodegen = await import('escodegen');
} catch (e) {
  console.warn('escodegen not available, some features may be limited');
}

try {
  // @ts-ignore - Dynamic import for optional dependencies
  lezerPython = await import('lezer-python');
} catch (e) {
  console.warn('lezer-python not available, Python support will be limited');
}

try {
  // @ts-ignore - Dynamic import for optional dependencies
  prettier = await import('prettier');
} catch (e) {
  console.warn('prettier not available, code formatting will be limited');
}

// === Core Types ===

/**
 * Represents an edit of a source file.
 */
export interface FileEdit {
  path: string;
  before: string;
  after: string;
  applied?: boolean;
  error?: string;
  diff?: string;
}

/**
 * Text edit operation
 */
export interface TextEdit {
  range: {
    start: { line: number; character: number };
    end: { line: number; character: number };
  };
  newText: string;
}

/**
 * Formats a file using Prettier if available, otherwise falls back to basic formatting
 * @param filePath Path to the file
 * @param content File content to format
 * @param lang Language of the file (default: 'typescript')
 * @returns Promise resolving to a FileEdit with the formatting changes
 */
async function formatFile(
  filePath: string,
  content: string,
  lang: string = 'typescript'
): Promise<FileEdit> {
  try {
    let formatted = content;

    if (prettier) {
      try {
        formatted = await prettier.format(content, {
          parser: lang,
          singleQuote: true,
          trailingComma: 'es5',
        });
      } catch (error) {
        console.warn('Prettier formatting failed, falling back to basic formatting', error);
      }
    }

    // Basic formatting if prettier fails or isn't available
    if (formatted === content) {
      formatted = content
        .replace(/\r\n/g, '\n')  // Normalize line endings
        .replace(/\s+\n/g, '\n') // Remove trailing whitespace
        .trim() + '\n';          // Ensure single trailing newline
    }

    return {
      path: filePath,
      before: content,
      after: formatted,
      applied: true,
      diff: DiffUtil.unifiedDiff(content, formatted, filePath)
    };
  } catch (error) {
    return {
      path: filePath,
      before: content,
      after: content,
      applied: false,
      error: error instanceof Error ? error.message : 'Unknown error formatting file',
      diff: ''
    };
  }
}

/**
 * Normalizes whitespace in code for consistent comparison
 * @param content The content to normalize
 * @returns Normalized content
 */
function normalizeWhitespace(content: string): string {
  return content
    .replace(/\r\n/g, '\n')  // Normalize line endings
    .replace(/[^\S\n]+\n/g, '\n')  // Remove trailing whitespace
    .replace(/\n{3,}/g, '\n\n')  // Normalize multiple newlines
    .trim() + '\n';  // Ensure single trailing newline
}

interface Plan {
  id?: string;
  timestamp?: string;
  edits: FileEdit[];
}

// === Workspace ===
export class Workspace {
  root: string;
  includePatterns: string[];
  excludePatterns: string[];

  constructor(root: string, includePatterns: string[] = ['**/*.*'], excludePatterns: string[] = ['**/node_modules/**', '**/.git/**']) {
    this.root = path.resolve(root);
    this.includePatterns = includePatterns;
    this.excludePatterns = excludePatterns;
  }

  async findFiles(include: string[] = this.includePatterns, exclude: string[] = this.excludePatterns): Promise<string[]> {
    const uris = await vscode.workspace.findFiles(
      `{${include.join(',')}}`,
      `{${exclude.join(',')}}`
    );
    return uris.map(u => u.fsPath);
  }

  async listFiles(include: string[] = this.includePatterns, exclude: string[] = this.excludePatterns): Promise<string[]> {
    return this.findFiles(include, exclude);
  }

  async readFile(filePath: string): Promise<string> {
    return await fsPromises.readFile(filePath, 'utf-8');
  }

  async writeFile(filePath: string, content: string): Promise<void> {
    await fsPromises.mkdir(path.dirname(filePath), { recursive: true });
    await fsPromises.writeFile(filePath, content, 'utf-8');
  }

  async backupFile(filePath: string): Promise<string> {
    const backup = `${filePath}.bak`;
    const content = await this.readFile(filePath);
    await this.writeFile(backup, content);
    return backup;
  }
}

// === Diff Utility ===
interface ApplyPatchOptions {
  fuzzFactor?: number;
  compareLine?: (line: string, hunk: string) => boolean;
}

export class DiffUtil {
  static unifiedDiff(before: string, after: string, file: string): string {
    return createPatch(
      file,
      before,
      after,
      'Original',
      'Modified',
      '',  // Required empty string as 6th parameter
      { context: 3 }
    );
  }

  static applyPatch(content: string, patch: string, options: ApplyPatchOptions = {}): string {
    const parsedPatch = parsePatch(patch);
    if (!parsedPatch || !Array.isArray(parsedPatch)) {
      throw new Error('Invalid patch format');
    }

    let result = content;

    for (const p of parsedPatch) {
      // Create a patch string from the parsed diff
      const patchStr = createPatch(
        p.oldFileName || 'file',
        p.oldHeader || '',
        p.newHeader || '',
        p.hunks.flatMap(h => [
          `@@ -${h.oldStart},${h.oldLines} +${h.newStart},${h.newLines} @@`,
          ...h.lines
        ]).join('\n')
      );

      // Apply the patch with proper options
      const patchOptions = {
        compareLine: options.compareLine || ((line: string, hunk: string) => line.trim() === hunk.trim()),
        fuzzFactor: options.fuzzFactor || 0
      };

      const patchResult = applyPatch(result, patchStr, patchOptions);

      if (typeof patchResult === 'string') {
        result = patchResult;
      }
    }

    return result;
  }

  static diffLines(before: string, after: string): string {
    // formatFile implementation moved below for now
    const beforeLines = before.split('\n');
    const afterLines = after.split('\n');

    const diff: string[] = [];
    const maxLength = Math.max(beforeLines.length, afterLines.length);

    for (let i = 0; i < maxLength; i++) {
      if (i >= beforeLines.length) {
        diff.push(`+ ${afterLines[i]}`);
      } else if (i >= afterLines.length) {
        diff.push(`- ${beforeLines[i]}`);
      } else if (beforeLines[i] !== afterLines[i]) {
        diff.push(`- ${beforeLines[i]}`);
        diff.push(`+ ${afterLines[i]}`);
      } else {
        diff.push(`  ${beforeLines[i]}`);
      }
    }

    return diff.join('\n');
  }
}

// === Code Search ===
interface SearchOptions {
  caseSensitive?: boolean;
  wholeWord?: boolean;
  regex?: boolean;
  includeNodeModules?: boolean;
  maxResults?: number;
  filePatterns?: string[];
}

type SearchResult = {
  file: string;
  line: number;
  lineText: string;
  match: string;
  startColumn: number;
  endColumn: number;
};

export class CodeSearch {
  private workspace: Workspace;
  private filePatterns: string[];
  private excludePatterns: string[];

  constructor(workspace: Workspace) {
    this.workspace = workspace;
    this.filePatterns = ['**/*.{ts,tsx,js,jsx,json,md}'];
    this.excludePatterns = ['**/node_modules/**', '**/.git/**', '**/dist/**'];
  }

  async search(query: string, options: SearchOptions = {}): Promise<SearchResult[]> {
    const results: SearchResult[] = [];
    const files = await this.workspace.findFiles(
      options.filePatterns || this.filePatterns,
      this.excludePatterns
    );

    const searchRegex = this.createSearchRegex(query, options);

    for (const file of files) {
      try {
        const content = await this.workspace.readFile(file);
        const lines = content.split('\n');

        for (let i = 0; i < lines.length; i++) {
          const line = lines[i];
          let match: RegExpExecArray | null;

          while ((match = searchRegex.exec(line)) !== null) {
            results.push({
              file,
              line: i + 1,
              lineText: line,
              match: match[0],
              startColumn: match.index + 1,
              endColumn: match.index + match[0].length + 1
            });

            // Apply maxResults if specified
            if (options.maxResults && results.length >= options.maxResults) {
              return results;
            }
          }
        }
      } catch (error) {
        console.warn(`Error searching file ${file}:`, error);
      }
    }

    return results;
  }

  private createSearchRegex(query: string, options: SearchOptions): RegExp {
    let pattern = options.regex ? query : this.escapeRegExp(query);

    if (options.wholeWord) {
      pattern = `\\b${pattern}\\b`;
    }

    const flags = options.caseSensitive ? 'g' : 'gi';
    return new RegExp(pattern, flags);
  }

  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }
}

// === AST Utilities ===
export interface ASTNode {
  type: string;
  text: string;
  startPosition: { row: number, column: number };
  endPosition: { row: number, column: number };
  children: ASTNode[];
}
export class ASTUtil {
  static traverse(node: any): ASTNode { return { type: node.type, text: node.text, startPosition: node.startPosition, endPosition: node.endPosition, children: node.children ? node.children.map((c: any) => ASTUtil.traverse(c)) : [] }; }
  static findNodes(node: ASTNode, type: string): ASTNode[] { const out: ASTNode[] = []; if (node.type === type) out.push(node); for (const c of node.children) out.push(...ASTUtil.findNodes(c, type)); return out; }
}

// === Code Block Manipulation ===
export class CodeBlockUtil {
  static moveBlock(lines: string[], start: number, end: number, target: number): string[] { const block = lines.slice(start, end); const rest = [...lines.slice(0, start), ...lines.slice(end)]; rest.splice(target, 0, ...block); return rest; }
  static duplicateBlock(lines: string[], start: number, end: number, target: number): string[] { const block = lines.slice(start, end); const newLines = [...lines]; newLines.splice(target, 0, ...block); return newLines; }
  static deleteBlock(lines: string[], start: number, end: number): string[] { const newLines = [...lines]; newLines.splice(start, end - start); return newLines; }
}

// === Large File Utilities ===
export class LargeFileUtil {
  static async processLines(filePath: string, callback: (line: string, lineNum: number) => Promise<void> | void): Promise<void> {
    const rl = readline.createInterface({
      input: fs.createReadStream(filePath)
    });

    let lineNum = 0;
    for await (const line of rl) {
      await callback(line, lineNum);
      lineNum++;
    }
  }

  static async readChunks(filePath: string, chunkSize: number = 1024 * 1024): Promise<string[]> {
    const stats = await fs.promises.stat(filePath);
    const total = stats.size;
    const chunks: string[] = [];
    let offset = 0;

    const fd = await fs.promises.open(filePath, 'r');
    const buffer = Buffer.alloc(chunkSize);

    try {
      while (offset < total) {
        const { bytesRead } = await fd.read(buffer, 0, chunkSize, offset);
        if (bytesRead === 0) break;
        chunks.push(buffer.toString('utf8', 0, bytesRead));
        offset += bytesRead;
      }
      return chunks;
    } finally {
      await fd.close();
    }
  }
}
// === Symbol Indexing ===
export type SymbolType = 'function' | 'class' | 'variable' | 'import' | 'interface' | 'type' | 'const' | 'let';

export interface SymbolEntry {
  file: string;
  name: string;
  type: SymbolType;
  line: number;
  startLine: number;
  endLine: number;
  language?: string;
  exportName?: string;
}

export class SymbolIndexer {
  private index: Map<string, SymbolEntry[]> = new Map();
  private symbols: Map<string, SymbolEntry> = new Map();
  private symbolPatterns: { regex: RegExp, type: SymbolType }[] = [
    { regex: /^(?:export\s+)?(?:async\s+)?function\s+([a-zA-Z_$][\w$]*)\s*\(/m, type: 'function' },
    { regex: /^export\s+default\s+function\s+([a-zA-Z_$][\w$]*)\s*\(/m, type: 'function' },
    { regex: /^(?:export\s+)?class\s+([A-Z][a-zA-Z0-9_$]*)\b/m, type: 'class' },
    { regex: /^export\s+default\s+class\s+([A-Z][a-zA-Z0-9_$]*)\b/m, type: 'class' },
    { regex: /^import\s+(?:[\s\S]*?\s+from\s+)?['"]([^'"]+)['"]/m, type: 'import' },
    { regex: /^(?:export\s+)?interface\s+([A-Z][a-zA-Z0-9_$]*)\b/m, type: 'interface' },
    { regex: /^(?:export\s+)?type\s+([A-Z][a-zA-Z0-9_$]*)\s*=/m, type: 'type' },
    { regex: /^export\s+(?:const|let|var)\s+([a-zA-Z_$][\w$]*)/m, type: 'variable' },
    { regex: /^const\s+([a-zA-Z_$][\w$]*)\s*=/m, type: 'const' },
    { regex: /^let\s+([a-zA-Z_$][\w$]*)\s*=/m, type: 'let' },
    { regex: /^var\s+([a-zA-Z_$][\w$]*)\s*=/m, type: 'variable' }
  ];

  constructor() {
    this.clear();
  }

  clear(): void {
    this.index.clear();
    this.symbols.clear();
  }

  getFiles(): string[] {
    return Array.from(this.index.keys());
  }

  getSymbolsInFile(file: string): SymbolEntry[] {
    return this.index.get(file) || [];
  }

  async indexFile(file: string, content: string): Promise<void> {
    const entries: SymbolEntry[] = [];
    const lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      for (const { regex, type } of this.symbolPatterns) {
        const match = line.match(regex);
        if (match && match[1]) {
          const entry: SymbolEntry = {
            file,
            name: match[1],
            type,
            line: i + 1,
            startLine: i + 1,
            endLine: i + 1
          };
          entries.push(entry);
          this.symbols.set(entry.name, entry);
        }
      }
    }

    this.index.set(file, entries);
  }

  find(symbol: string, exactMatch: boolean = false): SymbolEntry[] {
    const results: SymbolEntry[] = [];
    const searchTerm = exactMatch
      ? symbol
      : symbol.toLowerCase();

    for (const entry of this.symbols.values()) {
      const match = exactMatch
        ? entry.name === searchTerm
        : entry.name.toLowerCase().includes(searchTerm);

      if (match) {
        results.push(entry);
      }
    }

    return results;
  }
}

export class AIEditor {
  private workspace: Workspace;
  private symbolIndexer: SymbolIndexer;
  private searcher: CodeSearch;
  private bridge: any;
  private prettier: any;
  private _applied: number = 0;
  private _backups: string[] = [];
  private _diff: string = '';
  private planner: {
    renameSymbol: (path: string, oldName: string, newName: string, lang: string) => Promise<FileEdit>;
    extractFunction: (path: string, start: number, end: number, name: string, lang: string) => Promise<FileEdit>;
    moveLines: (path: string, start: number, end: number, to: number, lang: string) => Promise<FileEdit>;
    applyPlan: (plan: Plan) => Promise<{ applied: number; backups: string[]; diff: string }>;
  };

  constructor(root: string) {
    this.workspace = new Workspace(root);
    this.symbolIndexer = new SymbolIndexer();
    this.searcher = new CodeSearch(this.workspace);
    this.bridge = {};
    this.prettier = null;

    // Initialize planner methods
    this.planner = {
      renameSymbol: async (filePath: string, oldName: string, newName: string, lang: string = 'typescript'): Promise<FileEdit> => {
        return this.renameSymbol(filePath, oldName, newName, lang);
      },
      extractFunction: async (filePath: string, startLine: number, endLine: number, funcName: string, lang: string = 'typescript'): Promise<FileEdit> => {
        return this.extractFunction(filePath, startLine, endLine, funcName, lang);
      },
      moveLines: async (filePath: string, startLine: number, endLine: number, toLine: number, lang: string = 'typescript'): Promise<FileEdit> => {
        return this.moveLines(filePath, startLine, endLine, toLine, lang);
      },
      applyPlan: async (plan: Plan): Promise<{ applied: number; backups: string[]; diff: string }> => {
        return this.applyPlan(plan);
      }
    };
  }

  // Getters for private properties
  get applied(): number {
    return this._applied;
  }

  get backups(): string[] {
    return [...this._backups];
  }

  get diff(): string {
    return this._diff;
  }

  private getParser(): Parser | null {
    return parser;
  }

  private getLanguage(): Language | null {
    return language;
  }

  async renameSymbol(
    filePath: string,
    oldName: string,
    newName: string,
    lang: string = 'typescript'
  ): Promise<FileEdit> {
    const fileContent = await this.workspace.readFile(filePath);

    if (!parser || !language) {
      throw new Error('Parser not initialized. Call initParser() first.');
    }

    try {
      // Parse the file
      const tree = parser.parse(fileContent);

      // Find all occurrences of the symbol
      const query = language.query(`(identifier) @id (#eq? @id "${oldName}")`);
      const matches = query.matches(tree.rootNode);

      if (matches.length === 0) {
        return {
          path: filePath,
          before: fileContent,
          after: fileContent,
          applied: false,
          error: `Symbol '${oldName}' not found in file`
        };
      }

      // Apply replacements
      let newContent = fileContent;
      let offset = 0;

      for (const match of matches) {
        for (const capture of match.captures) {
          const { startIndex, endIndex } = capture.node;
          const before = newContent.substring(0, startIndex + offset);
          const after = newContent.substring(endIndex + offset);
          newContent = before + newName + after;
          offset += newName.length - (endIndex - startIndex);
        }
      }

      return {
        path: filePath,
        before: fileContent,
        after: newContent,
        applied: true,
        diff: DiffUtil.unifiedDiff(fileContent, newContent, filePath)
      };
    } catch (error) {
      return {
        path: filePath,
        before: fileContent,
        after: fileContent,
        applied: false,
        error: error instanceof Error ? error.message : 'Unknown error during symbol rename'
      };
    }
  }

  private async extractFunction(
    filePath: string,
    startLine: number,
    endLine: number,
    funcName: string,
    lang: string = 'typescript'
  ): Promise<FileEdit> {
    try {
      const fileContent = await this.workspace.readFile(filePath);
      const lines = fileContent.split('\n');

      // Get the range of the code to extract
      const startIndex = lines.slice(0, startLine - 1).join('\n').length + 1;
      const endIndex = lines.slice(0, endLine).join('\n').length;
      const extractedCode = fileContent.substring(startIndex, endIndex).trim();

      // Create the new function
      const newFunction = `function ${funcName}() {
  ${extractedCode}
}`;

      // Replace the extracted code with a function call
      const newContent = [
        fileContent.substring(0, startIndex),
        `${funcName}();`,
        fileContent.substring(endIndex)
      ].join('\n');

      return {
        path: filePath,
        before: fileContent,
        after: newContent,
        applied: true,
        diff: DiffUtil.unifiedDiff(fileContent, newContent, filePath)
      };
    } catch (error) {
      return {
        path: filePath,
        before: '',
        after: '',
        applied: false,
        error: error instanceof Error ? error.message : 'Failed to extract function'
      };
    }
  }

  private async moveLines(
    filePath: string,
    startLine: number,
    endLine: number,
    toLine: number,
    lang: string = 'typescript'
  ): Promise<FileEdit> {
    const fileContent = await this.workspace.readFile(filePath);
    const lines = fileContent.split('\n');

    // Ensure valid line numbers
    if (startLine < 1 || endLine > lines.length || toLine < 1 || toLine > lines.length + 1) {
      return {
        path: filePath,
        before: fileContent,
        after: fileContent,
        applied: false,
        error: 'Invalid line numbers provided'
      };
    }

    // Adjust for 0-based indexing
    const start = startLine - 1;
    const end = endLine;
    const target = toLine - 1;

    // Move the lines
    const movingLines = lines.slice(start, end);
    const newLines = [...lines];
    newLines.splice(start, end - start);

    // Calculate new target position after removing the lines
    const newTarget = target > start ? target - (end - start) : target;
    newLines.splice(newTarget, 0, ...movingLines);

    const newContent = newLines.join('\n');

    return {
      path: filePath,
      before: fileContent,
      after: newContent,
      applied: true,
      diff: DiffUtil.unifiedDiff(fileContent, newContent, filePath)
    };
  }

  private async applyPlan(plan: Plan): Promise<{ applied: number; backups: string[]; diff: string }> {
    let applied = 0;
    const backups: string[] = [];
    let diff = '';

    for (const edit of plan.edits) {
      try {
        // Create backup
        const backup = await this.workspace.backupFile(edit.path);
        backups.push(backup);

        // Apply the edit
        await this.workspace.writeFile(edit.path, edit.after);
        applied++;

        // Generate diff
        if (edit.diff) {
          diff += edit.diff;
        } else {
          const fileDiff = DiffUtil.unifiedDiff(edit.before, edit.after, edit.path);
          diff += fileDiff;
        }
      } catch (error) {
        console.error(`Failed to apply edit to ${edit.path}:`, error);
        // Continue with next edit even if one fails
      }
    }

    return {
      applied,
      backups,
      diff
    };
  }

  private async initParser(langWasmPath: string): Promise<void> {
    try {
      // Initialize the parser if not already done
      await ensureParserInitialized();

      // Create a new parser instance
      parser = new ParserModule.Parser();

      // Load the language wasm file
      const wasm = await fsPromises.readFile(langWasmPath);

      // Create a blob URL for the wasm file
      const blob = new Blob([wasm], { type: 'application/wasm' });
      const url = URL.createObjectURL(blob);

      try {
        // Load the language using the Parser.Language.load method
        language = await (ParserModule as any).Language.load(url);
        
        // Set the language for the parser
        if (parser && language) {
          parser.setLanguage(language);
        }
      } finally {
        // Clean up the blob URL
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Failed to initialize parser:', error);
      throw error;
    }
  }

  private normalizeWhitespace(content: string): string {
    if (!content) return '';
    return content
      .replace(/\r\n/g, '\n')  // Convert Windows line endings to Unix
      .replace(/\r/g, '\n')     // Convert old Mac line endings to Unix
      .replace(/[ \t]+$/gm, '')  // Remove trailing whitespace from each line
      .replace(/\n{3,}/g, '\n\n') // Normalize multiple empty lines to max 2
      .replace(/\s+$/, '');     // Remove trailing whitespace at the end of file
  }
}

/**
 * Minimal AST-node interface capturing only the properties we use.
 */
interface Node {
  namedChildren: Node[];
  startIndex: number;
  endIndex: number;
}

/**
 * Reads, parses, deduplicates, and merges near-duplicate definitions
 * in *any* programming language (via a Tree-sitter .wasm grammar).
 */
export async function removeDuplicates(
  path: string,
  langId: string,
  wasmPath: string,
  workspace: { readFile(path: string): Promise<string> }
): Promise<FileEdit> {
  // --- Parser & Grammar Caching (module-scope singletons) ---
  type ParserCache = {
    parser: Parser
    grammars: Map<string, Language>
  }
  const cacheKey = '__TREE_SITTER_CACHE__'
  const globalAny = globalThis as any

  if (!globalAny[cacheKey]) {
    await ParserModule.init();
    globalAny[cacheKey] = {
      parser: new ParserModule.Parser(),
      grammars: new Map<string, ParserModule.Language>()
    } as ParserCache;
  }

  const { parser, grammars } = globalAny[cacheKey] as ParserCache;

  // --- Load or reuse the requested language grammar ---
  if (!grammars.has(langId)) {
    try {
      // Initialize the parser if not already done
      if (!globalAny[cacheKey]?.initialized) {
        await ParserModule.init();
        globalAny[cacheKey] = { ...globalAny[cacheKey], initialized: true };
      }
      
// Load the language grammar
const lang = await (ParserModule as any).Language.load(wasmPath);
      grammars.set(langId, lang);
    } catch (err) {
      throw new Error(
        `Failed to load Tree-sitter grammar for "${langId}" from "${wasmPath}":\n  ${err}`
      );
    }
  }
  parser.setLanguage(grammars.get(langId)!)

  // --- Read & parse the source file ---
  let before: string
  try {
    before = await workspace.readFile(path)
  } catch (err) {
    throw new Error(`Unable to read file "${path}":\n  ${err}`)
  }

  const tree = parser.parse(before)
  const edits: { start: number; end: number; replacement: string }[] = []

  // --- Jaccard-based fuzzy header matcher ---
  function fuzzyMatch(a: string, b: string, threshold = 0.6): boolean {
    const tokenize = (s: string) =>
      new Set(s.toLowerCase().split(/\W+/).filter(Boolean))
    const A = tokenize(a), B = tokenize(b)
    const intersection = [...A].filter((w) => B.has(w)).length
    const union = new Set([...A, ...B]).size
    return union > 0 && intersection / union >= threshold
  }

  // --- Merge default parameter values across headers ---
  function mergeHeaders(headers: string[]): string {
    // Use first header as template
    const tmpl = headers[0]
    const m = tmpl.match(/^([^(]+)\(([^)]*)\)/)
    if (!m) return tmpl

    const [, fnName, paramStr] = m
    const map = new Map<string, string | null>()

    // Build name→default map, preferring any explicit defaults
    for (const hdr of headers) {
      const pm = hdr.match(/^[^(]+\(([^)]*)\)/)
      if (!pm) continue
      pm[1]
        .split(',')
        .map((p) => p.trim())
        .filter(Boolean)
        .forEach((p) => {
          const [name, def] = p.split('=').map((s) => s.trim())
          if (!map.has(name) || (def && def.length)) {
            map.set(name, def ?? null)
          }
        })
    }

    // Re-serialize in the original order
    const order = paramStr
      .split(',')
      .map((p) => p.trim().split('=')[0])
      .filter(Boolean)

    const mergedParams = order
      .map((n) => (map.get(n) ? `${n}=${map.get(n)}` : n))
      .join(', ')

    return `${fnName}(${mergedParams})`
  }

  /**
   * Recursively traverses `node` to find sibling groups of
   * (near-)duplicates, merges them, and records text-replacements.
   */
  function processNode(node: SyntaxNode) {
    // Recurse into all children first (handle nested duplicates)
    for (const child of node.namedChildren) {
      processNode(child)
    }

    const siblings = node.namedChildren
    if (siblings.length < 2) return

    // Group siblings by fuzzy header signature
    type Group = { signature: string; nodes: SyntaxNode[] }
    const groups: Group[] = []

    for (const sib of siblings) {
      const firstChild = sib.firstNamedChild;
      const hdrEnd = firstChild ? firstChild.startIndex : sib.endIndex;
      const rawHdr = before.slice(sib.startIndex, hdrEnd);
      const sig = rawHdr.trim();

      let grp = groups.find((g) => fuzzyMatch(g.signature, sig));
      if (!grp) {
        grp = { signature: sig, nodes: [] }
        groups.push(grp)
      }
      grp.nodes.push(sib)
    }

    // For each duplicate group, compose a merged replacement
    for (const grp of groups) {
      if (grp.nodes.length < 2) continue

      // Sort by file order
      const sorted = grp.nodes.sort((a, b) => a.startIndex - b.startIndex)
      const anchor = sorted[0]
      const firstChild = anchor.namedChildren[0]
      const hdrEnd = firstChild ? firstChild.startIndex : anchor.endIndex

      // Preserve indentation + unified header
      const rawHdr = before.slice(anchor.startIndex, hdrEnd)
      const indent = rawHdr.match(/^\s*/)?.[0] ?? ''
      const mergedHdr = indent + mergeHeaders(
        sorted.map((n) =>
          before.slice(n.startIndex, hdrEnd).trim()
        )
      )

      // If it's a leaf (e.g. import), keep the single line
      if (!anchor.namedChildren.length) {
        const singleLine = before.slice(
          anchor.startIndex,
          anchor.endIndex
        )
        edits.push({
          start: anchor.startIndex,
          end: sorted[sorted.length - 1].endIndex,
          replacement: singleLine
        })
        continue
      }

      // Merge all unique body fragments in appearance order
      const seenBodies = new Set<string>()
      const bodyLines: string[] = []

      for (const dup of sorted) {
        for (const sub of dup.namedChildren) {
          const raw = before.slice(sub.startIndex, sub.endIndex)
          const trimmed = raw.trim()
          if (!seenBodies.has(trimmed)) {
            seenBodies.add(trimmed)
            const subIndent = raw.match(/^\s*/)?.[0] ?? ''
            bodyLines.push(subIndent + trimmed)
          }
        }
      }

      // Preserve trailing tokens (braces, semicolons)
      const lastChild = anchor.namedChildren.at(-1)!
      const footer = before.slice(
        lastChild.endIndex,
        anchor.endIndex
      )

      const mergedBlock =
        mergedHdr +
        '\n' +
        bodyLines.join('\n') +
        '\n' +
        footer

      edits.push({
        start: anchor.startIndex,
        end: sorted[sorted.length - 1].endIndex,
        replacement: mergedBlock
      })
    }
  }

  // Kick-off the recursive merge at the root
  processNode(tree.rootNode)

  // Apply all edits in **reverse** order to maintain correct offsets
  edits.sort((a, b) => b.start - a.start)
  let after = before
  for (const { start, end, replacement } of edits) {
    after = after.slice(0, start) + replacement + after.slice(end)
  }

  return { path, before, after }
}

import * as prettierModule from 'prettier'
import { Options as PrettierOptions } from 'prettier'

export class Formatter {
  private configCache = new Map<string, PrettierOptions>()

  constructor(private readonly prettierInstance: typeof prettierModule | null = prettierModule) { }

  /**
   * Formats a file using Prettier if available, otherwise falls back to basic whitespace normalization.
   * Caches resolved Prettier configs per directory. Allows overriding options per invocation.
   */
  async formatFile(
    filePath: string,
    content: string,
    lang = 'typescript',
    overrideOptions: Partial<PrettierOptions> = {}
  ): Promise<FileEdit> {
    const fileDir = path.dirname(filePath)
    const before = content
    let after = before

    if (!before) {
      return this.buildEdit(filePath, before, '')
    }

    if (!this.prettierInstance) {
      after = this.fallbackFormat(before)
      return this.buildEdit(filePath, before, after)
    }

    const parser = this.getParser(lang)
    const cached = this.configCache.get(fileDir)
    let baseConfig: PrettierOptions = cached ?? {}

    if (!cached) {
      try {
const resolved = await prettierModule.resolveConfig(filePath, { editorconfig: true })
        baseConfig = resolved || {}
      } catch (resolveErr) {
        console.warn(`Failed to resolve Prettier config for ${filePath}:`, resolveErr instanceof Error ? resolveErr.message : resolveErr)
      }
      this.configCache.set(fileDir, baseConfig)
    }

    const options: PrettierOptions = {
      parser,
      ...baseConfig,
      ...overrideOptions
    }

    try {
after = await this.prettierInstance.format(before, options)
    } catch (formatErr) {
      console.warn(
        `Prettier formatting failed for ${filePath}, falling back to whitespace normalization:`,
        formatErr instanceof Error ? formatErr.message : formatErr
      )
      after = this.fallbackFormat(before)
    }

    return this.buildEdit(filePath, before, after)
  }

  private fallbackFormat(content: string): string {
    return content
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      .replace(/[ \t]+$/gm, '')
      .replace(/\n{3,}/g, '\n\n')
      .replace(/\s+$/, '')
  }

  private getParser(lang: string): string {
    const map: Record<string, string> = {
      typescript: 'typescript',
      tsx: 'typescript',
      javascript: 'babel',
      jsx: 'babel',
      json: 'json',
      json5: 'json',
      graphql: 'graphql',
      markdown: 'markdown',
      md: 'markdown',
      html: 'html',
      vue: 'vue',
      css: 'css',
      scss: 'scss',
      less: 'less',
      yaml: 'yaml',
      yml: 'yaml'
    }
    return map[lang.toLowerCase()] || 'babel'
  }

  private buildEdit(path: string, before: string, after: string): FileEdit {
    return {
      path,
      before,
      after,
      applied: before !== after
    }
  }
}

// === CLI Entrypoint ===
if (require.main === module) {
  (async () => {
    const root = process.argv[2] || process.cwd();
    const editor = new AIEditor(root);
    console.log(`Initialized AI Editor at ${root}`);

    // Example usage:
    // const plan = await editor.renameSymbol('path/to/file.ts', 'oldName', 'newName');
    // const result = await editor.applyPlan(plan);
  })();
}
