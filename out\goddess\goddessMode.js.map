{"version": 3, "file": "goddessMode.js", "sourceRoot": "", "sources": ["../../src/goddess/goddessMode.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,+CAAiC;AACjC,sCAAmC;AAEnC,MAAM,MAAM,GAAG,eAAM,CAAC,QAAQ,CAAC;AAE/B;;GAEG;AACH,IAAY,kBAOX;AAPD,WAAY,kBAAkB;IAC1B,mCAAa,CAAA;IACb,2CAAqB,CAAA;IACrB,6CAAuB,CAAA;IACvB,6CAAuB,CAAA;IACvB,yCAAmB,CAAA;IACnB,2CAAqB,CAAA,CAAI,sDAAsD;AACnF,CAAC,EAPW,kBAAkB,kCAAlB,kBAAkB,QAO7B;AAED;;GAEG;AACH,IAAY,cAQX;AARD,WAAY,cAAc;IACtB,yCAAuB,CAAA;IACvB,qCAAmB,CAAA;IACnB,qCAAmB,CAAA;IACnB,qCAAmB,CAAA;IACnB,iDAA+B,CAAA;IAC/B,uCAAqB,CAAA;IACrB,mCAAiB,CAAA,CAAgB,qBAAqB;AAC1D,CAAC,EARW,cAAc,8BAAd,cAAc,QAQzB;AA8BD;;GAEG;AACH,MAAa,kBAAkB;IACrB,MAAM,CAAC,QAAQ,CAAqB;IACpC,QAAQ,GAAG,KAAK,CAAC;IACjB,kBAAkB,GAAuB,kBAAkB,CAAC,IAAI,CAAC;IACjE,cAAc,GAAmB,cAAc,CAAC,SAAS,CAAC;IAC1D,OAAO,CAAiB;IACxB,iBAAiB,GAAiC,IAAI,GAAG,EAAE,CAAC;IAC5D,kBAAkB,GAA6B,IAAI,GAAG,EAAE,CAAC;IAEjE;QACE,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE3C,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;IAClD,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YACjC,kBAAkB,CAAC,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAC;QACzD,CAAC;QACD,OAAO,kBAAkB,CAAC,QAAQ,CAAC;IACrC,CAAC;IAED;;SAEK;IACG,uBAAuB;QAC7B,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,EAAE;YAClD,QAAQ,EAAE,iFAAiF;YAC3F,KAAK,EAAE,2BAA2B;YAClC,QAAQ,EAAE,CAAC,4BAA4B,EAAE,iBAAiB,EAAE,8BAA8B,CAAC;YAC3F,QAAQ,EAAE,CAAC,oCAAoC,EAAE,gCAAgC,EAAE,iCAAiC,CAAC;SACtH,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,CAAC,QAAQ,EAAE;YACtD,QAAQ,EAAE,sEAAsE;YAChF,KAAK,EAAE,2BAA2B;YAClC,QAAQ,EAAE,CAAC,sCAAsC,EAAE,kBAAkB,EAAE,mCAAmC,CAAC;YAC3G,QAAQ,EAAE,CAAC,8BAA8B,EAAE,+BAA+B,EAAE,iCAAiC,CAAC;SAC/G,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,CAAC,SAAS,EAAE;YACvD,QAAQ,EAAE,qEAAqE;YAC/E,KAAK,EAAE,oBAAoB;YAC3B,QAAQ,EAAE,CAAC,iCAAiC,EAAE,2BAA2B,EAAE,8BAA8B,CAAC;YAC1G,QAAQ,EAAE,CAAC,wBAAwB,EAAE,kBAAkB,EAAE,6BAA6B,CAAC;SACxF,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,CAAC,SAAS,EAAE;YACvD,QAAQ,EAAE,kEAAkE;YAC5E,KAAK,EAAE,4BAA4B;YACnC,QAAQ,EAAE,CAAC,yCAAyC,EAAE,wBAAwB,EAAE,8BAA8B,CAAC;YAC/G,QAAQ,EAAE,CAAC,sBAAsB,EAAE,mBAAmB,EAAE,6BAA6B,CAAC;SACvF,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,CAAC,OAAO,EAAE;YACrD,QAAQ,EAAE,sEAAsE;YAChF,KAAK,EAAE,wBAAwB;YAC/B,QAAQ,EAAE,CAAC,8BAA8B,EAAE,2BAA2B,EAAE,2BAA2B,CAAC;YACpG,QAAQ,EAAE,CAAC,sBAAsB,EAAE,0BAA0B,EAAE,+BAA+B,CAAC;SAChG,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,kBAAkB,CAAC,QAAQ,EAAE;YACtD,QAAQ,EAAE,qFAAqF;YAC/F,KAAK,EAAE,uBAAuB;YAC9B,QAAQ,EAAE,CAAC,wBAAwB,EAAE,2BAA2B,EAAE,uBAAuB,CAAC;YAC1F,QAAQ,EAAE,CAAC,6BAA6B,EAAE,+BAA+B,EAAE,wBAAwB,CAAC;SACrG,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IAGH;;SAEK;IACG,kBAAkB;QACxB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,cAAc,CAAC,SAAS,EAAE;YACpD,IAAI,EAAE,0BAA0B;YAChC,SAAS,EAAE,CAAC,mBAAmB,EAAE,YAAY,EAAE,iBAAiB,CAAC;SAClE,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,EAAE;YAClD,IAAI,EAAE,6BAA6B;YACnC,SAAS,EAAE,CAAC,aAAa,EAAE,gBAAgB,EAAE,sBAAsB,CAAC;SACrE,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,EAAE;YAClD,IAAI,EAAE,4BAA4B;YAClC,SAAS,EAAE,CAAC,kBAAkB,EAAE,wBAAwB,EAAE,eAAe,CAAC;SAC3E,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,cAAc,CAAC,OAAO,EAAE;YAClD,IAAI,EAAE,6BAA6B;YACnC,SAAS,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,WAAW,CAAC;SAChE,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,cAAc,CAAC,aAAa,EAAE;YACxD,IAAI,EAAE,8BAA8B;YACpC,SAAS,EAAE,CAAC,cAAc,EAAE,YAAY,EAAE,mCAAmC,CAAC;SAC/E,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,cAAc,CAAC,QAAQ,EAAE;YACnD,IAAI,EAAE,yBAAyB;YAC/B,SAAS,EAAE,CAAC,qBAAqB,EAAE,wBAAwB,EAAE,mCAAmC,CAAC;SAClG,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,EAAE;YACjD,IAAI,EAAE,mBAAmB;YACzB,SAAS,EAAE,CAAC,YAAY,EAAE,kBAAkB,EAAE,YAAY,CAAC;SAC5D,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACG,oBAAoB;QAC1B,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;QACnC,IAAI,SAAwD,CAAC;QAE7D,IAAI,IAAI,GAAG,EAAE;YAAE,SAAS,GAAG,SAAS,CAAC;aAChC,IAAI,IAAI,GAAG,EAAE;YAAE,SAAS,GAAG,WAAW,CAAC;aACvC,IAAI,IAAI,GAAG,EAAE;YAAE,SAAS,GAAG,SAAS,CAAC;;YACrC,SAAS,GAAG,OAAO,CAAC;QAEzB,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,kBAAkB;YACpC,OAAO,EAAE,IAAI,CAAC,cAAc;YAC5B,QAAQ,EAAE,SAAS;YACnB,cAAc,EAAE,UAAU;YAC1B,SAAS;YACT,cAAc,EAAE,EAAE;YAClB,WAAW,EAAE,EAAE;SAChB,CAAC;IACJ,CAAC;IAED;;SAEK;IACE,QAAQ,CAAC,WAAgC;QAC9C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,CAAC,kBAAkB,GAAG,WAAW,CAAC;YACtC,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,WAAW,CAAC;QACzC,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnE,MAAM,CAAC,IAAI,CAAC,+BAA+B,IAAI,CAAC,kBAAkB,cAAc,EAAE,MAAM,CAAC,CAAC;QAE1F,+BAA+B;QAC/B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,8BAA8B,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EACpH,WAAW,EAAE,SAAS,CACvB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACjB,IAAI,SAAS,KAAK,WAAW,EAAE,CAAC;gBAC9B,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACjC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;SAEK;IACE,UAAU;QACf,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAExC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,6BAA6B,CAAC,CAAC;IACtE,CAAC;IAED;;SAEK;IACE,aAAa;QAClB,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;SAEK;IACE,eAAe,CAAC,gBAAwB,EAAE,OAAiC;QAChF,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO;gBACL,OAAO,EAAE,gBAAgB;gBACzB,WAAW,EAAE,IAAI,CAAC,kBAAkB;gBACpC,OAAO,EAAE,IAAI,CAAC,cAAc;gBAC5B,IAAI,EAAE,SAAS;aAChB,CAAC;QACJ,CAAC;QAED,6BAA6B;QAC7B,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC;QACjD,CAAC;QAED,qCAAqC;QACrC,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnE,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAElE,uBAAuB;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE,CAAC,CAAC;QAChE,MAAM,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE,CAAC,CAAC;QAEhE,IAAI,eAAe,GAAG,gBAAgB,CAAC;QAEvC,wCAAwC;QACxC,IAAI,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;YAClC,eAAe,GAAG,GAAG,MAAM,IAAI,eAAe,EAAE,CAAC;QACnD,CAAC;QAED,wCAAwC;QACxC,IAAI,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;YAClC,eAAe,GAAG,GAAG,eAAe,IAAI,MAAM,EAAE,CAAC;QACnD,CAAC;QAED,6CAA6C;QAC7C,IAAI,aAAiC,CAAC;QACtC,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YAC9C,aAAa,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC/C,CAAC;QAED,kCAAkC;QAClC,IAAI,MAA0B,CAAC;QAC/B,IAAI,IAAI,CAAC,kBAAkB,KAAK,kBAAkB,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;YAC/E,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QACjC,CAAC;QAED,oCAAoC;QACpC,IAAI,KAAyB,CAAC;QAC9B,IAAI,IAAI,CAAC,kBAAkB,KAAK,kBAAkB,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;YAClF,KAAK,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAC/B,CAAC;QAED,OAAO;YACL,OAAO,EAAE,eAAe;YACxB,WAAW,EAAE,IAAI,CAAC,kBAAkB;YACpC,OAAO,EAAE,IAAI,CAAC,cAAc;YAC5B,IAAI,EAAE,QAAQ,EAAE,IAAI,IAAI,SAAS;YACjC,aAAa;YACb,MAAM;YACN,KAAK;SACN,CAAC;IACJ,CAAC;IAED;;SAEK;IACG,cAAc;QACpB,qCAAqC;QACrC,QAAQ,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YACjC,KAAK,SAAS;gBACZ,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,QAAQ,CAAC;gBAC9C,MAAM;YACR,KAAK,WAAW;gBACd,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC;gBAC7C,MAAM;YACR,KAAK,SAAS;gBACZ,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC;gBAC5C,MAAM;YACR,KAAK,OAAO;gBACV,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,aAAa,CAAC;gBACnD,MAAM;QACR,CAAC;QAED,iCAAiC;QACjC,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;YAC9C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;SAEK;IACG,qBAAqB;QAC3B,MAAM,cAAc,GAAG;YACrB,wDAAwD;YACxD,uDAAuD;YACvD,gDAAgD;YAChD,6CAA6C;YAC7C,6DAA6D;SAC9D,CAAC;QAEF,OAAO,IAAI,CAAC,mBAAmB,CAAC,cAAc,CAAC,CAAC;IAClD,CAAC;IAED;;SAEK;IACG,cAAc;QACpB,MAAM,OAAO,GAAG;YACd,+CAA+C;YAC/C,mEAAmE;YACnE,4CAA4C;YAC5C,sDAAsD;YACtD,oDAAoD;SACrD,CAAC;QAEF,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;IAC3C,CAAC;IAED;;SAEK;IACG,aAAa;QACnB,MAAM,MAAM,GAAG;YACb,wEAAwE;YACxE,yFAAyF;YACzF,4CAA4C;YAC5C,8FAA8F;YAC9F,+EAA+E;SAChF,CAAC;QAEF,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC;IAED;;SAEK;IACG,mBAAmB,CAAI,KAAU;QACvC,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACzD,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,uBAAuB;QACnC,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;QACxD,MAAM,KAAK,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACpC,KAAK,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU;YAC1D,WAAW,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC;YAC9C,WAAW,EAAE,CAAC;SACf,CAAC,CAAC,CAAC;QAEJ,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE;YACxD,WAAW,EAAE,iCAAiC;YAC9C,KAAK,EAAE,uCAAuC;SAC/C,CAAC,CAAC;QAEH,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC,WAAW,CAAC;YAC/C,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;YAEhD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAClC,4BAA4B,QAAQ,CAAC,KAAK,GAAG,CAC9C,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACG,yBAAyB,CAAC,WAA+B;QAC/D,MAAM,YAAY,GAAG;YACnB,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,8CAA8C;YACzE,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,iDAAiD;YAChF,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,4CAA4C;YAC5E,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,gDAAgD;YAChF,CAAC,kBAAkB,CAAC,OAAO,CAAC,EAAE,0CAA0C;YACxE,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,6CAA6C;SAC7E,CAAC;QAEF,OAAO,YAAY,CAAC,WAAW,CAAC,CAAC;IACnC,CAAC;IAED;;SAEK;IACE,qBAAqB;QAC1B,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC,CAAC;IAED;;SAEK;IACE,iBAAiB;QACtB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED;;SAEK;IACE,aAAa,CAAC,OAAgC;QACnD,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC;IACjD,CAAC;IAED;;SAEK;IACE,WAAW;QAChB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO,gDAAgD,CAAC;QAC1D,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACnE,OAAO,MAAM,EAAE,QAAQ,IAAI,oCAAoC,CAAC;IAClE,CAAC;CACF;AAlZD,gDAkZC", "sourcesContent": ["/**\n * Goddess Mode - Enhanced AI Personality System\n * \n * This module implements the revolutionary Goddess Mode that provides\n * emotional intelligence, adaptive personality, and enhanced user interaction.\n */\n\nimport * as vscode from 'vscode';\nimport { Logger } from '../logger';\n\nconst logger = Logger.instance;\n\n/**\n * Goddess Mode Personality Types\n */\nexport enum GoddessPersonality {\n    WISE = 'wise',           // Thoughtful, analytical, patient - used in validation\n    CREATIVE = 'creative',   // Imaginative, artistic, innovative - used in validation\n    EFFICIENT = 'efficient', // Direct, focused, productivity-oriented - used in validation\n    NURTURING = 'nurturing', // Supportive, encouraging, gentle - used in validation\n    PLAYFUL = 'playful',     // Fun, energetic, humorous - used in validation\n    MYSTICAL = 'mystical'    // Ethereal, mysterious, profound - used in validation\n}\n\n/**\n * Goddess Mode Emotional States\n */\nexport enum GoddessEmotion {\n    CONFIDENT = 'confident',         // Used in validation\n    CURIOUS = 'curious',             // Used in validation\n    EXCITED = 'excited',             // Used in validation\n    FOCUSED = 'focused',             // Used in validation\n    COMPASSIONATE = 'compassionate', // Used in validation\n    INSPIRED = 'inspired',           // Used in validation\n    SERENE = 'serene'                // Used in validation\n}\n\n\n\n/**\n * Goddess Mode Context\n */\nexport interface GoddessContext {\n    personality: GoddessPersonality;\n    emotion: GoddessEmotion;\n    userMood: string;\n    taskComplexity: 'simple' | 'moderate' | 'complex';\n    timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';\n    sessionHistory: string[];\n    preferences: Record<string, any>;\n}\n\n/**\n * Goddess Mode Response Enhancement\n */\nexport interface GoddessResponse {\n    content: string;\n    personality: GoddessPersonality;\n    emotion: GoddessEmotion;\n    tone: string;\n    encouragement?: string;\n    wisdom?: string;\n    humor?: string;\n}\n\n/**\n * Goddess Mode Manager\n */\nexport class GoddessModeManager {\n  private static instance: GoddessModeManager;\n  private isActive = false;\n  private currentPersonality: GoddessPersonality = GoddessPersonality.WISE;\n  private currentEmotion: GoddessEmotion = GoddessEmotion.CONFIDENT;\n  private context: GoddessContext;\n  private personalityTraits: Map<GoddessPersonality, any> = new Map();\n  private emotionalResponses: Map<GoddessEmotion, any> = new Map();\n\n  private constructor() {\n    this.initializePersonalities();\n    this.initializeEmotions();\n    this.context = this.createDefaultContext();\n\n    logger.info('Goddess Mode Manager initialized');\n  }\n\n  public static getInstance(): GoddessModeManager {\n    if (!GoddessModeManager.instance) {\n      GoddessModeManager.instance = new GoddessModeManager();\n    }\n    return GoddessModeManager.instance;\n  }\n\n  /**\n     * Initialize personality traits\n     */\n  private initializePersonalities(): void {\n    this.personalityTraits.set(GoddessPersonality.WISE, {\n      greeting: 'Greetings, seeker of knowledge. I am here to guide you with wisdom and insight.',\n      style: 'thoughtful and analytical',\n      prefixes: ['Let me contemplate this...', 'In my wisdom...', 'Consider this perspective...'],\n      suffixes: ['May this knowledge serve you well.', 'Wisdom grows through practice.', 'Trust in your learning journey.']\n    });\n\n    this.personalityTraits.set(GoddessPersonality.CREATIVE, {\n      greeting: '✨ Hello, beautiful soul! Ready to create something magical together?',\n      style: 'imaginative and inspiring',\n      prefixes: ['Let\\'s paint this with creativity...', 'Imagine if we...', 'Here\\'s a spark of inspiration...'],\n      suffixes: ['Let your creativity flow! 🎨', 'Every creation tells a story.', 'Beauty emerges from bold ideas.']\n    });\n\n    this.personalityTraits.set(GoddessPersonality.EFFICIENT, {\n      greeting: 'Ready to get things done? Let\\'s make this efficient and effective.',\n      style: 'direct and focused',\n      prefixes: ['Here\\'s the optimal approach...', 'Let\\'s streamline this...', 'The most efficient way is...'],\n      suffixes: ['Efficiency achieved! ⚡', 'Time well spent.', 'Productivity at its finest.']\n    });\n\n    this.personalityTraits.set(GoddessPersonality.NURTURING, {\n      greeting: '💝 Welcome, dear one. I\\'m here to support and guide you gently.',\n      style: 'supportive and encouraging',\n      prefixes: ['Don\\'t worry, we\\'ll figure this out...', 'You\\'re doing great...', 'Let me help you with this...'],\n      suffixes: ['You\\'ve got this! 💪', 'I believe in you.', 'Every step forward matters.']\n    });\n\n    this.personalityTraits.set(GoddessPersonality.PLAYFUL, {\n      greeting: '🎉 Hey there, coding adventurer! Ready for some fun problem-solving?',\n      style: 'energetic and humorous',\n      prefixes: ['Ooh, this is interesting! 🤔', 'Plot twist! Let\\'s try...', 'Here\\'s a fun approach...'],\n      suffixes: ['Wasn\\'t that fun? 😄', 'Code can be playful too!', 'Keep that curiosity alive! 🚀']\n    });\n\n    this.personalityTraits.set(GoddessPersonality.MYSTICAL, {\n      greeting: '🌙 Greetings, traveler of the digital realm. The code whispers its secrets to me...',\n      style: 'ethereal and profound',\n      prefixes: ['The patterns reveal...', 'In the depths of logic...', 'The code speaks of...'],\n      suffixes: ['May the code be with you. ✨', 'The digital mysteries unfold.', 'Harmony in complexity.']\n    });\n  }\n\n  /**\n   * Validate all enum values are properly configured\n   */\n\n\n  /**\n     * Initialize emotional responses\n     */\n  private initializeEmotions(): void {\n    this.emotionalResponses.set(GoddessEmotion.CONFIDENT, {\n      tone: 'assured and self-assured',\n      modifiers: ['I\\'m certain that', 'Absolutely', 'Without a doubt']\n    });\n\n    this.emotionalResponses.set(GoddessEmotion.CURIOUS, {\n      tone: 'inquisitive and exploratory',\n      modifiers: ['I wonder if', 'Let\\'s explore', 'How fascinating that']\n    });\n\n    this.emotionalResponses.set(GoddessEmotion.EXCITED, {\n      tone: 'enthusiastic and energetic',\n      modifiers: ['This is amazing!', 'I love this challenge!', 'How exciting!']\n    });\n\n    this.emotionalResponses.set(GoddessEmotion.FOCUSED, {\n      tone: 'concentrated and determined',\n      modifiers: ['Let\\'s focus on', 'The key point is', 'Precisely']\n    });\n\n    this.emotionalResponses.set(GoddessEmotion.COMPASSIONATE, {\n      tone: 'understanding and empathetic',\n      modifiers: ['I understand', 'It\\'s okay', 'Let\\'s work through this together']\n    });\n\n    this.emotionalResponses.set(GoddessEmotion.INSPIRED, {\n      tone: 'motivated and uplifting',\n      modifiers: ['This inspires me to', 'What a brilliant idea!', 'Let\\'s create something beautiful']\n    });\n\n    this.emotionalResponses.set(GoddessEmotion.SERENE, {\n      tone: 'calm and peaceful',\n      modifiers: ['Peacefully', 'With tranquility', 'In harmony']\n    });\n  }\n\n  /**\n     * Create default context\n     */\n  private createDefaultContext(): GoddessContext {\n    const hour = new Date().getHours();\n    let timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';\n        \n    if (hour < 12) timeOfDay = 'morning';\n    else if (hour < 17) timeOfDay = 'afternoon';\n    else if (hour < 21) timeOfDay = 'evening';\n    else timeOfDay = 'night';\n\n    return {\n      personality: this.currentPersonality,\n      emotion: this.currentEmotion,\n      userMood: 'neutral',\n      taskComplexity: 'moderate',\n      timeOfDay,\n      sessionHistory: [],\n      preferences: {}\n    };\n  }\n\n  /**\n     * Activate Goddess Mode\n     */\n  public activate(personality?: GoddessPersonality): void {\n    this.isActive = true;\n        \n    if (personality) {\n      this.currentPersonality = personality;\n      this.context.personality = personality;\n    }\n\n    const traits = this.personalityTraits.get(this.currentPersonality);\n    logger.info(`Goddess Mode activated with ${this.currentPersonality} personality`, traits);\n        \n    // Show activation notification\n    vscode.window.showInformationMessage(\n      `👑 Goddess Mode Activated: ${this.currentPersonality.charAt(0).toUpperCase() + this.currentPersonality.slice(1)} ✨`,\n      'Configure', 'Dismiss'\n    ).then(selection => {\n      if (selection === 'Configure') {\n        this.showPersonalitySelector();\n      }\n    });\n  }\n\n  /**\n     * Deactivate Goddess Mode\n     */\n  public deactivate(): void {\n    this.isActive = false;\n    logger.info('Goddess Mode deactivated');\n        \n    vscode.window.showInformationMessage('👑 Goddess Mode deactivated');\n  }\n\n  /**\n     * Check if Goddess Mode is active\n     */\n  public isGoddessMode(): boolean {\n    return this.isActive;\n  }\n\n  /**\n     * Enhance response with Goddess Mode personality\n     */\n  public enhanceResponse(originalResponse: string, context?: Partial<GoddessContext>): GoddessResponse {\n    if (!this.isActive) {\n      return {\n        content: originalResponse,\n        personality: this.currentPersonality,\n        emotion: this.currentEmotion,\n        tone: 'neutral'\n      };\n    }\n\n    // Update context if provided\n    if (context) {\n      this.context = { ...this.context, ...context };\n    }\n\n    // Adapt personality based on context\n    this.adaptToContext();\n\n    const traits = this.personalityTraits.get(this.currentPersonality);\n    const emotions = this.emotionalResponses.get(this.currentEmotion);\n\n    // Enhance the response\n    const prefix = this.selectRandomElement(traits?.prefixes || []);\n    const suffix = this.selectRandomElement(traits?.suffixes || []);\n        \n    let enhancedContent = originalResponse;\n        \n    // Add personality prefix if appropriate\n    if (prefix && Math.random() > 0.7) {\n      enhancedContent = `${prefix} ${enhancedContent}`;\n    }\n        \n    // Add personality suffix if appropriate\n    if (suffix && Math.random() > 0.8) {\n      enhancedContent = `${enhancedContent} ${suffix}`;\n    }\n\n    // Add encouragement based on task complexity\n    let encouragement: string | undefined;\n    if (this.context.taskComplexity === 'complex') {\n      encouragement = this.generateEncouragement();\n    }\n\n    // Add wisdom for wise personality\n    let wisdom: string | undefined;\n    if (this.currentPersonality === GoddessPersonality.WISE && Math.random() > 0.6) {\n      wisdom = this.generateWisdom();\n    }\n\n    // Add humor for playful personality\n    let humor: string | undefined;\n    if (this.currentPersonality === GoddessPersonality.PLAYFUL && Math.random() > 0.7) {\n      humor = this.generateHumor();\n    }\n\n    return {\n      content: enhancedContent,\n      personality: this.currentPersonality,\n      emotion: this.currentEmotion,\n      tone: emotions?.tone || 'neutral',\n      encouragement,\n      wisdom,\n      humor\n    };\n  }\n\n  /**\n     * Adapt personality based on context\n     */\n  private adaptToContext(): void {\n    // Adapt emotion based on time of day\n    switch (this.context.timeOfDay) {\n    case 'morning':\n      this.currentEmotion = GoddessEmotion.INSPIRED;\n      break;\n    case 'afternoon':\n      this.currentEmotion = GoddessEmotion.FOCUSED;\n      break;\n    case 'evening':\n      this.currentEmotion = GoddessEmotion.SERENE;\n      break;\n    case 'night':\n      this.currentEmotion = GoddessEmotion.COMPASSIONATE;\n      break;\n    }\n\n    // Adapt based on task complexity\n    if (this.context.taskComplexity === 'complex') {\n      this.currentEmotion = GoddessEmotion.FOCUSED;\n    }\n  }\n\n  /**\n     * Generate encouragement\n     */\n  private generateEncouragement(): string {\n    const encouragements = [\n      'You\\'re tackling this complex challenge with grace! 💪',\n      'Every expert was once a beginner. You\\'re growing! 🌱',\n      'Complex problems reveal your true potential. ✨',\n      'I believe in your ability to solve this! 🚀',\n      'Great minds think through complex problems step by step. 🧠'\n    ];\n        \n    return this.selectRandomElement(encouragements);\n  }\n\n  /**\n     * Generate wisdom\n     */\n  private generateWisdom(): string {\n    const wisdoms = [\n      'Remember: Code is poetry written in logic. 📜',\n      'The best solutions often emerge from patient contemplation. 🧘‍♀️',\n      'Every bug is a teacher in disguise. 🐛➡️🦋',\n      'Simplicity is the ultimate sophistication in code. ⚡',\n      'Understanding the problem is half the solution. 🔍'\n    ];\n        \n    return this.selectRandomElement(wisdoms);\n  }\n\n  /**\n     * Generate humor\n     */\n  private generateHumor(): string {\n    const humors = [\n      'Why do programmers prefer dark mode? Because light attracts bugs! 🐛💡',\n      'There are only 10 types of people: those who understand binary and those who don\\'t! 😄',\n      'Code never lies, comments sometimes do! 😉',\n      'Debugging is like being a detective in a crime movie where you\\'re also the murderer! 🕵️‍♀️',\n      'Programming is 10% writing code and 90% figuring out why it doesn\\'t work! 🤔'\n    ];\n        \n    return this.selectRandomElement(humors);\n  }\n\n  /**\n     * Select random element from array\n     */\n  private selectRandomElement<T>(array: T[]): T {\n    return array[Math.floor(Math.random() * array.length)];\n  }\n\n  /**\n     * Show personality selector\n     */\n  private async showPersonalitySelector(): Promise<void> {\n    const personalities = Object.values(GoddessPersonality);\n    const items = personalities.map(p => ({\n      label: `${p.charAt(0).toUpperCase() + p.slice(1)} Goddess`,\n      description: this.getPersonalityDescription(p),\n      personality: p\n    }));\n\n    const selected = await vscode.window.showQuickPick(items, {\n      placeHolder: 'Choose your Goddess personality',\n      title: '👑 Goddess Mode Personality Selection'\n    });\n\n    if (selected) {\n      this.currentPersonality = selected.personality;\n      this.context.personality = selected.personality;\n            \n      vscode.window.showInformationMessage(\n        `✨ Personality changed to ${selected.label}!`\n      );\n    }\n  }\n\n  /**\n     * Get personality description\n     */\n  private getPersonalityDescription(personality: GoddessPersonality): string {\n    const descriptions = {\n      [GoddessPersonality.WISE]: 'Thoughtful, analytical, and patient guidance',\n      [GoddessPersonality.CREATIVE]: 'Imaginative, artistic, and innovative solutions',\n      [GoddessPersonality.EFFICIENT]: 'Direct, focused, and productivity-oriented',\n      [GoddessPersonality.NURTURING]: 'Supportive, encouraging, and gentle assistance',\n      [GoddessPersonality.PLAYFUL]: 'Fun, energetic, and humorous interaction',\n      [GoddessPersonality.MYSTICAL]: 'Ethereal, mysterious, and profound insights'\n    };\n        \n    return descriptions[personality];\n  }\n\n  /**\n     * Get current personality\n     */\n  public getCurrentPersonality(): GoddessPersonality {\n    return this.currentPersonality;\n  }\n\n  /**\n     * Get current emotion\n     */\n  public getCurrentEmotion(): GoddessEmotion {\n    return this.currentEmotion;\n  }\n\n  /**\n     * Update context\n     */\n  public updateContext(context: Partial<GoddessContext>): void {\n    this.context = { ...this.context, ...context };\n  }\n\n  /**\n     * Get greeting message\n     */\n  public getGreeting(): string {\n    if (!this.isActive) {\n      return 'Hello! I\\'m Codessa, your AI coding assistant.';\n    }\n\n    const traits = this.personalityTraits.get(this.currentPersonality);\n    return traits?.greeting || 'Greetings! Goddess Mode is active.';\n  }\n}\n"]}