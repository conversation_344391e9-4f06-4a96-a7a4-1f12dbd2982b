{"version": 3, "file": "memoryVectorStore.js", "sourceRoot": "", "sources": ["../../../../src/memory/codessa/vectorStores/memoryVectorStore.ts"], "names": [], "mappings": ";;;AACA,yEAAkE;AAElE,4CAAyC;AAQzC;;GAEG;AACH,MAAa,iBAAiB;IACpB,SAAS,GAAe,EAAE,CAAC;IAC3B,UAAU,CAAa;IAE/B,YAAY,UAAsB;QAChC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC;YACH,4CAA4C;YAC5C,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACzC,eAAM,CAAC,IAAI,CAAC,8DAA8D,CAAC,CAAC;QAC9E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,YAAY,CAAC,SAAqB;QACvC,IAAI,CAAC;YACH,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC;YAClC,eAAM,CAAC,KAAK,CAAC,SAAS,SAAS,CAAC,MAAM,mCAAmC,CAAC,CAAC;QAC7E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,SAAS,CAAC,EAAU,EAAE,MAAgB,EAAE,WAAoC,EAAE;QACzF,IAAI,CAAC;YACH,2BAA2B;YAC3B,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;YAExB,0CAA0C;YAC1C,MAAM,QAAQ,GAAG,IAAI,uBAAQ,CAAC;gBAC5B,WAAW,EAAG,QAAQ,CAAC,OAAkB,IAAI,EAAE;gBAC/C,QAAQ,EAAE;oBACR,GAAG,QAAQ;oBACX,EAAE;oBACF,gBAAgB,EAAE,MAAM,CAAC,MAAM;oBAC/B,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC;iBACvE;aACF,CAAC,CAAC;YAEH,eAAe;YACf,IAAI,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,sBAAsB;YACrD,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,MAAM,CAAC,MAAM,qCAAqC,CAAC,CAAC;QAClG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,SAAS,CAAC,EAAU;QAC/B,IAAI,CAAC;YACH,2BAA2B;YAC3B,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;YACxB,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,yCAAyC,CAAC,CAAC;YACnF,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,SAAS,CAAC;QACnB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,EAAU;QAClC,IAAI,CAAC;YACH,mEAAmE;YACnE,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;YACxB,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;YAC5C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAE,GAAG,CAAC,QAA4B,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;YAC3F,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,aAAa,CAAC;YACtD,eAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;YAC3F,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY;QACvB,IAAI,CAAC;YACH,2BAA2B;YAC3B,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;YACxB,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;YAC5C,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;YACpB,eAAM,CAAC,IAAI,CAAC,qDAAqD,aAAa,aAAa,CAAC,CAAC;QAC/F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,oBAAoB,CAAC,WAAqB,EAAE,UAAU,GAAG,CAAC,EAAE,YAAsC;QAC7G,IAAI,CAAC;YACH,2BAA2B;YAC3B,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;YAExB,wEAAwE;YACxE,eAAM,CAAC,IAAI,CAAC,6DAA6D,WAAW,CAAC,MAAM,kBAAkB,UAAU,aAAa,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;YAC1K,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,gBAAgB,CAAC,KAAa,EAAE,CAAC,GAAG,CAAC;QAChD,IAAI,CAAC;YACH,0CAA0C;YAC1C,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAExC,mDAAmD;YACnD,4EAA4E;YAC5E,eAAM,CAAC,KAAK,CAAC,4CAA4C,KAAK,YAAY,CAAC,EAAE,CAAC,CAAC;YAC/E,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAC5C,WAAW,EAAG,GAA0C,CAAC,WAAW;gBACpE,QAAQ,EAAE,GAAG,CAAC,QAAQ;aACvB,CAAC,CAAC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;CACF;AArJD,8CAqJC", "sourcesContent": ["import type { Embeddings } from '../../../agents/workflows/corePolyfill';\nimport { Document } from '../../../agents/workflows/corePolyfill';\nimport type { IVectorStore } from '../../types';\nimport { logger } from '../../../logger';\n\nexport interface SearchResult {\n  pageContent: string;\n  metadata: Record<string, unknown>;\n  score?: number;\n}\n\n/**\n * In-memory vector store implementation\n */\nexport class MemoryVectorStore implements IVectorStore {\n  private documents: Document[] = [];\n  private embeddings: Embeddings;\n\n  constructor(embeddings: Embeddings) {\n    this.embeddings = embeddings;\n  }\n\n  /**\n     * Initialize the vector store\n     */\n  public async initialize(): Promise<void> {\n    try {\n      // Test embeddings to ensure they're working\n      await this.embeddings.embedQuery('test');\n      logger.info('Memory vector store initialized successfully with embeddings');\n    } catch (error) {\n      logger.error('Failed to initialize memory vector store:', error);\n      throw error;\n    }\n  }\n\n  /**\n     * Add documents\n     */\n  public addDocuments(documents: Document[]): void {\n    try {\n      this.documents.push(...documents);\n      logger.debug(`Added ${documents.length} documents to memory vector store`);\n    } catch (error) {\n      logger.error('Failed to add documents:', error);\n      throw error;\n    }\n  }\n\n  /**\n     * Add a vector\n     */\n  public async addVector(id: string, vector: number[], metadata: Record<string, unknown> = {}): Promise<void> {\n    try {\n      // Simulate async operation\n      await Promise.resolve();\n\n      // Create document with vector information\n      const document = new Document({\n        pageContent: (metadata.content as string) || '',\n        metadata: {\n          ...metadata,\n          id,\n          vectorDimensions: vector.length,\n          vectorNorm: Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0))\n        }\n      });\n\n      // Add document\n      this.addDocuments([document]); // Document[] expected\n      logger.debug(`Added vector with ID ${id} (${vector.length} dimensions) to memory vector store`);\n    } catch (error) {\n      logger.error(`Failed to add vector with ID ${id}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n     * Get a vector by ID\n     */\n  public async getVector(id: string): Promise<number[] | undefined> {\n    try {\n      // Simulate async operation\n      await Promise.resolve();\n      logger.warn(`Getting vectors by ID (${id}) is not supported by MemoryVectorStore`);\n      return undefined;\n    } catch (error) {\n      logger.error(`Failed to get vector with ID ${id}:`, error);\n      return undefined;\n    }\n  }\n\n  /**\n     * Delete a vector by ID\n     */\n  public async deleteVector(id: string): Promise<boolean> {\n    try {\n      // Simulate async operation and attempt to find and remove document\n      await Promise.resolve();\n      const initialLength = this.documents.length;\n      this.documents = this.documents.filter(doc => (doc.metadata as { id?: string }).id !== id);\n      const removed = this.documents.length < initialLength;\n      logger.warn(`Delete vector by ID (${id}): ${removed ? 'found and removed' : 'not found'}`);\n      return removed;\n    } catch (error) {\n      logger.error(`Failed to delete vector with ID ${id}:`, error);\n      return false;\n    }\n  }\n\n  /**\n     * Clear all vectors\n     */\n  public async clearVectors(): Promise<void> {\n    try {\n      // Simulate async operation\n      await Promise.resolve();\n      const documentCount = this.documents.length;\n      this.documents = [];\n      logger.info(`Memory vector store cleared successfully (removed ${documentCount} documents)`);\n    } catch (error) {\n      logger.error('Failed to clear memory vector store:', error);\n      throw error;\n    }\n  }\n\n  /**\n     * Search for similar vectors\n     */\n  public async searchSimilarVectors(queryVector: number[], maxResults = 5, searchFilter?: Record<string, unknown>): Promise<{ id: string; score: number }[]> {\n    try {\n      // Simulate async operation\n      await Promise.resolve();\n\n      // Basic implementation: return empty results with logging of parameters\n      logger.warn(`Vector search not fully implemented. Query vector length: ${queryVector.length}, max results: ${maxResults}, filter: ${searchFilter ? 'provided' : 'none'}`);\n      return [];\n    } catch (error) {\n      logger.error('Failed to search similar vectors:', error);\n      return [];\n    }\n  }\n\n  /**\n     * Search for similar documents\n     */\n  public async similaritySearch(query: string, k = 4): Promise<SearchResult[]> {\n    try {\n      // Use embeddings to generate query vector\n      await this.embeddings.embedQuery(query);\n\n      // Simple implementation that returns all documents\n      // In a real implementation, this would use embeddings and vector similarity\n      logger.debug(`Performing similarity search for query: \"${query}\" with k=${k}`);\n      return this.documents.slice(0, k).map(doc => ({\n        pageContent: (doc as Document & { pageContent: string }).pageContent,\n        metadata: doc.metadata\n      }));\n    } catch (error) {\n      logger.error('Failed to perform similarity search:', error);\n      return [];\n    }\n  }\n}\n"]}