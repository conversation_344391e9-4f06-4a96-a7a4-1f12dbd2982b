{"version": 3, "file": "ttsSettingsSection.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/sections/ttsSettingsSection.ts"], "names": [], "mappings": ";AAAA,kCAAkC;;AAqBlC,4DA2BC;AA9CD,kDAAkD;AAClD,SAAS,aAAa,CAAC,GAA8B;IACnD,OAAO,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;AAC5C,CAAC;AAUD,gDAAqE;AAErE,IAAI,SAAS,GAAe,EAAE,CAAC;AAC/B,IAAI,aAAa,GAAkB,IAAI,CAAC;AACxC,MAAM,YAAY,GAA6B,kCAAoB,CAAC,OAAO,CAAC;AAE5E,SAAgB,wBAAwB,CAAC,SAAsB,EAAE,QAAa;IAC5E,qBAAqB;IACrB,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;IAExE,oCAAoC;IACpC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAEvC,uBAAuB;IACvB,MAAM,MAAM,GAAG,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;IACpD,IAAI,MAAM;QAAE,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,YAAY,CAAC,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;IACrE,gBAAgB;IAChB,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;IAC1D,IAAI,SAAS;QAAE,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;IACjE,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;IACtD,IAAI,OAAO;QAAE,OAAO,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAElE,4BAA4B;IAC5B,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAsB,CAAC;IACnF,IAAI,cAAc,EAAE,CAAC;QACnB,cAAc,CAAC,QAAQ,GAAG,GAAG,EAAE,CAAC,kBAAkB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IAC1E,CAAC;IAED,kBAAkB;IAClB,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;IACtD,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC5C,CAAC;AACH,CAAC;AAED,SAAS,cAAc,CAAC,SAAsB;IAC5C,MAAM,OAAO,GAAG,SAAS,CAAC,aAAa,CAAC,aAAa,CAAgB,CAAC;IACtE,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACzC,OAAO,CAAC,SAAS,GAAG,uDAAuD,CAAC;QAC5E,OAAO;IACT,CAAC;IACD,IAAI,WAAW,GAAG;;;0BAGM,YAAY,CAAC,QAAQ;qBAC1B,YAAY,CAAC,WAAW;;;;;;yBAMpB,YAAY,CAAC,MAAM,CAAC,UAAU;oBACnC,YAAY,CAAC,MAAM,CAAC,KAAK;qBACxB,YAAY,CAAC,MAAM,CAAC,MAAM;4BACnB,YAAY,CAAC,MAAM,CAAC,YAAY;;;;aAI/C,CAAC;IACZ,WAAW,IAAI,uCAAuC;QAChD,kGAAkG;QAClG,sBAAsB,CAAC;IAC7B,SAAS,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QAC7B,WAAW,IAAI;kBACD,GAAG,CAAC,IAAI,IAAI,EAAE;kBACd,GAAG,CAAC,QAAQ,IAAI,EAAE;kBAClB,GAAG,CAAC,QAAQ,IAAI,EAAE;kBAClB,GAAG,CAAC,OAAO,IAAI,EAAE;kBACjB,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;;mDAEO,GAAG;qDACD,GAAG;;cAE1C,CAAC;IACb,CAAC,CAAC,CAAC;IACH,WAAW,IAAI,kBAAkB,CAAC;IAClC,OAAO,CAAC,SAAS,GAAG,WAAW,CAAC;IAChC,qCAAqC;IACrC,OAAO,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC1D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAClC,MAAM,OAAO,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YACpE,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;YAC7C,YAAY,CAAC,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IACH,OAAO,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;QAC5D,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;YAClC,MAAM,OAAO,GAAI,CAAC,CAAC,MAAsB,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACtE,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;YAC7C,SAAS,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,YAAY,CAAC,SAAsB,EAAE,GAAsB,EAAE,GAAkB;IACtF,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IAClD,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;IACvD,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAA4B,CAAC;IAChF,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAA4B,CAAC;IACxF,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAA4B,CAAC;IACxF,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAA4B,CAAC;IACtF,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAA4B,CAAC;IACtF,IAAI,KAAK;QAAE,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IACxC,IAAI,KAAK;QAAE,KAAK,CAAC,SAAS,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,gBAAgB,CAAC;IAC9E,IAAI,SAAS;QAAE,SAAS,CAAC,KAAK,GAAG,GAAG,EAAE,IAAI,IAAI,EAAE,CAAC;IACjD,IAAI,aAAa;QAAE,aAAa,CAAC,KAAK,GAAG,GAAG,EAAE,QAAQ,IAAI,EAAE,CAAC;IAC7D,IAAI,aAAa;QAAE,aAAa,CAAC,KAAK,GAAG,GAAG,EAAE,QAAQ,IAAI,EAAE,CAAC;IAC7D,IAAI,YAAY;QAAE,YAAY,CAAC,KAAK,GAAG,GAAG,EAAE,OAAO,IAAI,EAAE,CAAC;IAC1D,IAAI,YAAY;QAAE,YAAY,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC;IACxD,aAAa,GAAG,GAAG,CAAC;AACtB,CAAC;AAED,SAAS,YAAY,CAAC,SAAsB;IAC1C,MAAM,KAAK,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;IAClD,IAAI,KAAK;QAAE,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IACxC,uCAAuC;IACvC,MAAM,MAAM,GAAG,SAAS,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;IAC/D,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACxC,aAAa,GAAG,IAAI,CAAC;AACvB,CAAC;AAED,SAAS,OAAO,CAAC,SAAsB,EAAE,QAAa;IACpD,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAA4B,CAAC;IAChF,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAA4B,CAAC;IACxF,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAA4B,CAAC;IACxF,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAA4B,CAAC;IACtF,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAA4B,CAAC;IACtF,MAAM,GAAG,GAAa;QACpB,IAAI,EAAE,SAAS,EAAE,KAAK,IAAI,EAAE;QAC5B,QAAQ,EAAE,aAAa,EAAE,KAAK,IAAI,EAAE;QACpC,QAAQ,EAAE,aAAa,EAAE,KAAK,IAAI,EAAE;QACpC,OAAO,EAAE,YAAY,EAAE,KAAK,IAAI,EAAE;QAClC,OAAO,EAAE,YAAY,EAAE,OAAO,IAAI,KAAK;KACxC,CAAC;IACF,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;QAC1B,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACtB,CAAC;SAAM,CAAC;QACN,SAAS,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC;IACjC,CAAC;IACD,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC;IAC/B,YAAY,CAAC,SAAS,CAAC,CAAC;IACxB,cAAc,CAAC,SAAS,CAAC,CAAC;AAC5B,CAAC;AAED,+CAAgD;AAEhD,SAAS,SAAS,CAAC,SAAsB,EAAE,GAAW;IACpD,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,EAAE,IAAI,IAAI,gBAAgB,CAAC;IACzD,IAAA,iBAAS,EAAC;QACR,KAAK,EAAE,kBAAkB;QACzB,OAAO,EAAE,kDAAkD,OAAO,2BAA2B;QAC7F,SAAS,EAAE,GAAG,EAAE;YACd,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YACzB,MAAM,QAAQ,GAAI,MAAc,CAAC,QAAQ,IAAI,EAAE,CAAC;YAChD,QAAQ,CAAC,SAAS,GAAG,SAAS,CAAC;YAC/B,cAAc,CAAC,SAAS,CAAC,CAAC;QAC5B,CAAC;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,iBAAiB,CAAC,SAAsB,EAAE,QAAa;IAC9D,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,IAAI,EAAE,CAAC;IAEvC,MAAM,WAAW,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qDA8B+B,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;;;;;;uDAMlC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;;;;;;;;uCAQtD,WAAW,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;qCACrD,WAAW,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;uCAC/C,WAAW,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;uCACnD,WAAW,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;2CAC/C,WAAW,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;qEAY7B,WAAW,CAAC,IAAI,IAAI,GAAG;qFACP,WAAW,CAAC,IAAI,IAAI,GAAG;;;iEAG3C,WAAW,CAAC,KAAK,IAAI,GAAG;sFACH,WAAW,CAAC,KAAK,IAAI,GAAG;;;oEAG1C,WAAW,CAAC,MAAM,IAAI,GAAG;uFACN,WAAW,CAAC,MAAM,IAAI,GAAG;;;;;;;;;;;;;;;;;;;4BAmBpF,WAAW,CAAC,OAAO,EAAE,MAAM,IAAI,EAAE;;;;;;;;;;;4BAWjC,WAAW,CAAC,OAAO,EAAE,MAAM,IAAI,EAAE;;;;;;;;;;;4BAWjC,WAAW,CAAC,OAAO,EAAE,UAAU,IAAI,EAAE;;;;;;;;;;;;wDAYT,WAAW,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;;;;;;2DAM/C,WAAW,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;;;;;;;;;qCAS3E,WAAW,CAAC,QAAQ,EAAE,WAAW,KAAK,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;yCAC1D,WAAW,CAAC,QAAQ,EAAE,WAAW,KAAK,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;qCACtE,WAAW,CAAC,QAAQ,EAAE,WAAW,KAAK,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;;;;;yDAK1C,WAAW,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;;;;;;;GAOzG,CAAC;IAEF,SAAS,CAAC,SAAS,GAAG,WAAW,CAAC;IAElC,4CAA4C;IAC5C,sBAAsB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC9C,CAAC;AAED,SAAS,sBAAsB,CAAC,SAAsB,EAAE,QAAa;IACnE,8CAA8C;IAC9C,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAqB,CAAC;IAC1E,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAqB,CAAC;IAC5E,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAqB,CAAC;IAE9E,IAAI,UAAU,EAAE,CAAC;QACf,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACxC,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YACvD,IAAI,SAAS;gBAAE,SAAS,CAAC,WAAW,GAAG,UAAU,CAAC,KAAK,CAAC;QAC1D,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,WAAW,EAAE,CAAC;QAChB,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACzC,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;YACzD,IAAI,UAAU;gBAAE,UAAU,CAAC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC;QAC7D,CAAC,CAAC,CAAC;IACL,CAAC;IAED,IAAI,YAAY,EAAE,CAAC;QACjB,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YAC1C,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;YAC3D,IAAI,WAAW;gBAAE,WAAW,CAAC,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC;QAChE,CAAC,CAAC,CAAC;IACL,CAAC;IAED,2BAA2B;IAC3B,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAsB,CAAC;IACnF,IAAI,cAAc,EAAE,CAAC;QACnB,cAAc,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,EAAE;YAC7C,oBAAoB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;YAC3C,kBAAkB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QACH,6BAA6B;QAC7B,oBAAoB,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IAC7C,CAAC;AACH,CAAC;AAED,SAAS,oBAAoB,CAAC,QAAgB;IAC5C,4BAA4B;IAC5B,MAAM,OAAO,GAAG,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;IAC9D,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;IAE7D,gCAAgC;IAChC,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,GAAG,QAAQ,QAAQ,CAAC,CAAC;IACpE,IAAI,cAAc,EAAE,CAAC;QACnB,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACzC,CAAC;AACH,CAAC;AAED,SAAS,kBAAkB,CAAC,SAAsB,EAAE,QAAa;IAC/D,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAsB,CAAC;IACnF,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAsB,CAAC;IAE7E,IAAI,CAAC,cAAc,IAAI,CAAC,WAAW;QAAE,OAAO;IAE5C,MAAM,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC;IAEtC,yBAAyB;IACzB,WAAW,CAAC,SAAS,GAAG,6CAA6C,CAAC;IAEtE,8BAA8B;IAC9B,IAAK,MAAc,CAAC,MAAM,EAAE,CAAC;QAC1B,MAAc,CAAC,MAAM,CAAC,WAAW,CAAC;YACjC,OAAO,EAAE,cAAc;YACvB,QAAQ,EAAE,QAAQ;SACnB,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED,SAAS,OAAO,CAAC,QAAa;IAC5B,MAAM,QAAQ,GAAG,wEAAwE,CAAC;IAE1F,IAAK,MAAc,CAAC,MAAM,EAAE,CAAC;QAC1B,MAAc,CAAC,MAAM,CAAC,WAAW,CAAC;YACjC,OAAO,EAAE,SAAS;YAClB,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,qBAAqB,EAAE;SAClC,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED,SAAS,qBAAqB;IAC5B,MAAM,OAAO,GAAI,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAsB,EAAE,OAAO,IAAI,KAAK,CAAC;IAC9F,MAAM,SAAS,GAAI,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAsB,EAAE,OAAO,IAAI,KAAK,CAAC;IAClG,MAAM,QAAQ,GAAI,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAuB,EAAE,KAAK,IAAI,QAAQ,CAAC;IAClG,MAAM,KAAK,GAAI,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAuB,EAAE,KAAK,IAAI,EAAE,CAAC;IACtF,MAAM,IAAI,GAAG,UAAU,CAAE,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAsB,EAAE,KAAK,IAAI,KAAK,CAAC,CAAC;IAClG,MAAM,KAAK,GAAG,UAAU,CAAE,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAsB,EAAE,KAAK,IAAI,KAAK,CAAC,CAAC;IACpG,MAAM,MAAM,GAAG,UAAU,CAAE,QAAQ,CAAC,cAAc,CAAC,WAAW,CAAsB,EAAE,KAAK,IAAI,KAAK,CAAC,CAAC;IAEtG,MAAM,OAAO,GAAG;QACd,MAAM,EAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAsB,EAAE,KAAK,IAAI,EAAE;QAClF,MAAM,EAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAsB,EAAE,KAAK,IAAI,EAAE;QAClF,UAAU,EAAG,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAsB,EAAE,KAAK,IAAI,EAAE;KAC3F,CAAC;IAEF,MAAM,QAAQ,GAAG;QACf,WAAW,EAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAsB,EAAE,OAAO,IAAI,KAAK;QAC3F,cAAc,EAAG,QAAQ,CAAC,cAAc,CAAC,gBAAgB,CAAsB,EAAE,OAAO,IAAI,KAAK;QACjG,WAAW,EAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAuB,EAAE,KAAK,IAAI,UAAU;QAC/F,YAAY,EAAG,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAsB,EAAE,OAAO,IAAI,IAAI;QAC5F,gBAAgB,EAAE,IAAI;KACvB,CAAC;IAEF,OAAO;QACL,OAAO;QACP,SAAS;QACT,QAAQ;QACR,KAAK;QACL,IAAI;QACJ,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;KACT,CAAC;AACJ,CAAC", "sourcesContent": ["// TTS section logic and rendering\n\n// Utility to ensure parseInt always gets a string\nfunction safeGetString(val: string | null | undefined): string {\n  return typeof val === 'string' ? val : '';\n}\n\nexport type TTSVoice = {\n    name: string;\n    provider: string;\n    language: string;\n    voiceId: string;\n    enabled: boolean;\n};\n\nimport { defaultUIThemeConfig, UIThemeConfig } from '../themeConfig';\n\nlet ttsVoices: TTSVoice[] = [];\nlet editingTtsIdx: number | null = null;\nconst sectionTheme: UIThemeConfig['section'] = defaultUIThemeConfig.section;\n\nexport function renderTTSSettingsSection(container: HTMLElement, settings: any) {\n  // Sync from settings\n  ttsVoices = Array.isArray(settings.ttsVoices) ? settings.ttsVoices : [];\n\n  // Render comprehensive TTS settings\n  renderTTSSettings(container, settings);\n\n  // Add button listeners\n  const addBtn = document.getElementById('addTTSBtn');\n  if (addBtn) addBtn.onclick = () => showTTSModal(container, {}, null);\n  // Modal buttons\n  const cancelBtn = document.getElementById('cancelTTSBtn');\n  if (cancelBtn) cancelBtn.onclick = () => hideTTSModal(container);\n  const saveBtn = document.getElementById('saveTTSBtn');\n  if (saveBtn) saveBtn.onclick = () => saveTTS(container, settings);\n\n  // Provider change listeners\n  const providerSelect = document.getElementById('ttsProvider') as HTMLSelectElement;\n  if (providerSelect) {\n    providerSelect.onchange = () => updateVoiceOptions(container, settings);\n  }\n\n  // Test TTS button\n  const testBtn = document.getElementById('testTTSBtn');\n  if (testBtn) {\n    testBtn.onclick = () => testTTS(settings);\n  }\n}\n\nfunction renderTTSTable(container: HTMLElement) {\n  const section = container.querySelector('#ttsSection') as HTMLElement;\n  if (!ttsVoices || ttsVoices.length === 0) {\n    section.innerHTML = '<div style=\"color:#aaa;\">No TTS voices defined.</div>';\n    return;\n  }\n  let htmlContent = `<style>\n        .crud-table th, .crud-table td { padding: 6px 10px; }\n        .crud-table th {\n            background: ${sectionTheme.headerBg};\n            color: ${sectionTheme.headerColor};\n            font-weight: 600;\n        }\n        .crud-table tbody tr:nth-child(even) { background: #fafbfc; }\n        .crud-table tbody tr:hover { background: #e8f0fe; }\n        .btn-tts {\n            background:${sectionTheme.button.background};\n            color:${sectionTheme.button.color};\n            border:${sectionTheme.button.border};\n            border-radius:${sectionTheme.button.borderRadius};\n            padding:3px 10px; margin:0 2px; font-size:1em; cursor:pointer; transition:background 0.15s;\n        }\n        .btn-tts:hover { background:#1d4ed8; }\n    </style>`;\n  htmlContent += '<table class=\"crud-table\"><thead><tr>' +\n        '<th>Name</th><th>Provider</th><th>Language</th><th>Voice ID</th><th>Enabled</th><th>Actions</th>' +\n        '</tr></thead><tbody>';\n  ttsVoices.forEach((tts, idx) => {\n    htmlContent += `<tr>\n            <td>${tts.name || ''}</td>\n            <td>${tts.provider || ''}</td>\n            <td>${tts.language || ''}</td>\n            <td>${tts.voiceId || ''}</td>\n            <td>${tts.enabled ? 'Yes' : 'No'}</td>\n            <td>\n                <button type=\"button\" data-edit=\"${idx}\">Edit</button>\n                <button type=\"button\" data-delete=\"${idx}\">Delete</button>\n            </td>\n        </tr>`;\n  });\n  htmlContent += '</tbody></table>';\n  section.innerHTML = htmlContent;\n  // Attach edit/delete event listeners\n  section.querySelectorAll('button[data-edit]').forEach(btn => {\n    btn.addEventListener('click', (e) => {\n      const idxAttr = (e.target as HTMLElement).getAttribute('data-edit');\n      const idx = parseInt(safeGetString(idxAttr));\n      showTTSModal(container, ttsVoices[idx], idx);\n    });\n  });\n  section.querySelectorAll('button[data-delete]').forEach(btn => {\n    btn.addEventListener('click', (e) => {\n      const idxAttr = (e.target as HTMLElement).getAttribute('data-delete');\n      const idx = parseInt(safeGetString(idxAttr));\n      deleteTTS(container, idx);\n    });\n  });\n}\n\nfunction showTTSModal(container: HTMLElement, tts: Partial<TTSVoice>, idx: number | null) {\n  const modal = document.getElementById('ttsModal');\n  const title = document.getElementById('ttsModalTitle');\n  const nameInput = document.getElementById('ttsName') as HTMLInputElement | null;\n  const providerInput = document.getElementById('ttsProvider') as HTMLInputElement | null;\n  const languageInput = document.getElementById('ttsLanguage') as HTMLInputElement | null;\n  const voiceIdInput = document.getElementById('ttsVoiceId') as HTMLInputElement | null;\n  const enabledInput = document.getElementById('ttsEnabled') as HTMLInputElement | null;\n  if (modal) modal.style.display = 'flex';\n  if (title) title.innerText = idx == null ? 'Add TTS Voice' : 'Edit TTS Voice';\n  if (nameInput) nameInput.value = tts?.name || '';\n  if (providerInput) providerInput.value = tts?.provider || '';\n  if (languageInput) languageInput.value = tts?.language || '';\n  if (voiceIdInput) voiceIdInput.value = tts?.voiceId || '';\n  if (enabledInput) enabledInput.checked = !!tts?.enabled;\n  editingTtsIdx = idx;\n}\n\nfunction hideTTSModal(container: HTMLElement) {\n  const modal = document.getElementById('ttsModal');\n  if (modal) modal.style.display = 'none';\n  // Clear validation errors in container\n  const errors = container.querySelectorAll('.validation-error');\n  errors.forEach(error => error.remove());\n  editingTtsIdx = null;\n}\n\nfunction saveTTS(container: HTMLElement, settings: any) {\n  const nameInput = document.getElementById('ttsName') as HTMLInputElement | null;\n  const providerInput = document.getElementById('ttsProvider') as HTMLInputElement | null;\n  const languageInput = document.getElementById('ttsLanguage') as HTMLInputElement | null;\n  const voiceIdInput = document.getElementById('ttsVoiceId') as HTMLInputElement | null;\n  const enabledInput = document.getElementById('ttsEnabled') as HTMLInputElement | null;\n  const tts: TTSVoice = {\n    name: nameInput?.value || '',\n    provider: providerInput?.value || '',\n    language: languageInput?.value || '',\n    voiceId: voiceIdInput?.value || '',\n    enabled: enabledInput?.checked || false,\n  };\n  if (editingTtsIdx == null) {\n    ttsVoices.push(tts);\n  } else {\n    ttsVoices[editingTtsIdx] = tts;\n  }\n  settings.ttsVoices = ttsVoices;\n  hideTTSModal(container);\n  renderTTSTable(container);\n}\n\nimport { showModal } from '../components/modal';\n\nfunction deleteTTS(container: HTMLElement, idx: number) {\n  const ttsName = ttsVoices[idx]?.name || 'this TTS voice';\n  showModal({\n    title: 'Delete TTS Voice',\n    content: `Are you sure you want to delete the TTS voice \"${ttsName}\"? This cannot be undone.`,\n    onConfirm: () => {\n      ttsVoices.splice(idx, 1);\n      const settings = (window as any).settings || {};\n      settings.ttsVoices = ttsVoices;\n      renderTTSTable(container);\n    }\n  });\n}\n\nfunction renderTTSSettings(container: HTMLElement, settings: any) {\n  const ttsSettings = settings.tts || {};\n\n  const htmlContent = `\n    <style>\n      .tts-settings { padding: 20px; }\n      .tts-section { margin-bottom: 30px; padding: 15px; border: 1px solid var(--vscode-panel-border); border-radius: 8px; }\n      .tts-section h3 { margin-top: 0; color: var(--vscode-foreground); }\n      .form-group { margin-bottom: 15px; }\n      .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }\n      .form-control { width: 100%; padding: 8px; border: 1px solid var(--vscode-input-border); background: var(--vscode-input-background); color: var(--vscode-input-foreground); border-radius: 4px; }\n      .form-row { display: flex; gap: 15px; }\n      .form-row .form-group { flex: 1; }\n      .checkbox-group { display: flex; align-items: center; gap: 8px; }\n      .btn-primary { background: var(--vscode-button-background); color: var(--vscode-button-foreground); border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; }\n      .btn-primary:hover { background: var(--vscode-button-hoverBackground); }\n      .btn-secondary { background: var(--vscode-button-secondaryBackground); color: var(--vscode-button-secondaryForeground); border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; }\n      .api-key-input { font-family: monospace; }\n      .provider-config { display: none; padding: 10px; background: var(--vscode-editor-inactiveSelectionBackground); border-radius: 4px; margin-top: 10px; }\n      .provider-config.active { display: block; }\n      .voice-preview { display: flex; align-items: center; gap: 10px; }\n      .quality-indicator { padding: 2px 6px; border-radius: 3px; font-size: 12px; }\n      .quality-high { background: #22c55e; color: white; }\n      .quality-medium { background: #f59e0b; color: white; }\n      .quality-low { background: #ef4444; color: white; }\n    </style>\n\n    <div class=\"tts-settings\">\n      <!-- General TTS Settings -->\n      <div class=\"tts-section\">\n        <h3>🔊 General Settings</h3>\n        <div class=\"form-group\">\n          <div class=\"checkbox-group\">\n            <input type=\"checkbox\" id=\"ttsEnabled\" ${ttsSettings.enabled ? 'checked' : ''}>\n            <label for=\"ttsEnabled\">Enable Text-to-Speech</label>\n          </div>\n        </div>\n        <div class=\"form-group\">\n          <div class=\"checkbox-group\">\n            <input type=\"checkbox\" id=\"ttsAutoSpeak\" ${ttsSettings.autoSpeak ? 'checked' : ''}>\n            <label for=\"ttsAutoSpeak\">Auto-speak assistant responses</label>\n          </div>\n        </div>\n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"ttsProvider\">Provider</label>\n            <select id=\"ttsProvider\" class=\"form-control\">\n              <option value=\"system\" ${ttsSettings.provider === 'system' ? 'selected' : ''}>System TTS</option>\n              <option value=\"edge\" ${ttsSettings.provider === 'edge' ? 'selected' : ''}>Microsoft Edge</option>\n              <option value=\"google\" ${ttsSettings.provider === 'google' ? 'selected' : ''}>Google Cloud TTS</option>\n              <option value=\"openai\" ${ttsSettings.provider === 'openai' ? 'selected' : ''}>OpenAI TTS</option>\n              <option value=\"elevenlabs\" ${ttsSettings.provider === 'elevenlabs' ? 'selected' : ''}>ElevenLabs</option>\n            </select>\n          </div>\n          <div class=\"form-group\">\n            <label for=\"ttsVoice\">Voice</label>\n            <select id=\"ttsVoice\" class=\"form-control\">\n              <option value=\"\">Select a voice...</option>\n            </select>\n          </div>\n        </div>\n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"ttsRate\">Speech Rate: <span id=\"rateValue\">${ttsSettings.rate || 1.0}</span></label>\n            <input type=\"range\" id=\"ttsRate\" min=\"0.5\" max=\"2.0\" step=\"0.1\" value=\"${ttsSettings.rate || 1.0}\" class=\"form-control\">\n          </div>\n          <div class=\"form-group\">\n            <label for=\"ttsPitch\">Pitch: <span id=\"pitchValue\">${ttsSettings.pitch || 1.0}</span></label>\n            <input type=\"range\" id=\"ttsPitch\" min=\"0.5\" max=\"2.0\" step=\"0.1\" value=\"${ttsSettings.pitch || 1.0}\" class=\"form-control\">\n          </div>\n          <div class=\"form-group\">\n            <label for=\"ttsVolume\">Volume: <span id=\"volumeValue\">${ttsSettings.volume || 1.0}</span></label>\n            <input type=\"range\" id=\"ttsVolume\" min=\"0.0\" max=\"1.0\" step=\"0.1\" value=\"${ttsSettings.volume || 1.0}\" class=\"form-control\">\n          </div>\n        </div>\n        <div class=\"voice-preview\">\n          <button type=\"button\" id=\"testTTSBtn\" class=\"btn-secondary\">🎵 Test Voice</button>\n          <span class=\"quality-indicator quality-high\">High Quality</span>\n        </div>\n      </div>\n\n      <!-- Provider-specific configurations -->\n      <div class=\"tts-section\">\n        <h3>🔑 Provider Configuration</h3>\n\n        <!-- Google Cloud TTS -->\n        <div id=\"googleConfig\" class=\"provider-config\">\n          <h4>Google Cloud TTS</h4>\n          <div class=\"form-group\">\n            <label for=\"googleApiKey\">API Key</label>\n            <input type=\"password\" id=\"googleApiKey\" class=\"form-control api-key-input\"\n                   value=\"${ttsSettings.apiKeys?.google || ''}\"\n                   placeholder=\"Enter Google Cloud TTS API key\">\n          </div>\n        </div>\n\n        <!-- OpenAI TTS -->\n        <div id=\"openaiConfig\" class=\"provider-config\">\n          <h4>OpenAI TTS</h4>\n          <div class=\"form-group\">\n            <label for=\"openaiApiKey\">API Key</label>\n            <input type=\"password\" id=\"openaiApiKey\" class=\"form-control api-key-input\"\n                   value=\"${ttsSettings.apiKeys?.openai || ''}\"\n                   placeholder=\"Enter OpenAI API key\">\n          </div>\n        </div>\n\n        <!-- ElevenLabs -->\n        <div id=\"elevenlabsConfig\" class=\"provider-config\">\n          <h4>ElevenLabs</h4>\n          <div class=\"form-group\">\n            <label for=\"elevenlabsApiKey\">API Key</label>\n            <input type=\"password\" id=\"elevenlabsApiKey\" class=\"form-control api-key-input\"\n                   value=\"${ttsSettings.apiKeys?.elevenlabs || ''}\"\n                   placeholder=\"Enter ElevenLabs API key\">\n          </div>\n        </div>\n      </div>\n\n      <!-- Advanced Settings -->\n      <div class=\"tts-section\">\n        <h3>⚙️ Advanced Settings</h3>\n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <div class=\"checkbox-group\">\n              <input type=\"checkbox\" id=\"ssmlEnabled\" ${ttsSettings.advanced?.ssmlEnabled ? 'checked' : ''}>\n              <label for=\"ssmlEnabled\">Enable SSML (Speech Synthesis Markup Language)</label>\n            </div>\n          </div>\n          <div class=\"form-group\">\n            <div class=\"checkbox-group\">\n              <input type=\"checkbox\" id=\"emotionControl\" ${ttsSettings.advanced?.emotionControl ? 'checked' : ''}>\n              <label for=\"emotionControl\">Emotion Control</label>\n            </div>\n          </div>\n        </div>\n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label for=\"qualityMode\">Quality Mode</label>\n            <select id=\"qualityMode\" class=\"form-control\">\n              <option value=\"fast\" ${ttsSettings.advanced?.qualityMode === 'fast' ? 'selected' : ''}>Fast</option>\n              <option value=\"balanced\" ${ttsSettings.advanced?.qualityMode === 'balanced' ? 'selected' : ''}>Balanced</option>\n              <option value=\"high\" ${ttsSettings.advanced?.qualityMode === 'high' ? 'selected' : ''}>High Quality</option>\n            </select>\n          </div>\n          <div class=\"form-group\">\n            <div class=\"checkbox-group\">\n              <input type=\"checkbox\" id=\"cacheEnabled\" ${ttsSettings.advanced?.cacheEnabled ? 'checked' : ''}>\n              <label for=\"cacheEnabled\">Enable Audio Caching</label>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  `;\n\n  container.innerHTML = htmlContent;\n\n  // Add event listeners for real-time updates\n  setupTTSEventListeners(container, settings);\n}\n\nfunction setupTTSEventListeners(container: HTMLElement, settings: any) {\n  // Range input listeners for real-time updates\n  const rateSlider = document.getElementById('ttsRate') as HTMLInputElement;\n  const pitchSlider = document.getElementById('ttsPitch') as HTMLInputElement;\n  const volumeSlider = document.getElementById('ttsVolume') as HTMLInputElement;\n\n  if (rateSlider) {\n    rateSlider.addEventListener('input', () => {\n      const rateValue = document.getElementById('rateValue');\n      if (rateValue) rateValue.textContent = rateSlider.value;\n    });\n  }\n\n  if (pitchSlider) {\n    pitchSlider.addEventListener('input', () => {\n      const pitchValue = document.getElementById('pitchValue');\n      if (pitchValue) pitchValue.textContent = pitchSlider.value;\n    });\n  }\n\n  if (volumeSlider) {\n    volumeSlider.addEventListener('input', () => {\n      const volumeValue = document.getElementById('volumeValue');\n      if (volumeValue) volumeValue.textContent = volumeSlider.value;\n    });\n  }\n\n  // Provider change listener\n  const providerSelect = document.getElementById('ttsProvider') as HTMLSelectElement;\n  if (providerSelect) {\n    providerSelect.addEventListener('change', () => {\n      updateProviderConfig(providerSelect.value);\n      updateVoiceOptions(container, settings);\n    });\n    // Initialize provider config\n    updateProviderConfig(providerSelect.value);\n  }\n}\n\nfunction updateProviderConfig(provider: string) {\n  // Hide all provider configs\n  const configs = document.querySelectorAll('.provider-config');\n  configs.forEach(config => config.classList.remove('active'));\n\n  // Show selected provider config\n  const selectedConfig = document.getElementById(`${provider}Config`);\n  if (selectedConfig) {\n    selectedConfig.classList.add('active');\n  }\n}\n\nfunction updateVoiceOptions(container: HTMLElement, settings: any) {\n  const providerSelect = document.getElementById('ttsProvider') as HTMLSelectElement;\n  const voiceSelect = document.getElementById('ttsVoice') as HTMLSelectElement;\n\n  if (!providerSelect || !voiceSelect) return;\n\n  const provider = providerSelect.value;\n\n  // Clear existing options\n  voiceSelect.innerHTML = '<option value=\"\">Loading voices...</option>';\n\n  // Request voices from backend\n  if ((window as any).vscode) {\n    (window as any).vscode.postMessage({\n      command: 'getTTSVoices',\n      provider: provider\n    });\n  }\n}\n\nfunction testTTS(settings: any) {\n  const testText = 'Hello! This is a test of the text-to-speech system. How does it sound?';\n\n  if ((window as any).vscode) {\n    (window as any).vscode.postMessage({\n      command: 'testTTS',\n      text: testText,\n      settings: getCurrentTTSSettings()\n    });\n  }\n}\n\nfunction getCurrentTTSSettings() {\n  const enabled = (document.getElementById('ttsEnabled') as HTMLInputElement)?.checked || false;\n  const autoSpeak = (document.getElementById('ttsAutoSpeak') as HTMLInputElement)?.checked || false;\n  const provider = (document.getElementById('ttsProvider') as HTMLSelectElement)?.value || 'system';\n  const voice = (document.getElementById('ttsVoice') as HTMLSelectElement)?.value || '';\n  const rate = parseFloat((document.getElementById('ttsRate') as HTMLInputElement)?.value || '1.0');\n  const pitch = parseFloat((document.getElementById('ttsPitch') as HTMLInputElement)?.value || '1.0');\n  const volume = parseFloat((document.getElementById('ttsVolume') as HTMLInputElement)?.value || '1.0');\n\n  const apiKeys = {\n    google: (document.getElementById('googleApiKey') as HTMLInputElement)?.value || '',\n    openai: (document.getElementById('openaiApiKey') as HTMLInputElement)?.value || '',\n    elevenlabs: (document.getElementById('elevenlabsApiKey') as HTMLInputElement)?.value || ''\n  };\n\n  const advanced = {\n    ssmlEnabled: (document.getElementById('ssmlEnabled') as HTMLInputElement)?.checked || false,\n    emotionControl: (document.getElementById('emotionControl') as HTMLInputElement)?.checked || false,\n    qualityMode: (document.getElementById('qualityMode') as HTMLSelectElement)?.value || 'balanced',\n    cacheEnabled: (document.getElementById('cacheEnabled') as HTMLInputElement)?.checked || true,\n    streamingEnabled: true\n  };\n\n  return {\n    enabled,\n    autoSpeak,\n    provider,\n    voice,\n    rate,\n    pitch,\n    volume,\n    apiKeys,\n    advanced\n  };\n}\n"]}