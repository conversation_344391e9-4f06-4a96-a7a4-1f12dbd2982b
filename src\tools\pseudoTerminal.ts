import * as vscode from 'vscode';
import * as os from 'os';
import * as path from 'path';
import { spawn, ChildProcess } from 'child_process';
import { EventEmitter } from 'events';
import { Logger } from '../logger';

// Import code action provider integration
import {
    codeAction<PERSON>anager,
    TerminalCodeActionProvider
} from './codeActionProvider';

// Import advanced AI capabilities
import { ToolRegistry } from './toolRegistry';
import { MemoryManager } from '../memory/memoryManager';
import { workflowRegistry } from '../agents/workflows/workflowEngine';
import { SupervisorAgent } from '../agents/agentTypes/supervisorAgent';
import { llmService } from '../llm/llmService';
import { WebSearchTool } from './webSearchTool';
import { CodeIntelligenceTool } from './codeIntelligenceTool';
import { SecurityVulnerabilityTool } from './codeAnalysisTool';
import { DiagnosticsTool } from './diagnosticsTool';
import { QuantumMemorySystem } from '../memory/quantum/quantumMemorySystem';
import { AgentContext } from '../types/agent';
import { Agent } from '../agents/agentUtilities/agent';

/**
 * Revolutionary AI-Enhanced Pseudo Terminal
 * Surpasses standard terminal capabilities with:
 * - AI-powered command intelligence and suggestions
 * - Memory-based learning and context awareness
 * - Workflow automation and multi-agent collaboration
 * - Real-time web search and documentation lookup
 * - Advanced code analysis and security scanning
 * - Cross-repository intelligence and operations
 * - Quantum memory system for temporal command patterns
 */

/**
 * Advanced interfaces for AI-enhanced terminal integration
 */

// AI context interface for enhanced terminal analysis
export interface IAIContext {
    modelId: string;
    promptTemplate: string;
    temperature: number;
    maxTokens: number;
    stop?: string[];
}

// AI Command Intelligence Interface
export interface IAICommandIntelligence {
    suggestCommand(context: string, intent: string): Promise<string[]>;
    analyzeError(error: string, context: string): Promise<AIErrorAnalysis>;
    predictNextCommand(history: string[], currentContext: string): Promise<string[]>;
    optimizeCommand(command: string, context: string): Promise<string>;
}

// AI Error Analysis Result
export interface AIErrorAnalysis {
    errorType: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    suggestedFixes: string[];
    explanation: string;
    relatedDocumentation: string[];
    confidence: number;
}

// Memory-Enhanced Command Context
export interface IMemoryCommandContext {
    command: string;
    workingDirectory: string;
    timestamp: Date;
    success: boolean;
    output: string;
    userIntent?: string;
    projectContext?: string;
    relatedFiles?: string[];
}

// Workflow Integration Interface
export interface IWorkflowIntegration {
    triggerWorkflow(workflowId: string, context: Record<string, unknown>): Promise<unknown>;
    getAvailableWorkflows(): Promise<string[]>;
    createCustomWorkflow(definition: Record<string, unknown>): Promise<string>;
}

// Web Intelligence Interface
export interface IWebIntelligence {
    searchDocumentation(query: string, technology?: string): Promise<string[]>;
    getStackOverflowSolutions(error: string): Promise<Array<Record<string, unknown>>>;
    fetchLatestBestPractices(technology: string): Promise<string[]>;
}

// Code Intelligence Interface
export interface ICodeIntelligence {
    analyzeCodeQuality(filePath: string): Promise<Record<string, unknown>>;
    generateCode(prompt: string, language: string): Promise<string>;
    refactorCode(filePath: string, instructions: string): Promise<string>;
    detectVulnerabilities(filePath: string): Promise<Array<Record<string, unknown>>>;
}

// Multi-Agent Collaboration Interface
export interface IMultiAgentCollaboration {
    delegateTask(agentType: string, task: string, context: Record<string, unknown>): Promise<unknown>;
    getAgentCapabilities(): Promise<Record<string, string[]>>;
    coordinateAgents(tasks: Array<Record<string, unknown>>): Promise<Array<unknown>>;
}

// Quick fix pattern interface for terminal actions
export interface QuickFixPattern {
    pattern: RegExp;
    action: string;
    priority: number;
    category: string;
    fix: (match: RegExpMatchArray, document: vscode.TextDocument, range: vscode.Range) => vscode.CodeAction;
}

// Code action context for terminal operations
export interface CodeActionContext {
    document: vscode.TextDocument;
    range: vscode.Range;
    diagnostics: readonly vscode.Diagnostic[];
    trigger: 'manual' | 'auto' | 'onSave' | 'onType';
    source: string;
    only?: vscode.CodeActionKind;
}

// Code action result with metadata
export interface CodeActionResult {
    action: vscode.CodeAction;
    priority: number;
    category: string;
    confidence: number;
    metadata: {
        source: string;
        trigger: string;
        context: Record<string, unknown>;
    };
}

// Quick fix icon definition
export interface QuickFixIcon {
    name: string;
    icon: vscode.ThemeIcon;
    category: string;
    description: string;
}

export interface IAIToolPattern extends QuickFixPattern {
    aiContext?: IAIContext;
    contextProvider?: (match: RegExpMatchArray) => Promise<unknown>;
    resultProcessor?: (result: unknown) => vscode.CodeAction | vscode.CodeAction[];
}

export interface IToolTerminalContext {
    session: InteractiveSession;
    provider: TerminalActionProvider;
    actionContext: {
        toolId: string;
        category: string;
        priority: number;
        confidence: number;
    };
}

export interface ITerminalToolEvents extends TerminalEvents {
    onAIAction: vscode.Event<{
        action: vscode.CodeAction;
        context: unknown;
        aiResponse?: unknown;
    }>;
    onPatternMatch: vscode.Event<{
        pattern: IAIToolPattern;
        matches: RegExpMatchArray;
    }>;
}

/**
 * Revolutionary AI-Enhanced Terminal Action Provider
 * Combines code actions with advanced AI capabilities
 */
export class TerminalActionProvider extends TerminalCodeActionProvider implements
    IAICommandIntelligence, IWorkflowIntegration, IWebIntelligence, ICodeIntelligence, IMultiAgentCollaboration {

    private readonly _quickFixHistory: Set<string> = new Set();
    private _outputBuffer: string = '';
    private _outputFlushTimer: NodeJS.Timeout | null = null;
    private _terminalDiagnostics: vscode.DiagnosticCollection;
    private _aiPatterns: Map<string, IAIToolPattern> = new Map();
    private _toolContext: Map<string, IToolTerminalContext> = new Map();

    // AI-Enhanced Components
    private _toolRegistry: ToolRegistry;
    private _memoryManager: MemoryManager;
    private _quantumMemory?: QuantumMemorySystem;
    private _supervisorAgent?: SupervisorAgent;
    private _commandMemory: Map<string, IMemoryCommandContext[]> = new Map();
    private _aiCommandCache: Map<string, { suggestions: string[]; timestamp: number }> = new Map();

    // Event Emitters
    private readonly _onAIAction = new vscode.EventEmitter<{
        action: vscode.CodeAction;
        context: unknown;
        aiResponse?: unknown;
    }>();
    private readonly _onPatternMatch = new vscode.EventEmitter<{
        pattern: IAIToolPattern;
        matches: RegExpMatchArray;
    }>();
    private readonly _onCommandSuggestion = new vscode.EventEmitter<{
        suggestions: string[];
        context: string;
        confidence: number;
    }>();

    public readonly onAIAction = this._onAIAction.event;
    public readonly onPatternMatch = this._onPatternMatch.event;
    public readonly onCommandSuggestion = this._onCommandSuggestion.event;

    public registerToolContext(toolId: string, context: IToolTerminalContext): void {
        this._toolContext.set(toolId, context);
    }

    public getToolContext(toolId: string): IToolTerminalContext | undefined {
        return this._toolContext.get(toolId);
    }

    public updateToolContext(toolId: string, context: Partial<IToolTerminalContext>): void {
        const existing = this._toolContext.get(toolId);
        if (existing) {
            this._toolContext.set(toolId, { ...existing, ...context });
        }
    }

    constructor() {
        super();
        this._terminalDiagnostics = vscode.languages.createDiagnosticCollection('terminal');
        this._toolRegistry = ToolRegistry.instance;
        this._memoryManager = new MemoryManager();

        this.initializeAIComponents();
        this.initializeTerminalPatterns();
        this.registerAdvancedIcons();
    }

    private async initializeAIComponents(): Promise<void> {
        try {
            // Initialize Quantum Memory System if available
            this._quantumMemory = this._memoryManager.getQuantumMemorySystem();
            if (this._quantumMemory) {
                // Quantum memory system is available
            }

            // Initialize Supervisor Agent
            // Note: SupervisorAgent requires receiverAgent, but we'll initialize it later when needed
            // this._supervisorAgent = new SupervisorAgent(supervisorConfig, receiverAgent);

            Logger.instance.info('AI components initialized for terminal');
        } catch (error) {
            Logger.instance.error('Failed to initialize AI components:', error);
        }
    }

    private registerAdvancedIcons(): void {
        // Register terminal-specific icons
        this._registerIcon({
            name: 'terminal-error',
            icon: new vscode.ThemeIcon('error', new vscode.ThemeColor('terminalError.foreground')),
            category: 'terminal-error',
            description: 'Terminal error indicators'
        });

        this._registerIcon({
            name: 'terminal-warning',
            icon: new vscode.ThemeIcon('warning', new vscode.ThemeColor('terminalWarning.foreground')),
            category: 'terminal-warning',
            description: 'Terminal warning indicators'
        });

        this._registerIcon({
            name: 'terminal-success',
            icon: new vscode.ThemeIcon('check', new vscode.ThemeColor('terminalSuccess.foreground')),
            category: 'terminal-success',
            description: 'Terminal success indicators'
        });

        // AI-specific icons
        this._registerIcon({
            name: 'ai-suggestion',
            icon: new vscode.ThemeIcon('lightbulb', new vscode.ThemeColor('editorLightBulb.foreground')),
            category: 'ai-suggestion',
            description: 'AI command suggestions'
        });

        this._registerIcon({
            name: 'workflow-trigger',
            icon: new vscode.ThemeIcon('gear', new vscode.ThemeColor('symbolIcon.operatorForeground')),
            category: 'workflow',
            description: 'Workflow automation triggers'
        });
    }

    // ===== AI COMMAND INTELLIGENCE IMPLEMENTATION =====

    public async suggestCommand(context: string, intent: string): Promise<string[]> {
        const cacheKey = `${context}-${intent}`;
        const cached = this._aiCommandCache.get(cacheKey);

        if (cached && Date.now() - cached.timestamp < 300000) { // 5 minutes cache
            return cached.suggestions;
        }

        try {
            const webSearchTool = this._toolRegistry.getTool('webSearch') as WebSearchTool;
            const codeIntelTool = this._toolRegistry.getTool('codeIntel') as CodeIntelligenceTool;

            // Use AI to analyze context and suggest commands
            const suggestions: string[] = [];

            // Use code intelligence for context-aware suggestions
            if (codeIntelTool && context.includes('code')) {
                try {
                    const codeAnalysis = await codeIntelTool.execute('analyze-context', {
                        context,
                        intent
                    });
                    if (codeAnalysis.suggestions) {
                        suggestions.push(...codeAnalysis.suggestions);
                    }
                } catch (error) {
                    Logger.instance.warn('Code intelligence suggestions failed:', error);
                }
            }

            // Get command suggestions based on context
            if (context.includes('git')) {
                suggestions.push(...await this._getGitSuggestions(intent));
            } else if (context.includes('npm') || context.includes('node')) {
                suggestions.push(...await this._getNodeSuggestions(intent));
            } else if (context.includes('python') || context.includes('pip')) {
                suggestions.push(...await this._getPythonSuggestions(intent));
            }

            // Use web search for additional suggestions
            if (webSearchTool) {
                const searchResults = await webSearchTool.execute('duckduckgo', {
                    query: `${context} ${intent} command line`
                });

                if (searchResults.success && searchResults.output) {
                    const webSuggestions = this._extractCommandsFromWebResults(searchResults.output);
                    suggestions.push(...webSuggestions);
                }
            }

            // Cache the results
            this._aiCommandCache.set(cacheKey, {
                suggestions: suggestions.slice(0, 10), // Limit to top 10
                timestamp: Date.now()
            });

            this._onCommandSuggestion.fire({
                suggestions: suggestions.slice(0, 10),
                context,
                confidence: 0.8
            });

            return suggestions.slice(0, 10);
        } catch (error) {
            Logger.instance.error('Failed to suggest commands:', error);
            return [];
        }
    }

    public async analyzeError(error: string, context: string): Promise<AIErrorAnalysis> {
        try {
            const webSearchTool = this._toolRegistry.getTool('webSearch') as WebSearchTool;
            const securityTool = this._toolRegistry.getTool('security') as SecurityVulnerabilityTool;

            // Analyze error type and severity
            const errorType = this._classifyError(error);
            const severity = this._determineSeverity(error);

            // Get suggested fixes
            const suggestedFixes: string[] = [];

            // Use pattern matching for common errors
            if (error.includes('permission denied')) {
                suggestedFixes.push('sudo ' + context);
                suggestedFixes.push('chmod +x filename');
                suggestedFixes.push('Check file permissions');
            } else if (error.includes('command not found')) {
                const command = error.match(/command not found:\s*(.+)/)?.[1];
                if (command) {
                    suggestedFixes.push(`Install ${command}: sudo apt install ${command}`);
                    suggestedFixes.push(`Install ${command}: brew install ${command}`);
                    suggestedFixes.push(`Check if ${command} is in PATH`);
                }
            }

            // Check for security-related errors
            if (securityTool && (error.includes('security') || error.includes('vulnerability') || error.includes('permission'))) {
                try {
                    const securityAnalysis = await securityTool.execute('analyze', {
                        error,
                        context
                    });
                    if (securityAnalysis.suggestions) {
                        suggestedFixes.push(...securityAnalysis.suggestions);
                    }
                } catch (securityError) {
                    Logger.instance.warn('Security analysis failed:', securityError);
                }
            }

            // Search for solutions online
            const relatedDocumentation: string[] = [];
            if (webSearchTool) {
                const searchResults = await webSearchTool.execute('duckduckgo', {
                    query: `"${error}" solution fix`
                });

                if (searchResults.success && searchResults.output) {
                    relatedDocumentation.push(...this._extractDocumentationLinks(searchResults.output));
                }
            }

            return {
                errorType,
                severity,
                suggestedFixes,
                explanation: this._generateErrorExplanation(error, errorType),
                relatedDocumentation,
                confidence: 0.85
            };
        } catch (error) {
            Logger.instance.error('Failed to analyze error:', error);
            return {
                errorType: 'unknown',
                severity: 'medium',
                suggestedFixes: [],
                explanation: 'Unable to analyze error',
                relatedDocumentation: [],
                confidence: 0.1
            };
        }
    }

    public async predictNextCommand(history: string[], currentContext: string): Promise<string[]> {
        try {
            // Use quantum memory if available for pattern analysis
            if (this._quantumMemory) {
                const insights = await this._quantumMemory.generatePredictiveInsights(
                    `Command history: ${history.join('\n')}\nCurrent context: ${currentContext}`
                );

                if (insights.length > 0) {
                    return insights.slice(0, 5).map(insight => insight.suggestedActions?.[0] || '');
                }
            }

            // Fallback to simple pattern matching
            const predictions: string[] = [];
            const lastCommand = history[history.length - 1];

            if (lastCommand) {
                if (lastCommand.startsWith('git add')) {
                    predictions.push('git commit -m "message"', 'git status', 'git push');
                } else if (lastCommand.startsWith('npm install')) {
                    predictions.push('npm start', 'npm test', 'npm run build');
                } else if (lastCommand.startsWith('cd ')) {
                    predictions.push('ls', 'pwd', 'ls -la');
                }
            }

            return predictions;
        } catch (error) {
            Logger.instance.error('Failed to predict next command:', error);
            return [];
        }
    }

    public async optimizeCommand(command: string, context: string): Promise<string> {
        try {
            // Use AI to optimize the command
            let optimized = command;

            // Apply common optimizations
            if (command.includes('ls') && !command.includes('-')) {
                optimized = command.replace('ls', 'ls -la');
            } else if (command.includes('grep') && !command.includes('-r')) {
                optimized = command.replace('grep', 'grep -r');
            } else if (command.includes('find') && !command.includes('-type')) {
                optimized = command + ' -type f';
            }

            // Store optimization in memory
            if (this._quantumMemory) {
                await this._quantumMemory.storeTemporalMemory(
                    `Command optimization: ${command} -> ${optimized}`,
                    { filePath: 'terminal', projectContext: context },
                    'update'
                );
            }

            return optimized;
        } catch (error) {
            Logger.instance.error('Failed to optimize command:', error);
            return command;
        }
    }

    // ===== WORKFLOW INTEGRATION IMPLEMENTATION =====

    public async triggerWorkflow(workflowId: string, context: Record<string, unknown>): Promise<unknown> {
        try {
            // Use the workflow engine to execute workflows
            const workflowTool = this._toolRegistry.getTool('workflow');
            if (workflowTool) {
                return await workflowTool.execute({
                    workflowId,
                    context,
                    source: 'terminal'
                });
            }

            // Fallback: trigger common workflows directly
            switch (workflowId) {
                case 'codeAnalysis':
                    return await this._triggerCodeAnalysisWorkflow(context);
                case 'deployment':
                    return await this._triggerDeploymentWorkflow(context);
                case 'testing':
                    return await this._triggerTestingWorkflow(context);
                default:
                    throw new Error(`Unknown workflow: ${workflowId}`);
            }
        } catch (error) {
            Logger.instance.error(`Failed to trigger workflow ${workflowId}:`, error);
            throw error;
        }
    }

    public async getAvailableWorkflows(): Promise<string[]> {
        try {
            // Get workflows from the workflow registry
            const registeredWorkflows = workflowRegistry.getAllWorkflows();
            if (registeredWorkflows.length > 0) {
                return registeredWorkflows.map(workflow => workflow.id);
            }

            // Get workflows from the workflow engine tool
            const workflowTool = this._toolRegistry.getTool('workflow');
            if (workflowTool) {
                const result = await workflowTool.execute({ action: 'list' });
                if (result.success) {
                    return result.output || [];
                }
            }

            // Fallback: return built-in workflows
            return [
                'codeAnalysis',
                'deployment',
                'testing',
                'documentation',
                'refactoring',
                'security-scan',
                'dependency-analysis'
            ];
        } catch (error) {
            Logger.instance.error('Failed to get available workflows:', error);
            return [];
        }
    }

    public async createCustomWorkflow(definition: Record<string, unknown>): Promise<string> {
        try {
            const workflowTool = this._toolRegistry.getTool('workflow');
            if (workflowTool) {
                const result = await workflowTool.execute({
                    action: 'create',
                    definition
                });

                if (result.success) {
                    return result.output.id;
                }
            }

            throw new Error('Workflow tool not available');
        } catch (error) {
            Logger.instance.error('Failed to create custom workflow:', error);
            throw error;
        }
    }

    // ===== WEB INTELLIGENCE IMPLEMENTATION =====

    public async searchDocumentation(query: string, technology?: string): Promise<string[]> {
        try {
            const webSearchTool = this._toolRegistry.getTool('webSearch') as WebSearchTool;
            if (!webSearchTool) {
                return [];
            }

            const searchQuery = technology
                ? `${technology} ${query} documentation site:docs.${technology}.com OR site:${technology}.org`
                : `${query} documentation`;

            const result = await webSearchTool.execute('duckduckgo', {
                query: searchQuery
            });

            if (result.success && result.output) {
                return this._extractDocumentationLinks(result.output);
            }

            return [];
        } catch (error) {
            Logger.instance.error('Failed to search documentation:', error);
            return [];
        }
    }

    public async getStackOverflowSolutions(error: string): Promise<Array<Record<string, unknown>>> {
        try {
            const webSearchTool = this._toolRegistry.getTool('webSearch') as WebSearchTool;
            if (!webSearchTool) {
                return [];
            }

            const result = await webSearchTool.execute('duckduckgo', {
                query: `"${error}" site:stackoverflow.com`
            });

            if (result.success && result.output) {
                return this._parseStackOverflowResults(result.output);
            }

            return [];
        } catch (error) {
            Logger.instance.error('Failed to get StackOverflow solutions:', error);
            return [];
        }
    }

    public async fetchLatestBestPractices(technology: string): Promise<string[]> {
        try {
            const webSearchTool = this._toolRegistry.getTool('webSearch') as WebSearchTool;
            if (!webSearchTool) {
                return [];
            }

            const result = await webSearchTool.execute('duckduckgo', {
                query: `${technology} best practices 2024`
            });

            if (result.success && result.output) {
                return this._extractBestPractices(result.output);
            }

            return [];
        } catch (error) {
            Logger.instance.error('Failed to fetch best practices:', error);
            return [];
        }
    }

    // ===== CODE INTELLIGENCE IMPLEMENTATION =====

    public async analyzeCodeQuality(filePath: string): Promise<Record<string, unknown>> {
        try {
            const codeIntelTool = this._toolRegistry.getTool('codeIntel') as CodeIntelligenceTool;
            if (codeIntelTool) {
                return await codeIntelTool.execute('analyze', {
                    filePath
                });
            }

            // Fallback: basic analysis
            return {
                quality: 'unknown',
                issues: [],
                suggestions: []
            };
        } catch (error) {
            Logger.instance.error('Failed to analyze code quality:', error);
            throw error;
        }
    }

    public async generateCode(prompt: string, language: string): Promise<string> {
        try {
            const codeGenTool = this._toolRegistry.getTool('codeGen');
            if (codeGenTool) {
                const result = await codeGenTool.execute({
                    prompt,
                    language
                });

                if (result.success) {
                    return result.output;
                }
            }

            // Use LLM service to get provider and generate response
            const provider = await llmService.getDefaultProvider();
            if (provider) {
                const response = await provider.generate({
                    prompt: `Generate ${language} code for: ${prompt}`,
                    mode: 'generate',
                    temperature: 0.3,
                    maxTokens: 1000
                });
                return response.content || '';
            }

            return 'No LLM provider available for code generation';
        } catch (error) {
            Logger.instance.error('Failed to generate code:', error);
            throw error;
        }
    }

    public async refactorCode(filePath: string, instructions: string): Promise<string> {
        try {
            const refactorTool = this._toolRegistry.getTool('refactor');
            if (refactorTool) {
                const result = await refactorTool.execute({
                    filePath,
                    instructions
                });

                if (result.success) {
                    return result.output;
                }
            }

            throw new Error('Refactoring tool not available');
        } catch (error) {
            Logger.instance.error('Failed to refactor code:', error);
            throw error;
        }
    }

    public async detectVulnerabilities(filePath: string): Promise<Array<Record<string, unknown>>> {
        try {
            const securityTool = this._toolRegistry.getTool('security') as SecurityVulnerabilityTool;
            if (securityTool) {
                const result = await securityTool.execute('scan', {
                    filePath
                });

                if (result.success) {
                    return result.output || [];
                }
            }

            return [];
        } catch (error) {
            Logger.instance.error('Failed to detect vulnerabilities:', error);
            return [];
        }
    }

    // ===== MULTI-AGENT COLLABORATION IMPLEMENTATION =====

    public async delegateTask(agentType: string, task: string, context: Record<string, unknown>): Promise<unknown> {
        try {
            if (this._supervisorAgent) {
                // Use the supervisor agent's run method for task delegation
                const result = await this._supervisorAgent.run({
                    prompt: `Delegate this task to ${agentType}: ${task}`,
                    mode: 'task',
                    context: {
                        ...context,
                        agentType: agentType,
                        task: task
                    } as AgentContext
                });
                return result.output;
            }

            // Fallback: use tool registry
            const agentTool = this._toolRegistry.getTool(agentType);
            if (agentTool) {
                return await agentTool.execute({
                    task,
                    context
                });
            }

            throw new Error(`Agent type ${agentType} not available`);
        } catch (error) {
            Logger.instance.error(`Failed to delegate task to ${agentType}:`, error);
            throw error;
        }
    }

    public async getAgentCapabilities(): Promise<Record<string, string[]>> {
        try {
            const capabilities: Record<string, string[]> = {};

            // Get capabilities from tool registry
            const tools = this._toolRegistry.getAllTools();
            for (const [id, tool] of tools) {
                if (tool.category) {
                    if (!capabilities[tool.category]) {
                        capabilities[tool.category] = [];
                    }
                    capabilities[tool.category].push(tool.name || id || 'Unknown Tool');
                }
            }

            return capabilities;
        } catch (error) {
            Logger.instance.error('Failed to get agent capabilities:', error);
            return {};
        }
    }

    public async coordinateAgents(tasks: Array<Record<string, unknown>>): Promise<Array<unknown>> {
        try {
            if (this._supervisorAgent) {
                // Convert tasks to the format expected by executeParallelAgentTasks
                const parallelTasks = tasks.map(task => ({
                    agent: { id: task.agentType, name: task.agentType } as Agent,
                    input: String(task.task),
                    context: (task.context || {}) as Record<string, unknown>
                }));
                const results = await this._supervisorAgent.executeParallelAgentTasks(parallelTasks, {} as Record<string, unknown>);
                return results.map(result => ({ success: true, result }));
            }

            // Fallback: execute tasks sequentially
            const results: Array<{ success: boolean; result?: unknown; error?: string }> = [];
            for (const task of tasks) {
                try {
                    const result = await this.delegateTask(String(task.agentType), String(task.task), (task.context || {}) as Record<string, unknown>);
                    results.push({ success: true, result });
                } catch (error) {
                    results.push({ success: false, error: error instanceof Error ? error.message : String(error) });
                }
            }

            return results;
        } catch (error) {
            Logger.instance.error('Failed to coordinate agents:', error);
            throw error;
        }
    }

    // ===== HELPER METHODS FOR AI CAPABILITIES =====

    private async _getGitSuggestions(intent: string): Promise<string[]> {
        const suggestions: string[] = [];

        if (intent.includes('commit')) {
            suggestions.push('git add .', 'git commit -m "message"', 'git commit -am "message"');
        } else if (intent.includes('push')) {
            suggestions.push('git push', 'git push origin main', 'git push -u origin branch');
        } else if (intent.includes('pull')) {
            suggestions.push('git pull', 'git pull origin main', 'git fetch');
        } else if (intent.includes('branch')) {
            suggestions.push('git branch', 'git checkout -b new-branch', 'git merge branch');
        } else {
            suggestions.push('git status', 'git log --oneline', 'git diff');
        }

        return suggestions;
    }

    private async _getNodeSuggestions(intent: string): Promise<string[]> {
        const suggestions: string[] = [];

        if (intent.includes('install')) {
            suggestions.push('npm install', 'npm install --save', 'npm install --save-dev');
        } else if (intent.includes('run')) {
            suggestions.push('npm start', 'npm test', 'npm run build', 'npm run dev');
        } else if (intent.includes('update')) {
            suggestions.push('npm update', 'npm audit fix', 'npm outdated');
        } else {
            suggestions.push('npm init', 'npm list', 'npm version');
        }

        return suggestions;
    }

    private async _getPythonSuggestions(intent: string): Promise<string[]> {
        const suggestions: string[] = [];

        if (intent.includes('install')) {
            suggestions.push('pip install', 'pip install -r requirements.txt', 'pip install --upgrade');
        } else if (intent.includes('run')) {
            suggestions.push('python main.py', 'python -m module', 'python -c "code"');
        } else if (intent.includes('env')) {
            suggestions.push('python -m venv venv', 'source venv/bin/activate', 'deactivate');
        } else {
            suggestions.push('python --version', 'pip list', 'pip freeze');
        }

        return suggestions;
    }

    private _extractCommandsFromWebResults(results: Array<Record<string, unknown>>): string[] {
        const commands: string[] = [];

        try {
            if (Array.isArray(results)) {
                for (const result of results) {
                    const content = String(result.content || result.snippet || '');
                    const commandMatches = content.match(/`([^`]+)`/g);
                    if (commandMatches) {
                        commands.push(...commandMatches.map((m: string) => m.replace(/`/g, '')));
                    }
                }
            }
        } catch (error) {
            Logger.instance.error('Failed to extract commands from web results:', error);
        }

        return commands.slice(0, 5); // Limit to 5 commands
    }

    private _classifyError(error: string): string {
        if (error.includes('permission denied') || error.includes('EACCES')) {
            return 'permission';
        } else if (error.includes('command not found')) {
            return 'command-not-found';
        } else if (error.includes('ECONNREFUSED') || error.includes('network')) {
            return 'network';
        } else if (error.includes('syntax error') || error.includes('SyntaxError')) {
            return 'syntax';
        } else if (error.includes('module not found') || error.includes('import')) {
            return 'import';
        } else {
            return 'unknown';
        }
    }

    private _determineSeverity(error: string): 'low' | 'medium' | 'high' | 'critical' {
        if (error.includes('critical') || error.includes('fatal')) {
            return 'critical';
        } else if (error.includes('error') || error.includes('failed')) {
            return 'high';
        } else if (error.includes('warning') || error.includes('deprecated')) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    private _generateErrorExplanation(error: string, errorType: string): string {
        const errorContext = error ? ` Error details: ${error}` : '';
        switch (errorType) {
            case 'permission':
                return `This error occurs when you don't have sufficient permissions to perform the operation.${errorContext}`;
            case 'command-not-found':
                return 'The command you\'re trying to run is not installed or not in your PATH.';
            case 'network':
                return 'This is a network connectivity issue. Check your internet connection.';
            case 'syntax':
                return 'There\'s a syntax error in your command or code.';
            case 'import':
                return 'A required module or package is missing or not installed.';
            default:
                return 'An unknown error occurred. Check the error message for more details.';
        }
    }

    private _extractDocumentationLinks(results: Array<Record<string, unknown>>): string[] {
        const links: string[] = [];

        try {
            if (Array.isArray(results)) {
                for (const result of results) {
                    const url = String(result.url || '');
                    if (url && (url.includes('docs.') || url.includes('documentation'))) {
                        links.push(url);
                    }
                }
            }
        } catch (error) {
            Logger.instance.error('Failed to extract documentation links:', error);
        }

        return links;
    }

    private _parseStackOverflowResults(results: Array<Record<string, unknown>>): Array<Record<string, unknown>> {
        const solutions: Array<Record<string, unknown>> = [];

        try {
            if (Array.isArray(results)) {
                for (const result of results) {
                    solutions.push({
                        title: String(result.title || ''),
                        url: String(result.url || ''),
                        snippet: String(result.snippet || result.content || ''),
                        votes: this._extractVotes(String(result.snippet || ''))
                    });
                }
            }
        } catch (error) {
            Logger.instance.error('Failed to parse StackOverflow results:', error);
        }

        return solutions;
    }

    private _extractBestPractices(results: Array<Record<string, unknown>>): string[] {
        const practices: string[] = [];

        try {
            if (Array.isArray(results)) {
                for (const result of results) {
                    const content = String(result.content || result.snippet || '');
                    // Extract numbered lists or bullet points
                    const practiceMatches = content.match(/(?:\d+\.|•|\*)\s*([^\n]+)/g);
                    if (practiceMatches) {
                        practices.push(...practiceMatches.map((m: string) => m.replace(/^\d+\.|•|\*\s*/, '')));
                    }
                }
            }
        } catch (error) {
            Logger.instance.error('Failed to extract best practices:', error);
        }

        return practices.slice(0, 10); // Limit to 10 practices
    }

    private _extractVotes(content: string): number {
        const voteMatch = content.match(/(\d+)\s*votes?/i);
        return voteMatch ? parseInt(voteMatch[1], 10) : 0;
    }

    // ===== WORKFLOW HELPER METHODS =====

    private async _triggerCodeAnalysisWorkflow(context: Record<string, unknown>): Promise<unknown> {
        try {
            const codeIntelTool = this._toolRegistry.getTool('codeIntel') as CodeIntelligenceTool;
            const diagnosticsTool = this._toolRegistry.getTool('diagnostics') as DiagnosticsTool;

            const results: Record<string, unknown> = {
                analysis: null,
                diagnostics: null,
                suggestions: []
            };

            if (codeIntelTool && context.filePath) {
                results.analysis = await codeIntelTool.execute('symbolSearch', {
                    action: 'symbolSearch',
                    query: context.filePath
                });
            }

            if (diagnosticsTool && context.filePath) {
                results.diagnostics = await diagnosticsTool.execute('get', {
                    paths: [context.filePath]
                });
            }

            return results;
        } catch (error) {
            Logger.instance.error('Failed to trigger code analysis workflow:', error);
            throw error;
        }
    }

    private async _triggerDeploymentWorkflow(context: Record<string, unknown>): Promise<unknown> {
        try {
            const deployTool = this._toolRegistry.getTool('deployWebApp');

            if (deployTool) {
                return await deployTool.execute({
                    projectPath: context.projectPath || process.cwd(),
                    environment: context.environment || 'staging'
                });
            }

            throw new Error('Deployment tool not available');
        } catch (error) {
            Logger.instance.error('Failed to trigger deployment workflow:', error);
            throw error;
        }
    }

    private async _triggerTestingWorkflow(context: Record<string, unknown>): Promise<unknown> {
        try {
            // Use terminal command tool to run tests
            const terminalTool = this._toolRegistry.getTool('terminal_command');

            if (terminalTool) {
                const testCommands = [
                    'npm test',
                    'yarn test',
                    'python -m pytest',
                    'mvn test',
                    'gradle test'
                ];

                for (const command of testCommands) {
                    try {
                        const result = await terminalTool.execute({
                            command,
                            workingDirectory: context.projectPath || process.cwd()
                        });

                        if (result.success) {
                            return result;
                        }
                    } catch (error) {
                        // Try next command
                        continue;
                    }
                }
            }

            throw new Error('No suitable test command found');
        } catch (error) {
            Logger.instance.error('Failed to trigger testing workflow:', error);
            throw error;
        }
    }

    // ===== ENHANCED TERMINAL CAPABILITIES =====

    /**
     * Enhanced command execution with AI analysis
     */
    public async executeCommandWithAI(command: string, options?: {
        analyzeOutput?: boolean;
        suggestImprovements?: boolean;
        learnFromExecution?: boolean;
    }): Promise<{
        result: unknown;
        analysis?: AIErrorAnalysis;
        suggestions?: string[];
        improvements?: string[];
    }> {
        const startTime = Date.now();

        try {
            // Execute the command
            const result = await this.executeCommand(command);

            // Store command in memory
            const commandContext: IMemoryCommandContext = {
                command,
                workingDirectory: process.cwd(),
                timestamp: new Date(),
                success: Boolean((result as Record<string, unknown>)?.success) || true,
                output: String((result as Record<string, unknown>)?.output || result),
                projectContext: vscode.workspace.name
            };

            if (options?.learnFromExecution !== false) {
                this._storeCommandInMemory(commandContext);
            }

            const response: Record<string, unknown> = { result };

            // Analyze output if requested
            if (options?.analyzeOutput && (result as Record<string, unknown>)?.output) {
                response.analysis = await this.analyzeError(String((result as Record<string, unknown>)?.output), command);
            }

            // Get suggestions if requested
            if (options?.suggestImprovements) {
                response.suggestions = await this.predictNextCommand([command], process.cwd());
                response.improvements = [await this.optimizeCommand(command, process.cwd())];
            }

            // Add execution timing
            response.executionTime = Date.now() - startTime;

            return response as { result: unknown; analysis?: AIErrorAnalysis; suggestions?: string[]; improvements?: string[] };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);

            // Analyze the error
            const analysis = await this.analyzeError(errorMessage, command);

            return {
                result: { success: false, error: errorMessage },
                analysis,
                suggestions: analysis.suggestedFixes
            };
        }
    }

    /**
     * Get AI-powered command suggestions based on current context
     */
    public async getSmartSuggestions(partialCommand?: string): Promise<{
        suggestions: string[];
        context: string;
        confidence: number;
    }> {
        const context = this._getCurrentContext();
        const intent = partialCommand || 'general';

        const suggestions = await this.suggestCommand(context, intent);

        return {
            suggestions,
            context,
            confidence: 0.8
        };
    }

    /**
     * Execute a workflow directly from terminal
     */
    public async runWorkflow(workflowId: string, parameters?: Record<string, unknown>): Promise<unknown> {
        try {
            const context = {
                ...parameters,
                workingDirectory: process.cwd(),
                projectName: vscode.workspace.name,
                timestamp: new Date().toISOString()
            };

            return await this.triggerWorkflow(workflowId, context);
        } catch (error) {
            Logger.instance.error(`Failed to run workflow ${workflowId}:`, error);
            throw error;
        }
    }

    private _storeCommandInMemory(context: IMemoryCommandContext): void {
        const key = context.workingDirectory;
        const existing = this._commandMemory.get(key) || [];
        existing.push(context);

        // Keep only last 100 commands per directory
        if (existing.length > 100) {
            existing.shift();
        }

        this._commandMemory.set(key, existing);

        // Store in quantum memory if available
        if (this._quantumMemory) {
            this._quantumMemory.storeTemporalMemory(
                `Command executed: ${context.command}`,
                {
                    filePath: context.workingDirectory,
                    projectContext: context.projectContext || 'unknown'
                },
                'create',
                {
                    tags: [
                        context.success ? 'success' : 'failure',
                        'terminal-command',
                        context.output.substring(0, 100) // Limit output size for tags
                    ]
                }
            ).catch(error => {
                Logger.instance.error('Failed to store command in quantum memory:', error);
            });
        }
    }

    private _getCurrentContext(): string {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        const activeEditor = vscode.window.activeTextEditor;

        let context = 'general';

        if (workspaceFolder) {
            context += ` workspace:${workspaceFolder.name}`;
        }

        if (activeEditor) {
            const fileName = activeEditor.document.fileName;
            const extension = fileName.split('.').pop();
            context += ` file:${extension}`;
        }

        return context;
    }

    /**
     * Execute command using terminal command tool
     */
    private async executeCommand(command: string): Promise<unknown> {
        try {
            const terminalTool = this._toolRegistry.getTool('terminal_command');
            if (terminalTool) {
                return await terminalTool.execute({
                    command,
                    workingDirectory: process.cwd()
                });
            }

            throw new Error('Terminal command tool not available');
        } catch (error) {
            Logger.instance.error('Failed to execute command:', error);
            throw error;
        }
    }

    private initializeTerminalPatterns(): void {
        // Register standard terminal patterns
        this._registerStandardPatterns();
        // Register enhanced patterns
        this._registerEnhancedPatterns();
    }

    private _registerStandardPatterns(): void {
        // Command not found
        this.registerPattern({
            pattern: /command not found:\s+(.+)$/m,
            action: 'install-missing-command',
            priority: 100,
            category: 'terminal-error',
            fix: (match, doc, range) => {
                const cmd = match[1];
                const action = new vscode.CodeAction(
                    `${this._getIconForCategory('error', 'terminal')} Install missing command: ${cmd}`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.terminal.installCommand',
                    title: `Install ${cmd}`,
                    arguments: [cmd]
                };
                action.diagnostics = [{
                    message: `Command '${cmd}' not found in ${doc.uri.fsPath}`,
                    range,
                    severity: vscode.DiagnosticSeverity.Error,
                    source: 'terminal'
                }];
                return action;
            }
        });

        // Permission denied
        this.registerPattern({
            pattern: /permission denied|EACCES/i,
            action: 'fix-permissions',
            priority: 90,
            category: 'security',
            fix: (match, doc, range) => {
                const action = new vscode.CodeAction(
                    `${this._getIconForCategory('security', 'terminal')} Fix permission issues`,
                    vscode.CodeActionKind.QuickFix.append('security')
                );
                action.command = {
                    command: 'codessa.terminal.fixPermissions',
                    title: 'Fix permissions',
                    arguments: [match[0], doc.uri.toString()]
                };
                action.diagnostics = [{
                    message: `Permission error: ${match[0]}`,
                    range,
                    severity: vscode.DiagnosticSeverity.Error,
                    source: 'terminal'
                }];
                return action;
            }
        });

        // Network issues
        this.registerPattern({
            pattern: /ECONNREFUSED|ETIMEDOUT|network error/i,
            action: 'fix-network',
            priority: 85,
            category: 'terminal-warning',
            fix: (match, doc, range) => {
                const action = new vscode.CodeAction(
                    `${this._getIconForCategory('warning', 'terminal')} Fix network connection`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.terminal.fixNetwork',
                    title: 'Fix network',
                    arguments: [match[0], doc.uri.toString()]
                };
                action.diagnostics = [{
                    message: `Network error: ${match[0]}`,
                    range,
                    severity: vscode.DiagnosticSeverity.Warning,
                    source: 'terminal'
                }];
                return action;
            }
        });
    }

    private _registerEnhancedPatterns(): void {
        // Build errors
        this.registerPattern({
            pattern: /error TS\d+:|error C\d+:|compilation failed/i,
            action: 'fix-build-error',
            priority: 95,
            category: 'debug',
            fix: (match, doc, range) => {
                const action = new vscode.CodeAction(
                    `${this._getIconForCategory('error', 'terminal')} Fix build error`,
                    vscode.CodeActionKind.QuickFix.append('debug')
                );
                action.command = {
                    command: 'codessa.terminal.fixBuildError',
                    title: 'Fix build error',
                    arguments: [match[0], doc.uri.toString()]
                };
                action.diagnostics = [{
                    message: `Build error: ${match[0]}`,
                    range,
                    severity: vscode.DiagnosticSeverity.Error,
                    source: 'terminal',
                    relatedInformation: [{
                        location: new vscode.Location(doc.uri, range),
                        message: 'Build process failed'
                    }]
                }];
                return action;
            }
        });

        // Missing dependencies
        this.registerPattern({
            pattern: /cannot find module|module not found|package .* is not installed/i,
            action: 'install-dependency',
            priority: 95,
            category: 'dependency',
            fix: (match, doc, range) => {
                const moduleName = match[0].match(/['"]([^'"]+)['"]/)?.[1] || 'package';
                const action = new vscode.CodeAction(
                    `${this._getIconForCategory('package', 'terminal')} Install missing dependency: ${moduleName}`,
                    vscode.CodeActionKind.QuickFix.append('dependency')
                );
                action.command = {
                    command: 'codessa.terminal.installDependency',
                    title: 'Install dependency',
                    arguments: [match[0], doc.uri.toString()]
                };
                action.diagnostics = [{
                    message: `Missing dependency: ${moduleName}`,
                    range,
                    severity: vscode.DiagnosticSeverity.Warning,
                    source: 'terminal',
                    tags: [vscode.DiagnosticTag.Unnecessary],
                    relatedInformation: [{
                        location: new vscode.Location(doc.uri, range),
                        message: `Run 'npm install ${moduleName}' to fix this issue`
                    }]
                }];
                return action;
            }
        });
    }

    public analyzeOutput(output: string): vscode.CodeAction[] {
        this._appendOutput(output);
        
        class TerminalDocument implements Partial<vscode.TextDocument> {
            uri = vscode.Uri.parse('terminal://output');
            languageId = 'terminal';
            version = 1;
            lineCount = output.split('\n').length;
            
            getText(range?: vscode.Range): string {
                if (!range) return output;
                const lines = output.split('\n');
                if (range.start.line === range.end.line) {
                    return lines[range.start.line].substring(range.start.character, range.end.character);
                }
                const selectedLines = lines.slice(range.start.line, range.end.line + 1);
                selectedLines[0] = selectedLines[0].substring(range.start.character);
                const lastIndex = selectedLines.length - 1;
                selectedLines[lastIndex] = selectedLines[lastIndex].substring(0, range.end.character);
                return selectedLines.join('\n');
            }
            
            lineAt(lineOrPosition: number | vscode.Position): vscode.TextLine {
                const line = typeof lineOrPosition === 'number' ? lineOrPosition : lineOrPosition.line;
                const lines = output.split('\n');
                const text = lines[line] || '';
                return {
                    lineNumber: line,
                    text,
                    range: new vscode.Range(line, 0, line, text.length),
                    rangeIncludingLineBreak: new vscode.Range(line, 0, line, text.length + 1),
                    firstNonWhitespaceCharacterIndex: text.search(/\S/),
                    isEmptyOrWhitespace: text.trim().length === 0
                };
            }
        }
        
        const range = new vscode.Range(0, 0, output.split('\n').length - 1, 0);
        const context: CodeActionContext = {
            document: new TerminalDocument() as vscode.TextDocument,
            range,
            diagnostics: [],
            trigger: 'auto',
            source: 'terminal'
        };

        // Get standard quick fixes
        const quickFixes = this._getQuickFixesSync();

        // Get pattern-based actions from base class
        const patternActions = this._getPatternBasedActionsSync(context);
        
        // Get context-aware actions
        const contextActions = this._getContextAwareActions(context);
        
        // Combine all actions with proper icons and metadata
        const allActions = [...quickFixes, ...patternActions, ...contextActions]
            .map(action => {
                const category = action.kind?.value?.includes('terminal') ? 'terminal' : 'command';
                action.title = `${this._getIconForCategory(category)} ${action.title}`;
                return action;
            });

        // Add diagnostics for relevant actions
        this._updateDiagnostics(allActions);

        return allActions;
    }

    // ===== SYNCHRONOUS HELPER METHODS FOR ANALYZE OUTPUT =====

    private _getQuickFixesSync(): vscode.CodeAction[] {
        // Return basic quick fixes synchronously
        const actions: vscode.CodeAction[] = [];

        // Add common terminal quick fixes
        actions.push(
            new vscode.CodeAction('Clear Terminal', vscode.CodeActionKind.QuickFix),
            new vscode.CodeAction('Restart Terminal', vscode.CodeActionKind.QuickFix),
            new vscode.CodeAction('Copy Output', vscode.CodeActionKind.QuickFix)
        );

        return actions;
    }

    private _getPatternBasedActionsSync(context: CodeActionContext): vscode.CodeAction[] {
        // Use the base class pattern matching synchronously
        const output = context.document.getText();
        return super.analyzeOutput(output);
    }

    private _appendOutput(output: string): void {
        // Process output for visual indicators
        const processedOutput = this._processOutputWithIndicators(output);
        this._outputBuffer += processedOutput;
        
        if (this._outputFlushTimer) {
            clearTimeout(this._outputFlushTimer);
        }
        
        this._outputFlushTimer = setTimeout(() => this._flushOutputBuffer(), 100);
    }

    private _processOutputWithIndicators(output: string): string {
        // Add visual indicators based on content
        const lines = output.split('\n');
        return lines.map(line => {
            if (line.match(/error|failed|exception/i)) {
                return `${this._getIconForCategory('error', 'terminal')} ${line}`;
            }
            if (line.match(/warning|warn/i)) {
                return `${this._getIconForCategory('warning', 'terminal')} ${line}`;
            }
            if (line.match(/success|completed|done/i)) {
                return `${this._getIconForCategory('success', 'terminal')} ${line}`;
            }
            if (line.match(/info|note/i)) {
                return `${this._getIconForCategory('info', 'terminal')} ${line}`;
            }
            return line;
        }).join('\n');
    }

    private _flushOutputBuffer(): void {
        this._outputBuffer = '';
        this._outputFlushTimer = null;
    }

    private async _getQuickFixes(): Promise<vscode.CodeAction[]> {
        const actions: vscode.CodeAction[] = [];
        const uniqueActions = new Set<string>();
        const processPatterns: Promise<void>[] = [];

        for (const pattern of this._quickFixPatterns) {
            const matches = this._outputBuffer.match(pattern.pattern);
            if (matches && !this._quickFixHistory.has(pattern.action)) {
                try {
                    if (this.isAIPattern(pattern)) {
                        // Process AI patterns asynchronously
                        processPatterns.push(
                            this.processAIPattern(pattern, matches).then(aiActions => {
                                for (const action of aiActions) {
                                    const actionKey = `${action.title}-${action.kind?.value}`;
                                    if (!uniqueActions.has(actionKey)) {
                                        actions.push(action);
                                        uniqueActions.add(actionKey);
                                        this._quickFixHistory.add(pattern.action);
                                        this._addDiagnostic(pattern, matches);
                                    }
                                }
                            })
                        );
                    } else {
                        // Process standard patterns immediately
                        const action = this._createQuickFix(pattern, matches);
                        const actionKey = `${action.title}-${action.kind?.value}`;
                        
                        if (!uniqueActions.has(actionKey)) {
                            actions.push(action);
                            uniqueActions.add(actionKey);
                            this._quickFixHistory.add(pattern.action);
                            this._addDiagnostic(pattern, matches);
                        }
                    }
                } catch (error) {
                    console.error(`Error creating quick fix for pattern ${pattern.action}:`, error);
                }
            }
        }

        // Wait for all AI pattern processing to complete
        if (processPatterns.length > 0) {
            await Promise.all(processPatterns);
        }

        return actions;
    }

    private _addDiagnostic(pattern: QuickFixPattern, matches: RegExpMatchArray): void {
        const diagnostic = new vscode.Diagnostic(
            new vscode.Range(0, 0, 0, 0),
            matches[0],
            this._getDiagnosticSeverity(pattern.category)
        );
        diagnostic.source = 'terminal';
        diagnostic.code = pattern.action;

        this._terminalDiagnostics.set(
            vscode.Uri.parse('terminal://output'),
            [diagnostic]
        );
    }

    private _getDiagnosticSeverity(category: string): vscode.DiagnosticSeverity {
        switch (category.toLowerCase()) {
            case 'error':
            case 'security':
                return vscode.DiagnosticSeverity.Error;
            case 'warning':
            case 'dependency':
                return vscode.DiagnosticSeverity.Warning;
            case 'info':
            case 'terminal':
                return vscode.DiagnosticSeverity.Information;
            default:
                return vscode.DiagnosticSeverity.Hint;
        }
    }

    async provideCodeActions(
        document: vscode.TextDocument,
        range: vscode.Range,
        context: vscode.CodeActionContext,
        token: vscode.CancellationToken
    ): Promise<vscode.CodeAction[]> {
        // Check for cancellation
        if (token.isCancellationRequested) {
            return [];
        }

        // First check if this is a terminal document and has diagnostic context
        if (document.uri.scheme === 'terminal' && context.diagnostics.length > 0) {
            // Focus our analysis on the specific range where the code action was requested
            // Use the relevant text from the specific range for more targeted analysis
            const relevantText = document.getText(range);
            this._appendOutput(relevantText);
            const actions = await this._getQuickFixes();

            // Filter and sort actions based on diagnostics
            return actions.filter(action => {
                // Only include actions that are relevant to the current diagnostics
                return context.diagnostics.some(diag => {
                    return diag.range.intersection(range) && 
                           action.diagnostics?.some(actionDiag => 
                               actionDiag.message === diag.message);
                });
            });
        }

        // For non-terminal documents, let the parent handle it
        return [];
    }

    private _createQuickFix(pattern: QuickFixPattern, matches: RegExpMatchArray): vscode.CodeAction {
        const dummyDocument = { uri: vscode.Uri.parse('terminal://output') } as vscode.TextDocument;
        const dummyRange = new vscode.Range(0, 0, 0, 0);
        
        const action = pattern.fix(matches, dummyDocument, dummyRange);
        action.title = `${this._getIconForCategory(pattern.category)} ${action.title}`;
        action.kind = this._getCodeActionKindForCategory(pattern.category);
        
        return action;
    }

    private _updateDiagnostics(actions: vscode.CodeAction[]): void {
        const diagnostics: vscode.Diagnostic[] = [];
        // Remove unused lines variable

        actions.forEach(action => {
            if (action.diagnostics?.length) {
                action.diagnostics.forEach(diag => {
                    // Convert line-based range to position in full output
                    const startPos = diag.range.start;
                    const endPos = diag.range.end;
                    
                    const diagnostic = new vscode.Diagnostic(
                        new vscode.Range(startPos, endPos),
                        diag.message,
                        diag.severity
                    );
                    diagnostic.source = 'terminal';
                    diagnostic.code = action.kind?.value;
                    diagnostics.push(diagnostic);
                });
            }
        });

        this._terminalDiagnostics.set(
            vscode.Uri.parse('terminal://output'),
            diagnostics
        );
    }

    private _getCodeActionKindForCategory(category: string): vscode.CodeActionKind {
        switch (category.toLowerCase()) {
            case 'terminal':
                return vscode.CodeActionKind.QuickFix;
            case 'security':
                return vscode.CodeActionKind.QuickFix.append('security');
            case 'debug':
                return vscode.CodeActionKind.QuickFix.append('debug');
            case 'dependency':
                return vscode.CodeActionKind.QuickFix.append('dependency');
            case 'refactor':
                return vscode.CodeActionKind.RefactorExtract;
            default:
                return vscode.CodeActionKind.QuickFix;
        }
    }

    public registerPattern(pattern: QuickFixPattern | IAIToolPattern): void {
        if (this.isAIPattern(pattern)) {
            this._aiPatterns.set(pattern.action, pattern);
        }
        this._quickFixPatterns.push(pattern);
    }

    private isAIPattern(pattern: QuickFixPattern | IAIToolPattern): pattern is IAIToolPattern {
        return 'aiContext' in pattern || 'contextProvider' in pattern;
    }

    public markPatternUsed(patternId: string): void {
        this._quickFixHistory.add(patternId);
    }

    public clearHistory(): void {
        this._quickFixHistory.clear();
        this._terminalDiagnostics.clear();
    }

    public async processAIPattern(
        pattern: IAIToolPattern, 
        matches: RegExpMatchArray
    ): Promise<vscode.CodeAction[]> {
        this._onPatternMatch.fire({ pattern, matches });

        let context: unknown;
        if (pattern.contextProvider) {
            context = await pattern.contextProvider(matches);
        }

        if (!pattern.aiContext) {
            return [];
        }

        // Here you would integrate with your AI service
        // This is just a placeholder for the structure
        const aiResponse = await this.processWithAI(pattern.aiContext, context);

        if (pattern.resultProcessor) {
            const actions = pattern.resultProcessor(aiResponse);
            if (Array.isArray(actions)) {
                actions.forEach(action => {
                    this._onAIAction.fire({ action, context, aiResponse });
                });
                return actions;
            } else {
                this._onAIAction.fire({ action: actions, context, aiResponse });
                return [actions];
            }
        }

        return [];
    }

    private async processWithAI(aiContext: IAIContext, context: unknown): Promise<unknown> {
        // Implement your AI processing logic here
        // This is where you would integrate with your AI service
        const prompt = this.buildPrompt(aiContext.promptTemplate, context);
        return Promise.resolve({
            suggestion: `${prompt} - AI response`,
            confidence: 0.95,
            modelId: aiContext.modelId,
            context
        });
    }

    private buildPrompt(template: string, context: unknown): string {
        // Convert context to string representation
        const contextStr = JSON.stringify(context, null, 2);
        return template.replace('{{context}}', contextStr);
    }

    /**
     * Enhanced terminal integration methods
     */
    public analyzeTerminalOutput(output: string): vscode.CodeAction[] {
        // Use the terminal code action provider to analyze output
        this._appendOutput(output);
        // Return synchronous quick fixes
        const actions: vscode.CodeAction[] = [];
        this._quickFixPatterns.forEach(pattern => {
            const matches = output.match(pattern.pattern);
            if (matches) {
                try {
                    const action = this._createQuickFix(pattern, matches);
                    actions.push(action);
                } catch (error) {
                    Logger.instance.error(`Error creating quick fix for pattern ${pattern.action}:`, error);
                }
            }
        });
        return actions;
    }

    public async applyQuickFix(action: vscode.CodeAction): Promise<boolean> {
        try {
            if (action.edit) {
                const success = await vscode.workspace.applyEdit(action.edit);
                if (success) {
                    this.markPatternUsed(action.kind?.value || 'unknown');
                }
                return success;
            } else if (action.command) {
                await vscode.commands.executeCommand(action.command.command, ...action.command.arguments || []);
                this.markPatternUsed(action.kind?.value || 'unknown');
                return true;
            }
            return false;
        } catch (error) {
            Logger.instance.error('Failed to apply quick fix:', error);
            return false;
        }
    }

    public getCodeActionManager(): typeof codeActionManager {
        return codeActionManager;
    }

    public dispose(): void {
        this._terminalDiagnostics.dispose();
        if (this._outputFlushTimer) {
            clearTimeout(this._outputFlushTimer);
        }
    }
}

export interface QuickFixIcon {
    name: string;
    icon: vscode.ThemeIcon;
    category: string;
    description: string;
}

export interface TerminalActionResult {
    action: vscode.CodeAction;
    applied: boolean;
    timestamp: Date;
    output: string;
}

/**
 * Terminal configuration options
 */
export interface TerminalConfig {
    name?: string;
    shell?: string;
    args?: string[];
    cwd?: string;
    env?: { [key: string]: string | undefined };
    dimensions?: { columns: number; rows: number };
    enableColorSupport?: boolean;
    enableUnicodeSupport?: boolean;
    enableBracketedPaste?: boolean;
    scrollback?: number;
    fontSize?: number;
    fontFamily?: string;
    theme?: 'light' | 'dark' | 'system';
    hideFromUser?: boolean;
    isTransient?: boolean;
    enableCommandHistory?: boolean;
    maxCommandHistory?: number;
    enableAutoComplete?: boolean;
    customPrompt?: string;
    workingDirectoryTracking?: boolean;
}

/**
 * Result of command execution with detailed information
 */
export interface CommandResult {
    command: string;
    output: string;
    exitCode?: number;
    duration: number;
    success: boolean;
    error?: string;
    background?: boolean;
    completedAt: Date;
}

/**
 * Terminal state information
 */
export interface TerminalState {
    isConnected: boolean;
    isReady: boolean;
    currentDirectory: string;
    shellType: string;
    dimensions: { columns: number; rows: number };
    lastCommand: string;
    commandHistory: string[];
    isBusy: boolean;
    exitCode?: number;
}

/**
 * Enhanced terminal statistics for monitoring
 */
export interface TerminalStats {
    totalCommands: number;
    totalOutputBytes: number;
    averageResponseTime: number;
    uptimeSeconds: number;
    errorCount: number;
    resizeCount: number;
}

/**
 * Advanced terminal event types
 */
export interface TerminalEvents {
    onData: vscode.Event<string>;
    onExit: vscode.Event<number>;
    onReady: vscode.Event<void>;
    onResize: vscode.Event<{ columns: number; rows: number }>;
    onCommand: vscode.Event<string>;
    onError: vscode.Event<Error>;
    onStateChange: vscode.Event<TerminalState>;
}

/**
 * Interface for a terminal process with advanced capabilities
 */
export interface ITerminalProcess extends vscode.Disposable {
    readonly id: string;
    readonly config: TerminalConfig;
    readonly state: TerminalState;
    readonly stats: TerminalStats;
    readonly events: TerminalEvents;

    initialize(): Promise<void>;
    write(data: string): Promise<void>;
    writeLn(data: string): Promise<void>;
    executeCommand(command: string, options?: {
        timeout?: number;
        expectPrompt?: boolean;
        background?: boolean;
        workingDirectory?: string;
    }): Promise<string>;
    executeCommandAdvanced(command: string, options?: {
        timeout?: number;
        expectPrompt?: boolean;
        background?: boolean;
        workingDirectory?: string;
    }): Promise<CommandResult>;
    resize(cols: number, rows: number): Promise<void>;
    clear(): Promise<void>;
    reset(): Promise<void>;
    sendSignal(signal: string): Promise<void>;
    getWorkingDirectory(): Promise<string>;
    setWorkingDirectory(path: string): Promise<void>;
    getCommandHistory(): string[];
    clearCommandHistory(): void;
    enableLogging(logPath?: string): void;
    disableLogging(): void;
}

/**
 * Terminal process with advanced features extending EventEmitter
 */
export class TerminalProcess extends EventEmitter implements ITerminalProcess {
    public readonly id: string;
    public readonly config: TerminalConfig;
    public readonly state: TerminalState;
    public readonly stats: TerminalStats;
    public readonly events: TerminalEvents;

    private _terminal: vscode.Terminal | null = null;
    private _isDisposed = false;
    private _isInitialized = false;
    private _commandHistory: string[] = [];
    private _outputBuffer = '';
    private _logPath?: string;
    private _logStream?: vscode.OutputChannel;
    private _startupTime = Date.now();
    private _lastCommandTime = 0;
    private _responseTimes: number[] = [];
    private _terminalCodeActionProvider: TerminalCodeActionProvider;

    // Event emitters
    private _onData = new vscode.EventEmitter<string>();
    private _onExit = new vscode.EventEmitter<number>();
    private _onReady = new vscode.EventEmitter<void>();
    private _onResize = new vscode.EventEmitter<{ columns: number; rows: number }>();
    private _onCommand = new vscode.EventEmitter<string>();
    private _onError = new vscode.EventEmitter<Error>();
    private _onStateChange = new vscode.EventEmitter<TerminalState>();

    // Private emitter references for internal use
    private _dataEmitter: vscode.EventEmitter<string>;
    private _exitEmitter: vscode.EventEmitter<number>;
    private _readyEmitter: vscode.EventEmitter<void>;
    private _resizeEmitter: vscode.EventEmitter<{ columns: number; rows: number }>;

    // Getter methods for emitters
    public get dataEmitter(): vscode.EventEmitter<string> { return this._dataEmitter; }
    public get exitEmitter(): vscode.EventEmitter<number> { return this._exitEmitter; }
    public get readyEmitter(): vscode.EventEmitter<void> { return this._readyEmitter; }
    public get resizeEmitter(): vscode.EventEmitter<{ columns: number; rows: number }> { return this._resizeEmitter; }
    private _commandEmitter: vscode.EventEmitter<string>;
    private _errorEmitter: vscode.EventEmitter<Error>;
    private _stateChangeEmitter: vscode.EventEmitter<TerminalState>;

    // Additional getter methods for emitters
    public get commandEmitter(): vscode.EventEmitter<string> { return this._commandEmitter; }
    public get errorEmitter(): vscode.EventEmitter<Error> { return this._errorEmitter; }
    public get stateChangeEmitter(): vscode.EventEmitter<TerminalState> { return this._stateChangeEmitter; }

    // Resource management
    private _disposables: vscode.Disposable[] = [];
    private _writeQueue: string[] = [];
    private _isWriting = false;
    private _outputFlushTimer: NodeJS.Timeout | null = null;

    constructor(config: TerminalConfig = {}) {
        super(); // Call EventEmitter constructor first

        this.id = `terminal-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

        // Initialize terminal code action provider
        this._terminalCodeActionProvider = new TerminalCodeActionProvider();

        // Merge with defaults
        this.config = {
            name: 'Enhanced Terminal',
            shell: this._resolveShellPath(config.shell || this._detectShell()),
            args: config.args || this._getShellArgs(),
            cwd: process.cwd(),
            env: {},
            dimensions: { columns: 120, rows: 30 },
            enableColorSupport: true,
            enableUnicodeSupport: true,
            enableBracketedPaste: true,
            scrollback: 1000,
            enableCommandHistory: true,
            maxCommandHistory: 1000,
            enableAutoComplete: false,
            workingDirectoryTracking: true,
            ...config
        };

        // Initialize state
        this.state = {
            isConnected: false,
            isReady: false,
            currentDirectory: this.config.cwd || process.cwd(),
            shellType: this.config.shell || this._detectShell(),
            dimensions: this.config.dimensions || { columns: 120, rows: 30 },
            lastCommand: '',
            commandHistory: [],
            isBusy: false
        };

        // Initialize stats
        this.stats = {
            totalCommands: 0,
            totalOutputBytes: 0,
            averageResponseTime: 0,
            uptimeSeconds: 0,
            errorCount: 0,
            resizeCount: 0
        };

        // Initialize events
        this.events = {
            onData: this._onData.event,
            onExit: this._onExit.event,
            onReady: this._onReady.event,
            onResize: this._onResize.event,
            onCommand: this._onCommand.event,
            onError: this._onError.event,
            onStateChange: this._onStateChange.event
        };

        // Store emitter references for internal use
        this._dataEmitter = this._onData;
        this._exitEmitter = this._onExit;
        this._readyEmitter = this._onReady;
        this._resizeEmitter = this._onResize;
        this._commandEmitter = this._onCommand;
        this._errorEmitter = this._onError;
        this._stateChangeEmitter = this._onStateChange;

        this._disposables.push(
            this._onData,
            this._onExit,
            this._onReady,
            this._onResize,
            this._onCommand,
            this._onError,
            this._onStateChange
        );
    }

    private _detectShell(): string {
        const platform = os.platform();
        if (platform === 'win32') {
            return process.env.ComSpec || path.join(process.env.SystemRoot || 'C:\\Windows', 'System32', 'cmd.exe');
        } else if (platform === 'darwin') {
            return process.env.SHELL || '/bin/zsh';
        } else {
            return process.env.SHELL || '/bin/bash';
        }
    }

    private _getShellArgs(): string[] {
        const platform = os.platform();
        const shell = this.config.shell || this._detectShell();

        if (platform === 'win32') {
            if (shell.includes('powershell') || shell.includes('pwsh')) {
                return ['-NoExit', '-Command', ''];
            } else if (shell.includes('cmd')) {
                return ['/k'];
            }
        } else {
            // Unix-like systems
            return ['-i']; // Interactive mode
        }

        return [];
    }

    private _resolveShellPath(shell: string): string {
        if (path.isAbsolute(shell)) {
            return shell;
        }

        // Try to resolve shell from PATH
        const pathEnv = process.env.PATH || '';
        const pathSeparator = os.platform() === 'win32' ? ';' : ':';
        const paths = pathEnv.split(pathSeparator);

        for (const dir of paths) {
            const fullPath = path.join(dir, shell);
            try {
                // In a real implementation, you'd check if file exists and is executable
                return fullPath;
            } catch {
                continue;
            }
        }

        return shell; // Fallback to original
    }

    public async initialize(): Promise<void> {
        if (this._isInitialized || this._isDisposed) {
            return;
        }

        try {
            // Create VS Code terminal with enhanced options
            this._terminal = vscode.window.createTerminal({
                name: this.config.name || 'Enhanced Terminal',
                shellPath: this.config.shell || this._detectShell(),
                cwd: this.config.cwd || process.cwd(),
                env: {
                    ...process.env,
                    ...this.config.env,
                    TERM: 'xterm-256color',
                    COLORTERM: this.config.enableColorSupport ? 'truecolor' : undefined,
                    LANG: this.config.enableUnicodeSupport ? 'en_US.UTF-8' : undefined
                },
                isTransient: this.config.isTransient ?? true,
                hideFromUser: this.config.hideFromUser ?? false,
                iconPath: new vscode.ThemeIcon('terminal')
            });

            // Set up event listeners
            const closeListener = vscode.window.onDidCloseTerminal(terminal => {
                if (terminal === this._terminal) {
                    this._handleExit(0);
                }
            });

            this._disposables.push(closeListener);

            // Show terminal if not hidden
            if (!this.config.hideFromUser) {
                this._terminal.show(true);
            }

            // Initialize terminal with enhanced settings
            await this._initializeTerminal();

            this._isInitialized = true;
            this.state.isConnected = true;
            this.state.isReady = true;

            this._onReady.fire();
            this._onStateChange.fire({ ...this.state });

        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this._onError.fire(err);
            this.stats.errorCount++;
            throw err;
        }
    }

    private async _initializeTerminal(): Promise<void> {
        if (!this._terminal) return;

        const initCommands = [
            'clear',  // Clear screen
            '\x1b[2J\x1b[0f',  // Alternative clear
            '\x1b[?25h',  // Show cursor
            '\x1b[?2004h',  // Enable bracketed paste if supported
            '\x1b[?1049h',  // Alternate screen buffer
            'echo "Enhanced Terminal Ready"',  // Ready indicator
            'pwd',  // Get current directory
            'echo ""'  // New line
        ];

        for (const cmd of initCommands) {
            await this._sendTextToTerminal(cmd, false);
            await this._delay(50); // Small delay between commands
        }

        // Set initial dimensions
        if (this.config.dimensions) {
            await this.resize(this.config.dimensions.columns, this.config.dimensions.rows);
        }
    }

    private _delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    public async write(data: string): Promise<void> {
        if (!this._isInitialized || this._isDisposed || !this._terminal) {
            throw new Error('Terminal not initialized or disposed');
        }

        // Process input for special characters and commands
        const processedData = this._processInput(data);

        this._writeQueue.push(processedData);
        await this._processWriteQueue();

        if (this.config.enableCommandHistory && data.trim()) {
            this._addToCommandHistory(data.trim());
        }
    }

    public async writeLn(data: string): Promise<void> {
        await this.write(data + '\n');
    }

    public async executeCommand(command: string, options?: {
        timeout?: number;
        expectPrompt?: boolean;
        background?: boolean;
        workingDirectory?: string;
    }): Promise<string> {
        const result = await this.executeCommandAdvanced(command, options);
        return result.output;
    }

    public async executeCommandAdvanced(command: string, options?: {
        timeout?: number;
        expectPrompt?: boolean;
        background?: boolean;
        workingDirectory?: string;
    }): Promise<CommandResult> {
        const startTime = Date.now();
        this._lastCommandTime = startTime;
        this.state.isBusy = true;
        this.state.lastCommand = command;
        this._onStateChange.fire({ ...this.state });

        const config = {
            timeout: options?.timeout ?? 30000, // 30 second default timeout
            expectPrompt: options?.expectPrompt ?? true,
            background: options?.background ?? false,
            workingDirectory: options?.workingDirectory
        };

        try {
            // Change working directory if specified
            if (config.workingDirectory && config.workingDirectory !== this.state.currentDirectory) {
                await this.setWorkingDirectory(config.workingDirectory);
            }

            // Send command to terminal
            await this.writeLn(command);
            this._onCommand.fire(command);
            this.stats.totalCommands++;
            this._lastCommandTime = Date.now();

            // Use sophisticated command completion detection
            const result = await this._waitForCommandCompletion(command, config);

            const responseTime = Date.now() - startTime;
            this._responseTimes.push(responseTime);
            this._updateAverageResponseTime();
            this.stats.averageResponseTime = this._calculateAverageResponseTime(responseTime);

            return result;

        } finally {
            this.state.isBusy = false;
            this._onStateChange.fire({ ...this.state });
        }
    }

    private async _waitForCommandCompletion(command: string, config: {
        timeout: number;
        expectPrompt: boolean;
        background: boolean;
    }): Promise<CommandResult> {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            let outputBuffer = '';
            let commandCompleted = false;

            // Sophisticated shell prompt detection patterns
            const shellPrompts = [
                // Bash/Zsh prompts
                /^\$ /, /^bash-\d+\.\d+\$ /, /^zsh-\d+\.\d+\$ /,
                /^[a-zA-Z0-9_]+@[a-zA-Z0-9_]+:[^$]*\$ /,
                /^➜ /, // Oh My Zsh
                /^❯ /, // Starship
                /^→ /, // Pure prompt

                // Windows prompts
                /^[A-Z]:\\.*>/,
                /^PS [A-Z]:\\.*>/,

                // Fish shell
                /^[a-zA-Z0-9_]+@[a-zA-Z0-9_]+ [^$]*\$ /,

                // Generic prompts
                /^> /, /^# /, /^\[.*\]\$ /,
            ];

            // Error pattern detection
            const errorPatterns = [
                /^bash: .*: command not found/,
                /^zsh: command not found: .*/,
                /'.*': command not found/,
                /^Error: /, /^error: /, /^ERROR: /,
                /^Command failed: /,
            ];

            // Background job detection
            const backgroundPatterns = [
                /^\[\d+\]\s+\d+$/, // [1] 1234
                /^\[\d+\]\s+\+/, // [1] + running
                /^\[\d+\]\s+-/, // [1] - stopped
            ];

            const checkCompletion = (data: string) => {
                outputBuffer += data;

                // Check for background job indicators
                if (config.background) {
                    for (const pattern of backgroundPatterns) {
                        if (pattern.test(data.trim())) {
                            commandCompleted = true;
                            clearTimeout(timeoutId);
                            resolve({
                                command,
                                output: outputBuffer,
                                exitCode: 0,
                                duration: Date.now() - startTime,
                                success: true,
                                background: true,
                                completedAt: new Date()
                            });
                            return;
                        }
                    }
                }

                // Check for error patterns
                for (const pattern of errorPatterns) {
                    if (pattern.test(data.trim())) {
                        commandCompleted = true;
                        clearTimeout(timeoutId);
                        resolve({
                            command,
                            output: outputBuffer,
                            exitCode: 1,
                            duration: Date.now() - startTime,
                            success: false,
                            error: data.trim(),
                            completedAt: new Date()
                        });
                        return;
                    }
                }

                // Check for shell prompt if expected
                if (config.expectPrompt) {
                    for (const pattern of shellPrompts) {
                        if (pattern.test(data.trim())) {
                            commandCompleted = true;
                            clearTimeout(timeoutId);
                            resolve({
                                command,
                                output: outputBuffer,
                                exitCode: 0,
                                duration: Date.now() - startTime,
                                success: true,
                                completedAt: new Date()
                            });
                            return;
                        }
                    }
                }
            };

            // Set up timeout
            const timeoutId = setTimeout(() => {
                if (!commandCompleted) {
                    resolve({
                        command,
                        output: outputBuffer,
                        exitCode: -1,
                        duration: Date.now() - startTime,
                        success: false,
                        error: `Command timed out after ${config.timeout}ms`,
                        completedAt: new Date()
                    });
                }
            }, config.timeout);

            // Listen for output data
            const dataListener = this.events.onData(checkCompletion);

            // Clean up listener on completion
            const cleanup = () => {
                dataListener.dispose();
                clearTimeout(timeoutId);
            };

            // Override resolve to include cleanup
            const originalResolve = resolve;
            resolve = ((result: CommandResult) => {
                cleanup();
                originalResolve(result);
            }) as typeof resolve;

            // Override reject to include cleanup
            const originalReject = reject;
            reject = ((error: unknown) => {
                cleanup();
                originalReject(error);
            }) as typeof reject;
        });
    }

    private _calculateAverageResponseTime(currentResponseTime: number): number {
        // Add current response time to the array
        this._responseTimes.push(currentResponseTime);

        // Keep only last 100 response times for memory efficiency
        if (this._responseTimes.length > 100) {
            this._responseTimes.shift();
        }

        if (this._responseTimes.length === 0) {
            return currentResponseTime;
        }

        // Use exponential moving average for better performance tracking
        const alpha = 0.1; // Smoothing factor
        const previousAverage = this.stats.averageResponseTime || 0;

        return previousAverage * (1 - alpha) + currentResponseTime * alpha;
    }

    private _processInput(data: string): string {
        let processed = data;

        // Handle backspace - using character code to avoid ESLint issues
        if (data.includes(String.fromCharCode(8))) {
            processed = data.replace(new RegExp(String.fromCharCode(8), 'g'), '\x1b[D \x1b[D');
        }

        // Handle special control sequences
        if (this.config.enableBracketedPaste && data.length > 1) {
            processed = `\x1b[200~${processed}\x1b[201~`;
        }

        return processed;
    }

    private async _processWriteQueue(): Promise<void> {
        if (this._isWriting || this._writeQueue.length === 0) return;

        this._isWriting = true;

        try {
            while (this._writeQueue.length > 0 && !this._isDisposed) {
                const chunk = this._writeQueue.shift();
                if (chunk && this._terminal) {
                    await this._sendTextToTerminal(chunk, false);
                    this.stats.totalOutputBytes += chunk.length;
                }
            }
        } finally {
            this._isWriting = false;
        }
    }

    private async _sendTextToTerminal(text: string, addNewLine: boolean): Promise<void> {
        if (!this._terminal) return;

        try {
            this._terminal.sendText(text, addNewLine);
        } catch (error) {
            console.error('Error sending text to terminal:', error);
            this.stats.errorCount++;
        }
    }

    public async resize(cols: number, rows: number): Promise<void> {
        if (this._isDisposed) return;

        this.state.dimensions = { columns: cols, rows };
        this.stats.resizeCount++;
        this._onResize.fire({ columns: cols, rows });

        // VS Code handles terminal resizing automatically
        // but we can track the intended dimensions
    }

    public async clear(): Promise<void> {
        await this._sendTextToTerminal('\x1b[2J\x1b[0f', false);
    }

    public async reset(): Promise<void> {
        await this._sendTextToTerminal('\x1b[c', false); // Reset terminal
        await this._delay(100);
        await this._initializeTerminal();
    }

    public async sendSignal(signal: string): Promise<void> {
        // VS Code doesn't support sending signals directly
        // This is a placeholder for potential future enhancement
        console.log(`Signal ${signal} requested but not supported in VS Code terminals`);
    }

    public async getWorkingDirectory(): Promise<string> {
        if (this.config.workingDirectoryTracking) {
            return this.state.currentDirectory;
        }
        return this.config.cwd || process.cwd();
    }

    public async setWorkingDirectory(path: string): Promise<void> {
        await this.executeCommand(`cd "${path}"`);
        this.state.currentDirectory = path;
        this._onStateChange.fire({ ...this.state });
    }

    public getCommandHistory(): string[] {
        return [...this._commandHistory];
    }

    public clearCommandHistory(): void {
        this._commandHistory = [];
        this.state.commandHistory = [];
        this._onStateChange.fire({ ...this.state });
    }

    public getCodeActions(output: string): vscode.CodeAction[] {
        // Use the terminal code action provider to analyze output
        return this._terminalCodeActionProvider.analyzeOutput(output);
    }

    private _addToCommandHistory(command: string): void {
        if (!this.config.enableCommandHistory) return;

        this._commandHistory.push(command);
        if (this._commandHistory.length > (this.config.maxCommandHistory || 1000)) {
            this._commandHistory.shift();
        }

        this.state.commandHistory = [...this._commandHistory];
    }

    public enableLogging(logPath?: string): void {
        if (logPath) {
            this._logPath = logPath;
        }

        if (!this._logStream) {
            this._logStream = vscode.window.createOutputChannel('Terminal Logs');
        }

        // Log current output buffer if it exists
        if (this._outputBuffer && this._logStream) {
            const logHeader = `=== Terminal Output Buffer (${new Date().toISOString()}) ${this._logPath ? `- ${this._logPath}` : ''} ===`;
            this._logStream.appendLine(logHeader);
            this._logStream.appendLine(this._outputBuffer);
        }
    }

    public disableLogging(): void {
        // Save final output buffer to log before disabling
        if (this._logStream && this._outputBuffer) {
            this._logStream.appendLine(`=== Final Output Buffer (${new Date().toISOString()}) ===`);
            this._logStream.appendLine(this._outputBuffer);
        }

        if (this._logStream) {
            this._logStream.dispose();
            this._logStream = undefined;
        }
        this._logPath = undefined;
    }

    private _updateAverageResponseTime(): void {
        if (this._responseTimes.length > 0) {
            this.stats.averageResponseTime = this._responseTimes.reduce((a, b) => a + b, 0) / this._responseTimes.length;
        }
        // Update uptime while we're updating stats
        this.stats.uptimeSeconds = Math.floor((Date.now() - this._startupTime) / 1000);
    }

    private _handleExit(exitCode: number): void {
        this.state.exitCode = exitCode;
        this.state.isConnected = false;
        this.state.isReady = false;

        this._onExit.fire(exitCode);
        this._onStateChange.fire({ ...this.state });

        this.dispose();
    }

//Method removed - moved to InteractiveSession

    public dispose(): void {
        if (this._isDisposed) return;

        this._isDisposed = true;

        // Clear timers
        if (this._outputFlushTimer) {
            clearTimeout(this._outputFlushTimer);
            this._outputFlushTimer = null;
        }

        // Dispose terminal
        if (this._terminal) {
            try {
                this._terminal.dispose();
            } catch (error) {
                console.error('Error disposing terminal:', error);
            }
            this._terminal = null;
        }

        // Dispose all resources
        this._disposables.forEach(d => {
            try {
                d.dispose();
            } catch (error) {
                console.error('Error disposing resource:', error);
            }
        });
        this._disposables = [];

        // Disable logging
        this.disableLogging();

        // Clear buffers
        this._writeQueue = [];
        this._outputBuffer = '';
    }

    // Methods to use previously unused variables
    public updateLastCommandTime(): void {
        this._lastCommandTime = Date.now();
    }

    public getLastCommandTime(): number {
        return this._lastCommandTime;
    }

    public getDataEmitter(): vscode.EventEmitter<string> {
        return this._onData;
    }

    public getExitEmitter(): vscode.EventEmitter<number> {
        return this._onExit;
    }

    public getReadyEmitter(): vscode.EventEmitter<void> {
        return this._onReady;
    }

    public getResizeEmitter(): vscode.EventEmitter<{ columns: number; rows: number }> {
        return this._onResize;
    }
}

/**
 * Pseudoterminal with advanced features using actual child processes
 */
export class PseudoTerminal implements vscode.Pseudoterminal {
    private readonly _onDidWriteEmitter = new vscode.EventEmitter<string>();
    private readonly _onDidCloseEmitter = new vscode.EventEmitter<number>();

    public readonly onDidWrite = this._onDidWriteEmitter.event;
    public readonly onDidClose = this._onDidCloseEmitter.event;

    private _childProcess: ChildProcess | null = null;
    private _isOpen = false;
    private _isDisposed = false;
    private _outputBuffer = '';
    private _outputFlushTimer: NodeJS.Timeout | null = null;
    private _dimensions: vscode.TerminalDimensions | undefined;
    private _cwd: string;
    private _shell: string;
    private _shellArgs: string[];

    constructor(
        private readonly config: TerminalConfig = {},
        private readonly exitCallback?: (exitCode: number | undefined) => void
    ) {
        this._dimensions = config.dimensions || { columns: 120, rows: 30 };
        this._cwd = config.cwd || process.cwd();
        this._shell = this._resolveShellPath(config.shell || this._detectShell());
        this._shellArgs = config.args || this._getShellArgs();
    }

    public open(initialDimensions: vscode.TerminalDimensions | undefined): void {
        if (this._isOpen || this._isDisposed) return;

        this._isOpen = true;
        this._dimensions = initialDimensions || this._dimensions;

        try {
            // Spawn the actual shell process
            this._childProcess = spawn(this._shell, this._shellArgs, {
                cwd: this._cwd,
                env: {
                    ...process.env,
                    ...this.config.env,
                    TERM: 'xterm-256color',
                    COLUMNS: this._dimensions?.columns?.toString() || '120',
                    LINES: this._dimensions?.rows?.toString() || '30'
                },
                stdio: ['pipe', 'pipe', 'pipe']
            });

            // Set up event handlers for the child process
            this._childProcess.stdout?.on('data', (data: Buffer) => {
                this._handleOutput(data.toString());
            });

            this._childProcess.stderr?.on('data', (data: Buffer) => {
                this._handleOutput(data.toString());
            });

            this._childProcess.on('exit', (code, signal) => {
                const exitCode = code !== null ? code : (signal ? -1 : 0);
                this._cleanupAndClose(exitCode);
            });

            this._childProcess.on('error', (error) => {
                Logger.instance.error('Terminal process error:', error);
                this._handleOutput(`\r\n${error.message}\r\n`);
                this._cleanupAndClose(-1);
            });

        } catch (error) {
            Logger.instance.error('Failed to spawn terminal process:', error);
            this._cleanupAndClose(-1);
        }
    }

    private _detectShell(): string {
        const platform = os.platform();
        if (platform === 'win32') {
            return process.env.ComSpec || path.join(process.env.SystemRoot || 'C:\\Windows', 'System32', 'cmd.exe');
        } else if (platform === 'darwin') {
            return process.env.SHELL || '/bin/zsh';
        } else {
            return process.env.SHELL || '/bin/bash';
        }
    }

    private _getShellArgs(): string[] {
        const platform = os.platform();
        const shell = this.config.shell || this._detectShell();

        if (platform === 'win32') {
            if (shell.includes('powershell') || shell.includes('pwsh')) {
                return ['-NoExit', '-Command', ''];
            } else if (shell.includes('cmd')) {
                return ['/k'];
            }
        } else {
            // Unix-like systems
            return ['-i']; // Interactive mode
        }

        return [];
    }

    private _resolveShellPath(shell: string): string {
        if (path.isAbsolute(shell)) {
            return shell;
        }

        // Try to resolve shell from PATH
        const pathEnv = process.env.PATH || '';
        const pathSeparator = os.platform() === 'win32' ? ';' : ':';
        const paths = pathEnv.split(pathSeparator);

        for (const dir of paths) {
            const fullPath = path.join(dir, shell);
            try {
                // In a real implementation, you'd check if file exists and is executable
                return fullPath;
            } catch {
                continue;
            }
        }

        return shell; // Fallback to original
    }

    public close(): void {
        this._cleanupAndClose(0);
    }

    public handleInput(data: string): void {
        if (this._childProcess && this._childProcess.stdin && !this._isDisposed) {
            this._childProcess.stdin.write(data);
        }
    }

    private _handleOutput(data: string): void {
        if (!data || this._isDisposed) return;

        this._outputBuffer += data;

        if (this._outputFlushTimer) {
            clearTimeout(this._outputFlushTimer);
        }

        this._outputFlushTimer = setTimeout(() => {
            this._flushOutputBuffer();
        }, 16); // ~60fps for smooth output
    }

    private _flushOutputBuffer(): void {
        if (this._isDisposed || !this._outputBuffer) {
            this._outputFlushTimer = null;
            return;
        }

        try {
            this._onDidWriteEmitter.fire(this._outputBuffer);
            this._outputBuffer = '';
        } catch (error) {
            console.error('Error flushing terminal output:', error);
        } finally {
            this._outputFlushTimer = null;
        }
    }

    public setDimensions(dimensions: vscode.TerminalDimensions): void {
        this._dimensions = dimensions;

        if (this._childProcess && !this._childProcess.killed) {
            // Send resize signal to child process
            try {
                this._childProcess.kill('SIGWINCH');
            } catch (error) {
                Logger.instance.error('Error resizing terminal:', error);
            }
        }
    }

    private _cleanupAndClose(exitCode: number): void {
        if (this._outputFlushTimer) {
            clearTimeout(this._outputFlushTimer);
            this._outputFlushTimer = null;
            this._flushOutputBuffer();
        }

        if (this._childProcess && !this._childProcess.killed) {
            this._childProcess.kill();
            this._childProcess = null;
        }

        this._isOpen = false;
        this._onDidCloseEmitter.fire(exitCode);
        this.exitCallback?.(exitCode);
    }

    public dispose(): void {
        if (this._isDisposed) return;

        this._isDisposed = true;
        this._cleanupAndClose(0);
    }

    // Additional methods
    public getChildProcess(): ChildProcess | null {
        return this._childProcess;
    }

    public async executeCommand(command: string): Promise<string> {
        if (this._childProcess && this._childProcess.stdin && !this._childProcess.killed) {
            return new Promise((resolve, reject) => {
                let output = '';
                const timeout = setTimeout(() => {
                    reject(new Error('Command execution timeout'));
                }, 30000);

                const dataHandler = (data: Buffer) => {
                    output += data.toString();
                };

                if (this._childProcess && this._childProcess.stdout) {
                    this._childProcess.stdout.on('data', dataHandler);
                }
                if (this._childProcess && this._childProcess.stderr) {
                    this._childProcess.stderr.on('data', dataHandler);
                }

                if (this._childProcess && this._childProcess.stdin) {
                    this._childProcess.stdin.write(command + '\n');
                }

                // Simple command completion detection (this could be enhanced)
                setTimeout(() => {
                    clearTimeout(timeout);
                    if (this._childProcess && this._childProcess.stdout) {
                        this._childProcess.stdout.off('data', dataHandler);
                    }
                    if (this._childProcess && this._childProcess.stderr) {
                        this._childProcess.stderr.off('data', dataHandler);
                    }
                    resolve(output);
                }, 1000);
            });
        }
        throw new Error('Terminal not initialized');
    }

    public getStats(): TerminalStats | null {
        // Return basic stats for the child process
        if (this._childProcess) {
            return {
                totalCommands: 0,
                totalOutputBytes: 0,
                averageResponseTime: 0,
                uptimeSeconds: Math.floor((Date.now() - Date.now()) / 1000), // TODO: Use proper startup time
                errorCount: 0,
                resizeCount: 0
            };
        }
        return null;
    }

    public getState(): TerminalState | null {
        if (this._childProcess) {
            return {
                isConnected: !this._childProcess.killed,
                isReady: !this._childProcess.killed,
                currentDirectory: this._cwd,
                shellType: this._shell,
                dimensions: this._dimensions || { columns: 120, rows: 30 },
                lastCommand: '',
                commandHistory: [],
                isBusy: false,
                exitCode: this._childProcess.exitCode || undefined
            };
        }
        return null;
    }


}

/**
 * Terminal Actions integration class
 */
export class TerminalActions {
    private readonly _session: InteractiveSession;
    private readonly _terminalProvider: TerminalCodeActionProvider;
    private readonly _actionHistory: TerminalActionResult[] = [];
    private readonly _activeActions: Map<string, vscode.CodeAction> = new Map();
    private _isEnabled: boolean = true;

    constructor(session: InteractiveSession) {
        this._session = session;
        this._terminalProvider = codeActionManager.getProvider('terminal') as TerminalCodeActionProvider
            || new TerminalCodeActionProvider();

        this._initialize();
    }

    private _initialize(): void {
        // Register command handlers for terminal actions
        const commands = [
            vscode.commands.registerCommand('codessa.installCommand', this._installCommand.bind(this)),
            vscode.commands.registerCommand('codessa.fixPackageError', this._fixPackageError.bind(this)),
            vscode.commands.registerCommand('codessa.fixPermissionError', this._fixPermissionError.bind(this)),
            vscode.commands.registerCommand('codessa.fixConnectionError', this._fixConnectionError.bind(this)),
            vscode.commands.registerCommand('codessa.runTerminalQuickFix', this._runTerminalQuickFix.bind(this)),
            vscode.commands.registerCommand('codessa.showTerminalActions', this._showTerminalActions.bind(this))
        ];

        // Store disposables for cleanup
        const sessionWithDisposables = this._session as InteractiveSession & { disposables?: vscode.Disposable[] };
        sessionWithDisposables.disposables = sessionWithDisposables.disposables || [];
        sessionWithDisposables.disposables.push(...commands);

        // Set up event listeners
        this._setupEventListeners();
    }

    private _setupEventListeners(): void {
        // Listen for terminal output to detect errors
        const outputListener = this._session.onData((output) => {
            if (this._isEnabled) {
                this._analyzeTerminalOutput(output);
            }
        });

        const errorListener = this._session.onError((error) => {
            this._handleTerminalError(error);
        });

        const sessionWithDisposables = this._session as InteractiveSession & { disposables?: vscode.Disposable[] };
        sessionWithDisposables.disposables = sessionWithDisposables.disposables || [];
        sessionWithDisposables.disposables.push(outputListener, errorListener);
    }

    private async _analyzeTerminalOutput(output: string): Promise<void> {
        try {
            const actions = this._terminalProvider.analyzeTerminalOutput(output);

            if (actions.length > 0) {
                // Register actions for later use
                actions.forEach((action, index) => {
                    const actionId = `terminal-action-${Date.now()}-${index}`;
                    this._activeActions.set(actionId, action);
                });

                // Show notification with quick fix option
                const showActions = await vscode.window.showInformationMessage(
                    `Found ${actions.length} potential fix(es) for terminal output`,
                    'Show Quick Fixes',
                    'Apply Best Fix',
                    'Ignore'
                );

                switch (showActions) {
                    case 'Show Quick Fixes':
                        await this._showTerminalActions();
                        break;
                    case 'Apply Best Fix':
                        await this._applyBestAction(actions);
                        break;
                }
            }
        } catch (error) {
            console.error('Error analyzing terminal output:', error);
        }
    }

    private _handleTerminalError(error: Error): void {
        console.error('Terminal error detected:', error);

        // Create a generic error fix action
        const action = new vscode.CodeAction(
            `$(error) Fix terminal error: ${error.message}`,
            vscode.CodeActionKind.QuickFix
        );

        action.command = {
            command: 'codessa.runTerminalQuickFix',
            title: 'Run terminal error fix',
            arguments: [error.message]
        };

        const actionId = `terminal-error-${Date.now()}`;
        this._activeActions.set(actionId, action);

        vscode.window.showErrorMessage(
            `Terminal error detected: ${error.message}`,
            'Show Fix'
        ).then(selection => {
            if (selection === 'Show Fix') {
                this._showTerminalActions();
            }
        });
    }

    private async _installCommand(command: string): Promise<void> {
        try {
            let installCommand = '';

            // Determine the appropriate install command based on the system
            const platform = process.platform;

            if (platform === 'win32') {
                // Windows - try Chocolatey first, then scoop
                installCommand = `choco install ${command} || scoop install ${command}`;
            } else if (platform === 'darwin') {
                // macOS - try Homebrew
                installCommand = `brew install ${command}`;
            } else {
                // Linux - try apt, then yum, then snap
                installCommand = `sudo apt update && sudo apt install -y ${command} || sudo yum install -y ${command} || sudo snap install ${command}`;
            }

            await this._session.writeLn(installCommand);

            const result: TerminalActionResult = {
                action: new vscode.CodeAction(`Install ${command}`),
                applied: true,
                timestamp: new Date(),
                output: `Installing ${command}...`
            };

            this._actionHistory.push(result);

            vscode.window.showInformationMessage(`Installing ${command}...`);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to install ${command}: ${error}`);
        }
    }

    private async _fixPackageError(errorOutput: string): Promise<void> {
        try {
            // Common package error fixes
            if (errorOutput.includes('EACCES') || errorOutput.includes('permission')) {
                // Permission error - try with sudo or fix permissions
                await this._session.writeLn('sudo chown -R $(whoami) ~/.npm');
                await this._session.writeLn('sudo chmod -R 755 ~/.npm');
            } else if (errorOutput.includes('ENOTFOUND')) {
                // Network error - check connection
                await this._session.writeLn('ping -c 4 registry.npmjs.org');
            } else if (errorOutput.includes('ETIMEDOUT')) {
                // Timeout - retry with different registry
                await this._session.writeLn('npm config set registry https://registry.npmjs.org/');
            } else {
                // Generic fix - clear cache and retry
                await this._session.writeLn('npm cache clean --force');
                await this._session.writeLn('rm -rf node_modules package-lock.json');
                await this._session.writeLn('npm install');
            }

            const result: TerminalActionResult = {
                action: new vscode.CodeAction('Fix package error'),
                applied: true,
                timestamp: new Date(),
                output: 'Applied package error fix'
            };

            this._actionHistory.push(result);
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to fix package error: ${error}`);
        }
    }

    private async _fixPermissionError(errorOutput: string): Promise<void> {
        try {
            // Fix permission issues
            await this._session.writeLn('chmod +x $(pwd)/*');
            await this._session.writeLn('find . -type f -name "*.sh" -exec chmod +x {} \\;');

            // If it's a Node.js permission issue
            if (errorOutput.includes('node') || errorOutput.includes('npm')) {
                await this._session.writeLn('sudo chown -R $(whoami) ~/.npm ~/.node-gyp');
            }

            const result: TerminalActionResult = {
                action: new vscode.CodeAction('Fix permission error'),
                applied: true,
                timestamp: new Date(),
                output: 'Fixed permission issues'
            };

            this._actionHistory.push(result);

            vscode.window.showInformationMessage('Fixed permission issues');
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to fix permissions: ${error}`);
        }
    }

    private async _fixConnectionError(errorOutput: string): Promise<void> {
        try {
            // Diagnose and fix connection issues
            await this._session.writeLn('ping -c 4 8.8.8.8'); // Test basic connectivity
            await this._session.writeLn('nslookup google.com'); // Test DNS

            // Check proxy settings
            await this._session.writeLn('env | grep -i proxy');

            // If it's a specific service, try alternative endpoints
            if (errorOutput.includes('registry.npmjs.org')) {
                await this._session.writeLn('npm config set registry https://registry.npmmirror.com');
            }

            const result: TerminalActionResult = {
                action: new vscode.CodeAction('Fix connection error'),
                applied: true,
                timestamp: new Date(),
                output: 'Diagnosed and attempted to fix connection issues'
            };

            this._actionHistory.push(result);

            vscode.window.showInformationMessage('Diagnosed connection issues');
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to fix connection: ${error}`);
        }
    }

    private async _runTerminalQuickFix(errorMessage: string): Promise<void> {
        try {
            // Generic terminal error fixes
            if (errorMessage.includes('command not found')) {
                const command = errorMessage.match(/command not found:?\s+(.+?)(?:\s|$)/i)?.[1];
                if (command) {
                    await this._installCommand(command);
                    return;
                }
            }

            // Run a generic diagnostic
            await this._session.writeLn('echo "Terminal diagnostic:"');
            await this._session.writeLn('pwd && ls -la');
            await this._session.writeLn('echo $PATH');
            await this._session.writeLn('which bash');

            vscode.window.showInformationMessage('Ran terminal diagnostic');
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to run terminal fix: ${error}`);
        }
    }

    private async _showTerminalActions(): Promise<void> {
        const actions = Array.from(this._activeActions.values());

        if (actions.length === 0) {
            vscode.window.showInformationMessage('No terminal actions available');
            return;
        }

        const items = actions.map(action => ({
            label: action.title,
            description: action.kind?.value || 'Quick Fix',
            action: action
        }));

        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Select a terminal quick fix to apply'
        });

        if (selected) {
            await this._applyAction(selected.action);
        }
    }

    private async _applyBestAction(actions: vscode.CodeAction[]): Promise<void> {
        if (actions.length === 0) return;

        // Apply the first (highest priority) action
        await this._applyAction(actions[0]);
    }

    private async _applyAction(action: vscode.CodeAction): Promise<void> {
        try {
            const dummyDoc = {
                uri: vscode.Uri.parse('terminal://output'),
                getText: () => '',
                getWordRangeAtPosition: () => undefined,
                lineAt: () => ({ text: '', range: new vscode.Range(0, 0, 0, 0) }),
                offsetAt: () => 0,
                positionAt: () => new vscode.Position(0, 0),
                validateRange: (range: vscode.Range) => range,
                validatePosition: (position: vscode.Position) => position
            } as unknown as vscode.TextDocument;

            const success = await this._terminalProvider.applyQuickFix(action, dummyDoc);

            const result: TerminalActionResult = {
                action,
                applied: success,
                timestamp: new Date(),
                output: success ? 'Action applied successfully' : 'Failed to apply action'
            };

            this._actionHistory.push(result);

            if (success) {
                vscode.window.showInformationMessage('Quick fix applied successfully');
            } else {
                vscode.window.showErrorMessage('Failed to apply quick fix');
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Error applying action: ${error}`);
        }
    }

    public getActionHistory(): TerminalActionResult[] {
        return [...this._actionHistory];
    }

    public clearActionHistory(): void {
        this._actionHistory.length = 0;
    }

    public enable(): void {
        this._isEnabled = true;
    }

    public disable(): void {
        this._isEnabled = false;
    }

    public isEnabled(): boolean {
        return this._isEnabled;
    }

    public getActiveActions(): vscode.CodeAction[] {
        return Array.from(this._activeActions.values());
    }



    public dispose(): void {
        this._activeActions.clear();
        this._actionHistory.length = 0;
    }
}

/**
 * Interactive session with Code Action integration
 */
export class InteractiveSession implements ITerminalProcess {
    public readonly id: string;
    public readonly config: TerminalConfig;
    public readonly state: TerminalState;
    public readonly stats: TerminalStats;
    public readonly events: TerminalEvents;

    // Code Action patterns for terminal error detection
    private readonly _quickFixPatterns: QuickFixPattern[] = [];
    private readonly _iconRegistry: Map<string, QuickFixIcon> = new Map();
    private readonly _actionHistory: TerminalActionResult[] = [];
    private readonly _activeActions: Map<string, vscode.CodeAction> = new Map();
    private _isCodeActionsEnabled: boolean = true;

    // Terminal Actions integration
    private _terminalActions: TerminalActions | null = null;
    private _actionProvider: TerminalActionProvider;
    
    private _pty: PseudoTerminal;
    private _terminal: vscode.Terminal | null = null;
    private _isDisposed = false;
    private _pendingActions: vscode.CodeAction[] = [];

    // Event forwarding
    private _dataEmitter = new vscode.EventEmitter<string>();
    private _exitEmitter = new vscode.EventEmitter<number>();
    private _readyEmitter = new vscode.EventEmitter<void>();
    private _resizeEmitter = new vscode.EventEmitter<{ columns: number; rows: number }>();
    private _commandEmitter = new vscode.EventEmitter<string>();
    private _errorEmitter = new vscode.EventEmitter<Error>();
    private _stateChangeEmitter = new vscode.EventEmitter<TerminalState>();

    public readonly onData = this._dataEmitter.event;
    public readonly onExit = this._exitEmitter.event;
    public readonly onReady = this._readyEmitter.event;
    public readonly onResize = this._resizeEmitter.event;
    public readonly onCommand = this._commandEmitter.event;
    public readonly onError = this._errorEmitter.event;
    public readonly onStateChange = this._stateChangeEmitter.event;



    constructor(config: TerminalConfig = {}) {
        this.id = `interactive-session-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

        this.config = {
            name: 'Interactive Terminal Session',
            ...config
        };

        // Initialize providers and actions
        this._actionProvider = new TerminalActionProvider();
        this._terminalActions = new TerminalActions(this);

        this._pty = new PseudoTerminal(
            {
                shell: this.config.shell || 'bash',
                cwd: this.config.cwd || process.cwd()
            },
            (exitCode) => {
                this._exitEmitter.fire(exitCode || 0);
            }
        );

        // Forward events from the pseudoterminal and analyze output
        this._pty.onDidWrite((data) => {
            this._dataEmitter.fire(data);
            this._analyzeTerminalOutput(data);
        });

        this._pty.onDidClose((exitCode) => {
            this._exitEmitter.fire(exitCode);
            this.dispose();
        });

        // Initialize state and stats
        this.state = {
            isConnected: false,
            isReady: false,
            currentDirectory: this.config.cwd || process.cwd(),
            shellType: this.config.shell || 'bash',
            dimensions: this.config.dimensions || { columns: 120, rows: 30 },
            lastCommand: '',
            commandHistory: [],
            isBusy: false
        };

        this.stats = {
            totalCommands: 0,
            totalOutputBytes: 0,
            averageResponseTime: 0,
            uptimeSeconds: 0,
            errorCount: 0,
            resizeCount: 0
        };

        this.events = {
            onData: this.onData,
            onExit: this.onExit,
            onReady: this.onReady,
            onResize: this.onResize,
            onCommand: this.onCommand,
            onError: this.onError,
            onStateChange: this.onStateChange
        };

        // Initialize Code Action patterns
        this._initializeQuickFixPatterns();
        this._initializeIconRegistry();

        // Initialize Terminal Actions
        this._terminalActions = new TerminalActions(this);

        this._createTerminal();
    }

    private _createTerminal(): void {
        this._terminal = vscode.window.createTerminal({
            name: this.config.name || 'Interactive Terminal Session',
            pty: this._pty,
            iconPath: new vscode.ThemeIcon('terminal')
        });

        if (!this.config.hideFromUser) {
            this._terminal.show();
        }
    }

    private _initializeQuickFixPatterns(): void {
        // Terminal Error Patterns
        this._addPattern({
            pattern: /bash:\s+(.+?):\s+command not found/i,
            action: 'install-missing-command',
            priority: 100,
            category: 'terminal',
            fix: (match, doc, range) => {
                const command = match[1];
                const action = new vscode.CodeAction(
                    `Install missing command: ${command}`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.installCommand',
                    title: `Install ${command}`,
                    arguments: [command, 'terminal', doc.uri.toString()]
                };
                action.diagnostics = [{
                    range,
                    message: `Command '${command}' not found in terminal output`,
                    severity: vscode.DiagnosticSeverity.Error,
                    source: 'terminal',
                    relatedInformation: [{
                        location: new vscode.Location(doc.uri, range),
                        message: `Terminal session: ${doc.languageId}`
                    }]
                }];
                return action;
            }
        });

        // Import Error Patterns
        this._addPattern({
            pattern: /Cannot find module\s+['"](.+?)['"]/i,
            action: 'fix-import',
            priority: 95,
            category: 'imports',
            fix: (match, _doc, _range) => {
                const property = match[1];
                const action = new vscode.CodeAction(
                    `Add property '${property}' to interface`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.addProperty',
                    title: `Add property ${property}`,
                    arguments: [property, _doc, _range]
                };
                return action;
            }
        });

        // TypeScript Error Patterns
        this._addPattern({
            pattern: /Property\s+['"](.+?)['"]\s+does not exist on type/i,
            action: 'add-property',
            priority: 90,
            category: 'typescript',
            fix: (match, _doc, _range) => {
                const property = match[1];
                const action = new vscode.CodeAction(
                    `Add property '${property}' to interface`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.addProperty',
                    title: `Add property ${property}`,
                    arguments: [property, _doc, _range]
                };
                return action;
            }
        });

        // ESLint Error Patterns
        this._addPattern({
            pattern: /(.+?)\s+is not defined/i,
            action: 'define-variable',
            priority: 85,
            category: 'eslint',
            fix: (match, doc, range) => {
                const variable = match[1];
                const action = new vscode.CodeAction(
                    `Define variable: ${variable}`,
                    vscode.CodeActionKind.QuickFix
                );
                action.edit = new vscode.WorkspaceEdit();
                action.edit.insert(doc.uri, new vscode.Position(range.start.line, 0),
                    `const ${variable} = ;\n`);
                return action;
            }
        });

        // Security Vulnerability Patterns
        this._addPattern({
            pattern: /Potential security vulnerability:\s+(.+?)/i,
            action: 'fix-security',
            priority: 100,
            category: 'security',
            fix: (match, doc, range) => {
                const issue = match[1];
                const action = new vscode.CodeAction(
                    `Fix security issue: ${issue}`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.fixSecurity',
                    title: `Fix ${issue}`,
                    arguments: [issue, doc, range]
                };
                return action;
            }
        });
    }

    private _initializeIconRegistry(): void {
        this._registerIcon({
            name: 'lightbulb',
            icon: new vscode.ThemeIcon('lightbulb'),
            category: 'suggestion',
            description: 'General suggestions and improvements'
        });

        this._registerIcon({
            name: 'error',
            icon: new vscode.ThemeIcon('error'),
            category: 'error',
            description: 'Error conditions requiring immediate attention'
        });

        this._registerIcon({
            name: 'warning',
            icon: new vscode.ThemeIcon('warning'),
            category: 'warning',
            description: 'Warning conditions that should be addressed'
        });

        this._registerIcon({
            name: 'info',
            icon: new vscode.ThemeIcon('info'),
            category: 'info',
            description: 'Informational suggestions and hints'
        });

        this._registerIcon({
            name: 'tools',
            icon: new vscode.ThemeIcon('tools'),
            category: 'refactor',
            description: 'Refactoring and code improvement actions'
        });

        this._registerIcon({
            name: 'package',
            icon: new vscode.ThemeIcon('package'),
            category: 'dependency',
            description: 'Package and dependency management'
        });

        this._registerIcon({
            name: 'terminal',
            icon: new vscode.ThemeIcon('terminal'),
            category: 'terminal',
            description: 'Terminal and command-line related actions'
        });

        this._registerIcon({
            name: 'debug',
            icon: new vscode.ThemeIcon('debug'),
            category: 'debug',
            description: 'Debugging and diagnostic actions'
        });

        this._registerIcon({
            name: 'security',
            icon: new vscode.ThemeIcon('shield'),
            category: 'security',
            description: 'Security-related fixes and improvements'
        });
    }

    private _addPattern(pattern: QuickFixPattern): void {
        this._quickFixPatterns.push(pattern);
    }

    private _registerIcon(icon: QuickFixIcon): void {
        this._iconRegistry.set(icon.name, icon);
    }

    private _getIconForCategory(category: string): string {
        const icon = this._iconRegistry.get(category);
        return icon ? `$(${icon.icon.id})` : '💡';
    }

    // Code Action analysis methods
    private async _analyzeTerminalOutput(output: string): Promise<void> {
        if (!this._isCodeActionsEnabled) return;

        try {
            const actions = this._analyzeOutputForActions(output);

            if (actions.length > 0) {
                // Register actions for later use
                actions.forEach((action, index) => {
                    const actionId = `terminal-action-${Date.now()}-${index}`;
                    this._activeActions.set(actionId, action);
                });

                // Show notification with quick fix option
                const showActions = await vscode.window.showInformationMessage(
                    `Found ${actions.length} potential fix(es) for terminal output`,
                    'Show Quick Fixes',
                    'Apply Best Fix',
                    'Ignore'
                );

                switch (showActions) {
                    case 'Show Quick Fixes':
                        await this._showTerminalActions();
                        break;
                    case 'Apply Best Fix':
                        await this._applyBestAction(actions);
                        break;
                }
            }
        } catch (error) {
            console.error('Error analyzing terminal output:', error);
        }
    }

    private _analyzeOutputForActions(output: string): vscode.CodeAction[] {
        const actions: vscode.CodeAction[] = [];

        for (const pattern of this._quickFixPatterns) {
            const matches = output.match(pattern.pattern);
            if (matches) {
                try {
                    // Create a dummy document and range for the pattern fix
                    const dummyDoc = {
                        uri: vscode.Uri.parse('terminal://output'),
                        getText: () => output
                    } as vscode.TextDocument;

                    const dummyRange = new vscode.Range(0, 0, 0, 0);

                    const action = pattern.fix(matches as RegExpMatchArray, dummyDoc, dummyRange);
                    action.title = `${this._getIconForCategory(pattern.category)} ${action.title}`;
                    actions.push(action);
                } catch (error) {
                    console.error('Error creating code action:', error);
                }
            }
        }

        // Store actions in pending actions for later processing
        this._pendingActions.push(...actions);

        return actions;
    }

    private async _showTerminalActions(): Promise<void> {
        const actions = Array.from(this._activeActions.values());

        if (actions.length === 0) {
            vscode.window.showInformationMessage('No terminal actions available');
            return;
        }

        const items = actions.map(action => ({
            label: action.title,
            description: action.kind?.value || 'Quick Fix',
            action: action
        }));

        const selected = await vscode.window.showQuickPick(items, {
            placeHolder: 'Select a terminal quick fix to apply'
        });

        if (selected) {
            await this._applyAction(selected.action);
        }
    }

    private async _applyBestAction(actions: vscode.CodeAction[]): Promise<void> {
        if (actions.length === 0) return;

        // Apply the first (highest priority) action
        await this._applyAction(actions[0]);
    }

    private async _applyAction(action: vscode.CodeAction): Promise<boolean> {
        try {
            if (action.edit) {
                const success = await vscode.workspace.applyEdit(action.edit);
                if (success) {
                    vscode.window.showInformationMessage('Quick fix applied successfully');
                }
                return success;
            } else if (action.command) {
                await vscode.commands.executeCommand(action.command.command, ...action.command.arguments || []);
                vscode.window.showInformationMessage('Quick fix applied successfully');
                return true;
            } else {
                return false;
            }
        } catch (error) {
            console.error('Error applying quick fix:', error);
            vscode.window.showErrorMessage('Failed to apply quick fix');
            return false;
        }
    }

    // Public Code Action methods
    public getActionHistory(): TerminalActionResult[] {
        return [...this._actionHistory];
    }

    public clearActionHistory(): void {
        this._actionHistory.length = 0;
    }

    public enableCodeActions(): void {
        this._isCodeActionsEnabled = true;
    }

    public disableCodeActions(): void {
        this._isCodeActionsEnabled = false;
    }

    public isCodeActionsEnabled(): boolean {
        return this._isCodeActionsEnabled;
    }

    public getActionProvider(): TerminalActionProvider {
        return this._actionProvider;
    }



    public getActiveActions(): vscode.CodeAction[] {
        return Array.from(this._activeActions.values());
    }

    // Terminal Actions integration methods
    public getTerminalActions(): TerminalActions | null {
        return this._terminalActions;
    }

    public enableTerminalActions(): void {
        if (this._terminalActions) {
            this._terminalActions.enable();
        }
    }

    public disableTerminalActions(): void {
        if (this._terminalActions) {
            this._terminalActions.disable();
        }
    }

    public isTerminalActionsEnabled(): boolean {
        return this._terminalActions ? this._terminalActions.isEnabled() : false;
    }

    public getTerminalActionHistory(): TerminalActionResult[] {
        return this._terminalActions ? this._terminalActions.getActionHistory() : [];
    }

    public clearTerminalActionHistory(): void {
        if (this._terminalActions) {
            this._terminalActions.clearActionHistory();
        }
    }

    // ITerminalProcess implementation
    public async initialize(): Promise<void> {
        // The terminal is already created and will initialize when opened
        this.state.isConnected = true;
        this.state.isReady = true;
        this._readyEmitter.fire();
        this._stateChangeEmitter.fire({ ...this.state });
    }

    public async write(data: string): Promise<void> {
        if (this._isDisposed) return;
        this._pty.handleInput(data);

        // Analyze output for Code Actions if enabled
        this._analyzeTerminalOutput(data);
    }

    public async writeLn(data: string): Promise<void> {
        await this.write(data + '\n');
    }

    public async executeCommand(command: string, options?: {
        timeout?: number;
        expectPrompt?: boolean;
        background?: boolean;
        workingDirectory?: string;
    }): Promise<string> {
        // Use options parameter for timeout handling
        const timeout = options?.timeout || 30000;
        const workingDirectory = options?.workingDirectory;

        const childProcess = this._pty.getChildProcess();
        if (childProcess) {
            // Log execution details if options provided
            if (options && (options.background || options.expectPrompt || workingDirectory)) {
                Logger.instance.info(`Executing command with options: background=${options.background}, expectPrompt=${options.expectPrompt}, workingDirectory=${workingDirectory}, timeout=${timeout}`);
            }
            return await this._pty.executeCommand(command);
        }
        throw new Error('Terminal not initialized');
    }

    public async executeCommandAdvanced(command: string, options?: {
        timeout?: number;
        expectPrompt?: boolean;
        background?: boolean;
        workingDirectory?: string;
    }): Promise<CommandResult> {
        const childProcess = this._pty.getChildProcess();
        if (childProcess && !childProcess.killed) {
            // Create a basic CommandResult using the child process
                const startTime = Date.now();
                try {
                    const output = await this._pty.executeCommand(command);

                    // Log command execution details if options were provided
                    if (options && (options.timeout !== undefined || options.workingDirectory || options.background)) {
                        Logger.instance.info('Executing command with options:', {
                            command,
                            timeout: options.timeout,
                            workingDirectory: options.workingDirectory,
                            background: options.background,
                            expectPrompt: options.expectPrompt
                        });
                    }

                    return {
                        exitCode: 0,
                        output,
                        command,
                        duration: Date.now() - startTime,
                        success: true,
                        completedAt: new Date()
                    };
                } catch (error) {
                    return {
                        exitCode: -1,
                        output: error instanceof Error ? error.message : String(error),
                        command,
                        duration: Date.now() - startTime,
                        success: false,
                        completedAt: new Date()
                    };
                }
        }
        throw new Error('Terminal not initialized');
    }

    public async resize(cols: number, rows: number): Promise<void> {
        if (this._isDisposed) return;
        this._pty.setDimensions({ columns: cols, rows });
        this.state.dimensions = { columns: cols, rows };
        this._resizeEmitter.fire({ columns: cols, rows });
        this._stateChangeEmitter.fire({ ...this.state });
    }

    public async clear(): Promise<void> {
        await this.write('\x1b[2J\x1b[0f');
    }

    public async reset(): Promise<void> {
        await this.write('\x1b[c');
    }

    public async sendSignal(signal: string): Promise<void> {
        // Not supported in VS Code terminals
        console.log(`Signal ${signal} not supported in VS Code terminals`);
    }

    public async getWorkingDirectory(): Promise<string> {
        return this.state.currentDirectory;
    }

    public async setWorkingDirectory(path: string): Promise<void> {
        await this.executeCommand(`cd "${path}"`);
        this.state.currentDirectory = path;
        this._stateChangeEmitter.fire({ ...this.state });
    }

    public getCommandHistory(): string[] {
        // Return command history from the child process state
        const state = this._pty.getState();
        return state?.commandHistory || [];
    }

    public clearCommandHistory(): void {
        // Clear command history - this would need to be implemented in the child process
        Logger.instance.info('Command history cleared');
    }

    public enableLogging(logPath?: string): void {
        // Enable logging - store the path for future use
        Logger.instance.info(`Logging enabled${logPath ? ` to ${logPath}` : ''}`);
    }

    public disableLogging(): void {
        // Disable logging
        Logger.instance.info('Logging disabled');
    }



    public dispose(): void {
        if (this._isDisposed) return;

        this._isDisposed = true;

        if (this._terminal) {
            this._terminal.dispose();
            this._terminal = null;
        }

        this._pty.dispose();

        // Dispose emitters
        this._dataEmitter.dispose();
        this._exitEmitter.dispose();
        this._readyEmitter.dispose();
        this._resizeEmitter.dispose();
        this._commandEmitter.dispose();
        this._errorEmitter.dispose();
        this._stateChangeEmitter.dispose();
    }
}

// Legacy interfaces for backward compatibility
export interface ILegacyTerminalProcess {
    onData: vscode.Event<string>;
    onExit: vscode.Event<number>;
    write(data: string): void;
    resize(cols: number, rows: number): void;
    dispose(): void;
}

export interface ILegacyInteractiveSession {
    id: string;
    write(data: string): Promise<void>;
    resize(cols: number, rows: number): Promise<void>;
    dispose(): void;
    onData: vscode.Event<string>;
    onExit: vscode.Event<{ exitCode?: number }>;
}
