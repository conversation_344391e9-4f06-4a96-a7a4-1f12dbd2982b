{"version": 3, "file": "modal.js", "sourceRoot": "", "sources": ["../../../../src/ui/settings/components/modal.ts"], "names": [], "mappings": ";;AAKA,sCAEC;AAED,8BAmJC;AA5JD,8CAA8C;AAC9C,gDAAqE;AAErE,IAAI,YAAY,GAAkB,kCAAoB,CAAC;AAEvD,SAAgB,aAAa,CAAC,KAAoB;IAChD,YAAY,GAAG,KAAK,CAAC;AACvB,CAAC;AAED,SAAgB,SAAS,CAAC,OAA0F;IAClH,4BAA4B;IAC5B,MAAM,aAAa,GAAG,4BAA4B,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;IACzF,MAAM,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC;IACtC,gCAAgC;IAChC,IAAI,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC;IAClC,MAAM,YAAY,GAAG,gCAAgC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAC5E,IAAI,YAAY,EAAE,CAAC;QACjB,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,kGAAkG,YAAY,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;IACrL,CAAC;IACD,4BAA4B;IAC5B,MAAM,IAAI,GAAG,QAAQ,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;IAC9D,IAAI,IAAI;QAAE,IAAI,CAAC,MAAM,EAAE,CAAC;IAExB,gBAAgB;IAChB,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC9C,OAAO,CAAC,EAAE,GAAG,uBAAuB,CAAC;IACrC,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;IACtB,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;IACjC,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;IACxB,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC;IACzB,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC;IAC9B,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC;IAC/B,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,kBAAkB,CAAC,CAAC,gCAAgC;IAC/E,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;IAC9B,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IAC/B,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;IACpC,OAAO,CAAC,KAAK,CAAC,cAAc,GAAG,QAAQ,CAAC;IACxC,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;IAC5B,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,uCAAuC,CAAC;IAEnE,eAAe;IACf,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC5C,KAAK,CAAC,SAAS,GAAG,uBAAuB,CAAC;IAC1C,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IACrC,KAAK,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;IACzC,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;IAC/C,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,UAAU,CAAC;IAC1C,KAAK,CAAC,KAAK,CAAC,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;IACnD,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;IAC7C,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,aAAa,UAAU,CAAC,MAAM,EAAE,CAAC;IACtD,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,qBAAqB,CAAC;IAC5C,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC/B,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;IAC/B,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC;IAC/B,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC;IAC/B,KAAK,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;IAClC,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;IAC1B,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,UAAU,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC;IAC/D,KAAK,CAAC,KAAK,CAAC,UAAU,GAAG,WAAW,UAAU,CAAC,SAAS,CAAC,QAAQ,uCAAuC,UAAU,CAAC,SAAS,CAAC,QAAQ,0BAA0B,CAAC;IAEhK,eAAe;IACf,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC7C,MAAM,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,cAAc,CAAC;IAClD,MAAM,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC,gBAAgB,CAAC;IACtD,MAAM,CAAC,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC;IACnC,MAAM,CAAC,KAAK,CAAC,aAAa,GAAG,QAAQ,CAAC;IACtC,IAAI,aAAa,EAAE,CAAC;QAClB,MAAM,CAAC,SAAS,GAAG,kEAAkE,UAAU,CAAC,WAAW,UAAU,OAAO,CAAC,KAAK,gBAAgB,CAAC;IACrJ,CAAC;SAAM,CAAC;QACN,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC;IACrC,CAAC;IACD,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAE1B,gBAAgB;IAChB,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IACjD,UAAU,CAAC,SAAS,GAAG,WAAW,CAAC;IACnC,UAAU,CAAC,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC;IACvC,UAAU,CAAC,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC,eAAe,CAAC;IACvD,KAAK,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAE9B,aAAa;IACb,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC7C,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IAC9B,MAAM,CAAC,KAAK,CAAC,cAAc,GAAG,UAAU,CAAC;IACzC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,CAAC;IAC1B,MAAM,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;IAE/B,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACnD,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC;IACjC,SAAS,CAAC,SAAS,GAAG,kBAAkB,CAAC;IACzC,SAAS,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC;IAC1D,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC;IAChD,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;IAClD,SAAS,CAAC,KAAK,CAAC,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC;IAC9D,SAAS,CAAC,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC;IACrC,SAAS,CAAC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;IACjC,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;IACnC,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE;QACvB,OAAO,CAAC,MAAM,EAAE,CAAC;QACjB,IAAI,OAAO,CAAC,QAAQ;YAAE,OAAO,CAAC,QAAQ,EAAE,CAAC;IAC3C,CAAC,CAAC;IAEF,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACpD,UAAU,CAAC,WAAW,GAAG,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC;IAC3D,UAAU,CAAC,SAAS,GAAG,mBAAmB,CAAC;IAC3C,IAAI,aAAa,EAAE,CAAC;QAClB,UAAU,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC,iBAAiB,CAAC,UAAU,CAAC;QACtE,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,iBAAiB,CAAC,KAAK,CAAC;QAC5D,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,iBAAiB,CAAC,MAAM,CAAC;QAC9D,UAAU,CAAC,KAAK,CAAC,SAAS,GAAG,UAAU,CAAC,iBAAiB,CAAC,MAAM,CAAC;QACjE,UAAU,CAAC,KAAK,CAAC,YAAY,GAAG,UAAU,CAAC,iBAAiB,CAAC,YAAY,CAAC;IAC5E,CAAC;SAAM,CAAC;QACN,UAAU,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC;QAC3D,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC;QACjD,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;QACnD,UAAU,CAAC,KAAK,CAAC,YAAY,GAAG,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC;IACjE,CAAC;IACD,UAAU,CAAC,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC;IACtC,UAAU,CAAC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;IAClC,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;IACpC,UAAU,CAAC,OAAO,GAAG,GAAG,EAAE;QACxB,OAAO,CAAC,MAAM,EAAE,CAAC;QACjB,OAAO,CAAC,SAAS,EAAE,CAAC;IACtB,CAAC,CAAC;IACF,6CAA6C;IAC7C,UAAU,CAAC,GAAG,EAAE;QACd,UAAU,CAAC,KAAK,EAAE,CAAC;QACnB,4BAA4B;QAC5B,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;QAC5B,KAAK,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;QAC1B,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,UAAU,CAAC;IACrC,CAAC,EAAE,CAAC,CAAC,CAAC;IAEN,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;IAC9B,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;IAC/B,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAE1B,+CAA+C;IAC/C,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,EAAE;QAC1C,IAAI,CAAC,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;YACzB,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,OAAO,CAAC,QAAQ;gBAAE,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC3C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,sBAAsB;IACtB,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE;QACxC,IAAK,CAAmB,CAAC,GAAG,KAAK,QAAQ,EAAE,CAAC;YAC1C,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,IAAI,OAAO,CAAC,QAAQ;gBAAE,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC3C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC3B,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;IACnC,OAAO,CAAC,KAAK,EAAE,CAAC;AAClB,CAAC", "sourcesContent": ["// Reusable modal component for settings panel\nimport { defaultUIThemeConfig, UIThemeConfig } from '../themeConfig';\n\nlet currentTheme: UIThemeConfig = defaultUIThemeConfig;\n\nexport function setModalTheme(theme: UIThemeConfig) {\n  currentTheme = theme;\n}\n\nexport function showModal(options: { title: string; content: string; onConfirm: () => void; onCancel?: () => void; }) {\n  // Detect destructive action\n  const isDestructive = /delete|remove|permanently/i.test(options.title + options.content);\n  const modalTheme = currentTheme.modal;\n  // Extract warning for highlight\n  let contentHTML = options.content;\n  const warningMatch = /((cannot|can't) be undone\\.?)/i.exec(options.content);\n  if (warningMatch) {\n    contentHTML = options.content.replace(warningMatch[1], `<span style='color:#b91c1c;font-weight:600;display:inline-flex;align-items:center;gap:4px;'>⚠️ ${warningMatch[1]}</span>`);\n  }\n  // Remove any existing modal\n  const prev = document.getElementById('settings-global-modal');\n  if (prev) prev.remove();\n\n  // Modal overlay\n  const overlay = document.createElement('div');\n  overlay.id = 'settings-global-modal';\n  overlay.tabIndex = -1;\n  overlay.style.position = 'fixed';\n  overlay.style.top = '0';\n  overlay.style.left = '0';\n  overlay.style.width = '100vw';\n  overlay.style.height = '100vh';\n  overlay.style.background = 'rgba(0,0,0,0.32)'; // keep overlay semi-transparent\n  overlay.style.zIndex = '9999';\n  overlay.style.display = 'flex';\n  overlay.style.alignItems = 'center';\n  overlay.style.justifyContent = 'center';\n  overlay.style.opacity = '0';\n  overlay.style.transition = 'opacity 0.22s cubic-bezier(.4,0,.2,1)';\n\n  // Modal dialog\n  const modal = document.createElement('div');\n  modal.className = 'settings-modal-dialog';\n  modal.setAttribute('role', 'dialog');\n  modal.setAttribute('aria-modal', 'true');\n  modal.style.background = modalTheme.background;\n  modal.style.color = modalTheme.foreground;\n  modal.style.borderRadius = modalTheme.borderRadius;\n  modal.style.boxShadow = modalTheme.boxShadow;\n  modal.style.border = `2px solid ${modalTheme.border}`;\n  modal.style.padding = '38px 34px 28px 34px';\n  modal.style.minWidth = '340px';\n  modal.style.maxWidth = '520px';\n  modal.style.maxHeight = '90vh';\n  modal.style.overflowY = 'auto';\n  modal.style.position = 'relative';\n  modal.style.opacity = '0';\n  modal.style.transform = `scale(${modalTheme.animation.scale})`;\n  modal.style.transition = `opacity ${modalTheme.animation.duration} cubic-bezier(.4,0,.2,1), transform ${modalTheme.animation.duration} cubic-bezier(.4,0,.2,1)`;\n\n  // Modal header\n  const header = document.createElement('div');\n  header.style.fontSize = modalTheme.headerFontSize;\n  header.style.fontWeight = modalTheme.headerFontWeight;\n  header.style.marginBottom = '22px';\n  header.style.letterSpacing = '0.01em';\n  if (isDestructive) {\n    header.innerHTML = `<span style='display:inline-flex;align-items:center;gap:10px;'>${modalTheme.warningIcon} <span>${options.title}</span></span>`;\n  } else {\n    header.textContent = options.title;\n  }\n  modal.appendChild(header);\n\n  // Modal content\n  const contentDiv = document.createElement('div');\n  contentDiv.innerHTML = contentHTML;\n  contentDiv.style.marginBottom = '26px';\n  contentDiv.style.fontSize = modalTheme.contentFontSize;\n  modal.appendChild(contentDiv);\n\n  // Button row\n  const btnRow = document.createElement('div');\n  btnRow.style.display = 'flex';\n  btnRow.style.justifyContent = 'flex-end';\n  btnRow.style.gap = '18px';\n  btnRow.style.marginTop = '8px';\n\n  const cancelBtn = document.createElement('button');\n  cancelBtn.textContent = 'Cancel';\n  cancelBtn.className = 'modal-btn-cancel';\n  cancelBtn.style.background = modalTheme.button.background;\n  cancelBtn.style.color = modalTheme.button.color;\n  cancelBtn.style.border = modalTheme.button.border;\n  cancelBtn.style.borderRadius = modalTheme.button.borderRadius;\n  cancelBtn.style.padding = '7px 22px';\n  cancelBtn.style.fontSize = '1em';\n  cancelBtn.style.cursor = 'pointer';\n  cancelBtn.onclick = () => {\n    overlay.remove();\n    if (options.onCancel) options.onCancel();\n  };\n\n  const confirmBtn = document.createElement('button');\n  confirmBtn.textContent = isDestructive ? 'Delete' : 'Save';\n  confirmBtn.className = 'modal-btn-confirm';\n  if (isDestructive) {\n    confirmBtn.style.background = modalTheme.buttonDestructive.background;\n    confirmBtn.style.color = modalTheme.buttonDestructive.color;\n    confirmBtn.style.border = modalTheme.buttonDestructive.border;\n    confirmBtn.style.boxShadow = modalTheme.buttonDestructive.shadow;\n    confirmBtn.style.borderRadius = modalTheme.buttonDestructive.borderRadius;\n  } else {\n    confirmBtn.style.background = modalTheme.button.background;\n    confirmBtn.style.color = modalTheme.button.color;\n    confirmBtn.style.border = modalTheme.button.border;\n    confirmBtn.style.borderRadius = modalTheme.button.borderRadius;\n  }\n  confirmBtn.style.padding = '7px 22px';\n  confirmBtn.style.fontSize = '1em';\n  confirmBtn.style.cursor = 'pointer';\n  confirmBtn.onclick = () => {\n    overlay.remove();\n    options.onConfirm();\n  };\n  // Accessibility: focus confirmBtn by default\n  setTimeout(() => {\n    confirmBtn.focus();\n    // Animate overlay and modal\n    overlay.style.opacity = '1';\n    modal.style.opacity = '1';\n    modal.style.transform = 'scale(1)';\n  }, 0);\n\n  btnRow.appendChild(cancelBtn);\n  btnRow.appendChild(confirmBtn);\n  modal.appendChild(btnRow);\n\n  // Close on overlay click (but not modal click)\n  overlay.addEventListener('mousedown', (e) => {\n    if (e.target === overlay) {\n      overlay.remove();\n      if (options.onCancel) options.onCancel();\n    }\n  });\n\n  // Close on Escape key\n  overlay.addEventListener('keydown', (e) => {\n    if ((e as KeyboardEvent).key === 'Escape') {\n      overlay.remove();\n      if (options.onCancel) options.onCancel();\n    }\n  });\n\n  overlay.appendChild(modal);\n  document.body.appendChild(overlay);\n  overlay.focus();\n}\n\n"]}