import * as vscode from 'vscode';
import { OperationMode, ContextSource, ContextType } from './operationMode';
import { Agent } from '../agentUtilities/agent';
import { LLMGenerateParams } from '../../llm/types';
import { Logger } from '../../logger';
import { contextManager } from './contextManager';
import { promptManager } from '../../prompts/promptManager';
import { AgentContext } from '../../types/agent';

/**
 * Agent role definition
 */
interface AgentRole {
  id: string;
  name: string;
  description: string;
  systemPrompt: string;
}

/**
 * Agent instance in a multi-agent system
 */
interface AgentInstance {
  id: string;
  role: AgentRole;
  agent: Agent;
  messages: { role: string; content: string }[];
}

/**
 * Multi-Agent Mode - Team of agents with supervisor coordination
 */
export class MultiAgentMode extends OperationMode {
  readonly id = 'multi-agent';
  readonly displayName = 'Multi-Agent';
  readonly description = 'Team of AI agents working together on complex tasks';
  readonly icon = '$(organization)';
  readonly defaultContextType = ContextType.ENTIRE_CODEBASE;
  readonly requiresHumanVerification = false;
  readonly supportsMultipleAgents = true;

  private isRunning = false;
  private cancelTokenSource: vscode.CancellationTokenSource | undefined;
  private progressReporter: vscode.Progress<{ message?: string; increment?: number }> | undefined;
  public statusBarItem: vscode.StatusBarItem | undefined = undefined;

  private agents: Map<string, AgentInstance> = new Map();
  private supervisorAgent: Agent | undefined;
  private conversationLog: { agent: string; message: string; timestamp: Date }[] = [];

  // Predefined agent roles
  private readonly predefinedRoles: AgentRole[] = [
    {
      id: 'supervisor',
      name: 'Supervisor',
      description: 'Coordinates the team and ensures the task is completed effectively',
      systemPrompt: promptManager.renderPrompt('agent.multiAgent.supervisor', {})
    },
    {
      id: 'architect',
      name: 'Architect',
      description: 'Designs the overall structure and architecture of the solution',
      systemPrompt: promptManager.renderPrompt('agent.multiAgent.architect', {})
    },
    {
      id: 'developer',
      name: 'Developer',
      description: 'Implements the solution based on the architect\'s design',
      systemPrompt: promptManager.renderPrompt('agent.multiAgent.developer', {})
    },
    {
      id: 'tester',
      name: 'Tester',
      description: 'Tests the solution to ensure it meets requirements and is free of bugs',
      systemPrompt: promptManager.renderPrompt('agent.multiAgent.tester', {})
    },
    {
      id: 'reviewer',
      name: 'Reviewer',
      description: 'Reviews code and provides feedback to improve quality',
      systemPrompt: promptManager.renderPrompt('agent.multiAgent.reviewer', {})
    }
  ];

  /**
     * Initialize the Multi-Agent mode
     */
  async initialize(context: vscode.ExtensionContext): Promise<void> {


    // Create status bar item
    this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Left, 100);
    this.statusBarItem.text = '$(organization) Multi-Agent: Idle';
    this.statusBarItem.tooltip = 'Codessa Multi-Agent Mode';
    this.statusBarItem.command = 'codessa.toggleMultiAgentMode';
    context.subscriptions.push(this.statusBarItem);
  }

  /**
     * Process a user message in Multi-Agent mode
     */
  async processMessage(
    message: string,
    agent: Agent,
    contextSource: ContextSource
  ): Promise<string> {
    try {
      Logger.instance.info(`Processing message in Multi-Agent mode: ${message}`);

      // If agents are not set up, create them
      if (this.agents.size === 0) {
        await this.setupAgents(agent);
      }

      // Start the multi-agent system
      await this.startMultiAgentSystem(message, contextSource);

      return 'Multi-Agent system started. The team will work on this task collaboratively and report back when finished.';
    } catch (error) {
      Logger.instance.error('Error processing message in Multi-Agent mode:', error);
      return `Error starting multi-agent system: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
     * Set up agents for the multi-agent system
     */
  private async setupAgents(baseAgent: Agent): Promise<void> {
    // Clear existing agents
    this.agents.clear();

    // Create instances for each role
    for (const role of this.predefinedRoles) {
      // Clone the base agent
      const agentInstance: AgentInstance = {
        id: `agent-${role.id}`,
        role,
        agent: baseAgent, // In a real implementation, you would create separate agent instances
        messages: []
      };

      this.agents.set(role.id, agentInstance);
    }

    // Set supervisor agent
    this.supervisorAgent = baseAgent;
  }

  /**
     * Start the multi-agent system
     */
  private async startMultiAgentSystem(
    task: string,
    contextSource: ContextSource
  ): Promise<void> {
    if (this.isRunning) {
      throw new Error('Multi-Agent system is already running. Please wait for it to complete or cancel it.');
    }

    this.isRunning = true;
    this.cancelTokenSource = new vscode.CancellationTokenSource();
    const token: AgentContext = { cancellationToken: this.cancelTokenSource.token };

    // Update status bar
    if (this.statusBarItem) {
      this.statusBarItem.text = '$(organization) Multi-Agent: Running';
      this.statusBarItem.show();
    }

    // Start progress indicator
    vscode.window.withProgress(
      {
        location: vscode.ProgressLocation.Notification,
        title: 'Codessa Multi-Agent System',
        cancellable: true
      },
      async (progress, progressToken) => {
        this.progressReporter = progress;

        // Handle cancellation
        progressToken.onCancellationRequested(() => {
          this.cancelTokenSource?.cancel();
          this.stopMultiAgentSystem('User cancelled the operation');
        });

        progress.report({ message: 'Starting multi-agent system...' });

        try {
          // Get context content
          const contextContent = await contextManager.getContextContent(contextSource);

          // Add memory context if available
          let memoryContext = '';
          try {
            if (this.supervisorAgent) {
              const agentMemory = this.supervisorAgent.getMemory();
              if (agentMemory) {
                const relevantMemories = await agentMemory.getRelevantMemories(task);
                if (relevantMemories && relevantMemories.length > 0) {
                  memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);
                  Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to multi-agent context`);
                }
              }
            }
          } catch (memoryError) {
            Logger.instance.warn('Failed to retrieve memory context for multi-agent:', memoryError);
            // Continue without memory context
          }

          // Clear conversation log
          this.conversationLog = [];

          // Initialize the task with the supervisor
          await this.runSupervisorPhase(task, contextContent, memoryContext, progress, token);

          // Complete the task
          this.stopMultiAgentSystem('Task completed successfully');

          // Show completion notification
          vscode.window.showInformationMessage('Multi-Agent system has completed the task successfully.');

          // Create a report document
          await this.createReport();
        } catch (error) {
          Logger.instance.error('Error in multi-agent execution:', error);
          this.stopMultiAgentSystem(`Error: ${error instanceof Error ? error.message : String(error)}`);

          // Show error notification
          vscode.window.showErrorMessage(`Multi-Agent system encountered an error: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
    );
  }

  /**
     * Run the supervisor phase of the multi-agent system
     */
  private async runSupervisorPhase(
    task: string,
    contextContent: string,
    memoryContext: string,
    progress: vscode.Progress<{ message?: string; increment?: number }>,
    token: AgentContext
  ): Promise<void> {
    if (!this.supervisorAgent) {
      throw new Error('Supervisor agent not initialized');
    }

    // Maximum number of iterations
    const MAX_ITERATIONS = 10;
    let currentIteration = 0;

    // Initial prompt for the supervisor
    const supervisorPrompt = `
You are the supervisor of a multi-agent team working on the following task:

${task}

Here is the context you need:

${contextContent}

${memoryContext}

Your team consists of the following agents:
${Array.from(this.agents.values()).map(agent => `- ${agent.role.name}: ${agent.role.description}`).join('\n')}

As the supervisor, you need to:
1. Analyze the task and break it down into subtasks
2. Assign each subtask to the appropriate team member
3. Coordinate the work of the team
4. Integrate the results into a final solution

Please start by analyzing the task and creating a plan.
`;

    // Log supervisor prompt
    this.logMessage('supervisor', 'system', supervisorPrompt);

    // Generate initial plan
    progress.report({ message: 'Supervisor is analyzing the task and creating a plan...' });

    const planResponse = await this.supervisorAgent.generate(
      supervisorPrompt,
      this.getLLMParams(),
      token
    );

    // Log supervisor response
    this.logMessage('supervisor', 'assistant', planResponse);

    // Execute the multi-agent workflow
    while (currentIteration < MAX_ITERATIONS && !token.cancellationToken?.isCancellationRequested) {
      currentIteration++;

      // Report progress
      progress.report({
        message: `Iteration ${currentIteration}: Executing multi-agent workflow...`,
        increment: 100 / MAX_ITERATIONS
      });

      // For each agent (except supervisor), generate a response
      for (const [roleId, agentInstance] of this.agents.entries()) {
        if (roleId === 'supervisor') continue;

        // Generate prompt for this agent
        const agentPrompt = `
You are the ${agentInstance.role.name} in a multi-agent team working on the following task:

${task}

The supervisor has created the following plan:

${planResponse}

Your specific role is: ${agentInstance.role.description}

Based on the plan, what actions will you take for this iteration?
`;

        // Log agent prompt
        this.logMessage(roleId, 'system', agentPrompt);

        // Generate agent response
        progress.report({ message: `${agentInstance.role.name} is working...` });

        const agentResponse = await agentInstance.agent.generate(
          agentPrompt,
          this.getLLMParams(),
          token
        );

        // Log agent response
        this.logMessage(roleId, 'assistant', agentResponse);

        // Small delay to prevent rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Generate supervisor coordination message
      const coordinationPrompt = `
You are the supervisor of the multi-agent team. Here's the current state of the project:

Task: ${task}

Your initial plan:
${planResponse}

Team member updates:
${Array.from(this.agents.entries())
          .filter(([id]) => id !== 'supervisor')
          .map(([id, instance]) => {
            const lastMessage = this.getLastMessageForAgent(id);
            return `${instance.role.name}: ${lastMessage || 'No update yet'}`;
          })
          .join('\n\n')}

Based on these updates, provide coordination and guidance for the next iteration.
If the task is complete, indicate that clearly.
`;

      // Log coordination prompt
      this.logMessage('supervisor', 'system', coordinationPrompt);

      // Generate supervisor response
      progress.report({ message: 'Supervisor is coordinating the team...' });

      const coordinationResponse = await this.supervisorAgent.generate(
        coordinationPrompt,
        this.getLLMParams(),
        token
      );

      // Log supervisor response
      this.logMessage('supervisor', 'assistant', coordinationResponse);

      // Check if task is complete
      if (coordinationResponse && (
        coordinationResponse.toLowerCase().includes('task complete') ||
        coordinationResponse.toLowerCase().includes('task is complete')
      )) {
        break;
      }

      // Small delay to prevent rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Final summary from supervisor
    const summaryPrompt = `
You are the supervisor of the multi-agent team. The task is now complete.

Task: ${task}

Please provide a comprehensive summary of what the team has accomplished, including:
1. The original task and objectives
2. The approach taken by the team
3. The contributions of each team member
4. The final solution or outcome
5. Any challenges faced and how they were overcome
6. Recommendations for future work or improvements

This summary will be included in the final report.
`;

    // Log summary prompt
    this.logMessage('supervisor', 'system', summaryPrompt);

    // Generate summary
    progress.report({ message: 'Generating final summary...' });

    const summaryResponse = await this.supervisorAgent.generate(
      summaryPrompt,
      this.getLLMParams(),
      token
    );

    // Log summary response
    this.logMessage('supervisor', 'assistant', summaryResponse);
  }

  /**
     * Stop the multi-agent system
     */
  private stopMultiAgentSystem(reason: string): void {
    this.isRunning = false;
    this.cancelTokenSource?.dispose();
    this.cancelTokenSource = undefined;

    // Update status bar
    if (this.statusBarItem) {
      this.statusBarItem.text = '$(organization) Multi-Agent: Idle';
    }

    // Log reason
    Logger.instance.info(`Multi-Agent system stopped: ${reason}`);
  }

  /**
     * Log a message in the conversation log
     */
  private logMessage(agentId: string, role: string, content: string): void {
    this.conversationLog.push({
      agent: agentId,
      message: `[${role}] ${content}`,
      timestamp: new Date()
    });
  }

  /**
     * Get the last message for a specific agent
     */
  private getLastMessageForAgent(agentId: string): string | undefined {
    // Filter messages for this agent and get the last one
    const agentMessages = this.conversationLog
      .filter(log => log.agent === agentId && log.message.startsWith('[assistant]'));

    if (agentMessages.length === 0) {
      return undefined;
    }

    const lastMessage = agentMessages[agentMessages.length - 1];
    return lastMessage.message.replace('[assistant] ', '');
  }

  /**
     * Create a report document
     */
  private async createReport(): Promise<void> {
    // Generate report content
    let reportContent = '# Multi-Agent Task Report\n\n';

    // Add timestamp
    reportContent += `Generated on: ${new Date().toLocaleString()}\n\n`;

    // Add team members
    reportContent += '## Team Members\n\n';
    for (const [, agentInstance] of this.agents.entries()) {
      reportContent += `### ${agentInstance.role.name}\n`;
      reportContent += `${agentInstance.role.description}\n\n`;
    }

    // Add conversation log
    reportContent += '## Conversation Log\n\n';
    for (const log of this.conversationLog) {
      if (log.message.startsWith('[assistant]')) {
        const agentName = this.agents.get(log.agent)?.role.name || log.agent;
        reportContent += `### ${agentName} (${log.timestamp.toLocaleTimeString()})\n\n`;
        reportContent += `${log.message.replace('[assistant] ', '')}\n\n`;
      }
    }

    // Create a document
    const document = await vscode.workspace.openTextDocument({
      content: reportContent,
      language: 'markdown'
    });

    await vscode.window.showTextDocument(document);
  }

  /**
     * Get LLM parameters specific to Multi-Agent mode
     */
  getLLMParams(): LLMGenerateParams {
    return {
      prompt: '',
      modelId: '',
      temperature: 0.5, // Balanced temperature for creativity and precision
      maxTokens: 2000,   // Longer responses for detailed reasoning
      mode: 'task'
    };
  }

  /**
     * Get the system prompt for Multi-Agent mode
     */
  async getSystemPrompt(): Promise<string> {
    return promptManager.renderPrompt('mode.multiAgent', {});
  }

  /**
     * Get UI components specific to Multi-Agent mode
     */
  getUIComponents(): {
    controlPanel?: string;
    contextPanel?: string;
    messageInput?: string;
  } {
    return {
      controlPanel: `
<div class="multi-agent-control-panel">
    <div class="multi-agent-status">
        <span id="multi-agent-status-indicator" class="status-indicator"></span>
        <span id="multi-agent-status-text">Idle</span>
    </div>
    <div class="multi-agent-controls">
        <button id="btn-start-multi-agent" title="Start Multi-Agent System"><i class="codicon codicon-play"></i> Start</button>
        <button id="btn-stop-multi-agent" title="Stop Multi-Agent System" disabled><i class="codicon codicon-stop"></i> Stop</button>
    </div>
    <div class="multi-agent-team">
        <h4>Team Configuration</h4>
        <div id="multi-agent-team-members" class="team-members-list">
            <!-- Team members will be added here dynamically -->
        </div>
        <button id="btn-add-agent" title="Add Agent"><i class="codicon codicon-add"></i> Add Agent</button>
    </div>
</div>
`,
      contextPanel: `
<div class="context-panel">
    <div class="context-header">
        <h3>Multi-Agent Context</h3>
        <div class="context-controls">
            <button id="btn-refresh-context" title="Refresh Context"><i class="codicon codicon-refresh"></i></button>
            <button id="btn-select-files" title="Select Files"><i class="codicon codicon-file-code"></i></button>
            <button id="btn-select-folders" title="Select Folders"><i class="codicon codicon-folder"></i></button>
        </div>
    </div>
    <div class="context-type">
        <select id="context-type-selector">
            <option value="entire_codebase">Entire Codebase</option>
            <option value="selected_files">Selected Files</option>
            <option value="current_file">Current File</option>
            <option value="custom">Custom</option>
        </select>
    </div>
    <div id="context-files-list" class="context-files-list"></div>
</div>
`,
      messageInput: `
<div class="message-input-container">
    <textarea id="message-input" placeholder="Describe the task for the multi-agent team to complete..."></textarea>
    <button id="btn-send" title="Send"><i class="codicon codicon-send"></i></button>
</div>
`
    };
  }

  /**
     * Handle mode-specific commands
     */
  async handleCommand(command: string, args: unknown[]): Promise<void> {
    switch (command) {
      case 'startMultiAgentSystem':
        if (!this.isRunning && args.length >= 3) {
          const [task, agent, contextSource] = args;
          await this.setupAgents(agent as Agent);
          await this.startMultiAgentSystem(task as string, contextSource as ContextSource);
        }
        break;

      case 'stopMultiAgentSystem':
        if (this.isRunning) {
          this.cancelTokenSource?.cancel();
          this.stopMultiAgentSystem('User manually stopped the multi-agent system');
        }
        break;

      case 'addAgentRole':
        if (args.length >= 1) {
          const role = args[0] as AgentRole;
          this.predefinedRoles.push(role);
        }
        break;
    }
  }
}
