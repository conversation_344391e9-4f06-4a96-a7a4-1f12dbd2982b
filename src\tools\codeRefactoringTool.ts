import * as vscode from 'vscode';
import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../agents/agentUtilities/agent';
import * as cp from 'child_process';
import { z } from 'zod';
import * as path from 'path';
import * as fs from 'fs';
import { llmService } from '../llm/llmService';
import { LLMConfig } from '../config';

/**
 * Detects code smells and suggests refactoring opportunities
 */
export class CodeSmellDetectorTool implements ITool {
  readonly id = 'codeSmellDetector';
  readonly name = 'Code Smell Detector';
  readonly description = 'Detects code smells and suggests refactoring opportunities.';
  readonly type = 'single-action';
  readonly actions: Record<string, any> = {};
  readonly singleActionSchema = z.object({
    filePath: z.string().describe('Path to the file to analyze.'),
    smellTypes: z.array(z.enum([
      'long-method',
      'large-class',
      'primitive-obsession',
      'long-parameter-list',
      'duplicate-code',
      'feature-envy',
      'shotgun-surgery',
      'switch-statements',
      'all'
    ])).optional().describe('Types of code smells to check for. Default is all.')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      filePath: { type: 'string', description: 'Path to the file to analyze.' },
      smellTypes: {
        type: 'array',
        items: {
          type: 'string',
          enum: [
            'long-method',
            'large-class',
            'primitive-obsession',
            'long-parameter-list',
            'duplicate-code',
            'feature-envy',
            'shotgun-surgery',
            'switch-statements',
            'all'
          ]
        },
        description: 'Types of code smells to check for. Default is all.'
      }
    },
    required: ['filePath']
  };

  private llmConfig: LLMConfig = {
    provider: 'openai',
    modelId: 'gpt-3.5-turbo',
    options: { temperature: 0.1 }
  };

  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    try {
      const filePath = input.filePath as string;
      const smellTypes = (input.smellTypes as string[]) || ['all'];

      if (!filePath) {
        return {
          success: false,
          error: '\'filePath\' is required.',
          toolId: this.id,
          actionName
        };
      }

      // Validate file exists
      try {
        const uri = vscode.Uri.file(filePath);
        // Replace Promise.then().catch() with try/catch for better error handling
        let exists = false;
        try {
          await vscode.workspace.fs.stat(uri);
          exists = true;
        } catch {
          exists = false;
        }

        if (!exists) {
          return {
            success: false,
            error: `File not found: ${filePath}`,
            toolId: this.id,
            actionName
          };
        }
      } catch (error) {
        return {
          success: false,
          error: `Error accessing file: ${error}`,
          toolId: this.id,
          actionName
        };
      }

      // Get file content
      const document = await vscode.workspace.openTextDocument(filePath);
      const text = document.getText();
      const fileExtension = path.extname(filePath).toLowerCase();
      const language = document.languageId;

      // Perform pattern-based code smell detection
      const patternResults = this.detectCodeSmellsByPattern(text, fileExtension, smellTypes);

      // Use LLM for deeper analysis if available
      let llmResults: any = { available: false };
      try {
        const provider = await llmService.getProviderForConfig(this.llmConfig);
        if (provider) {
          llmResults = await this.detectCodeSmellsWithLLM(text, language, smellTypes, provider);
        }
      } catch (error) {
        llmResults = {
          available: false,
          error: `LLM analysis failed: ${error}`
        };
      }

      // Combine results
      const combinedResults = this.combineResults(patternResults, llmResults);

      // Generate recommendations
      const recommendations = this.generateRecommendations(combinedResults);

      return {
        success: true,
        output: {
          codeSmells: combinedResults,
          recommendations,
          summary: {
            totalSmells: this.countTotalSmells(combinedResults),
            severityBreakdown: this.getSeverityBreakdown(combinedResults),
            fileInfo: {
              path: filePath,
              language,
              size: text.length,
              lines: text.split('\n').length
            }
          }
        },
        toolId: this.id,
        actionName,
        metadata: {
          filePath,
          smellTypes: smellTypes.includes('all') ?
            ['long-method', 'large-class', 'primitive-obsession', 'long-parameter-list',
              'duplicate-code', 'feature-envy', 'shotgun-surgery', 'switch-statements'] :
            smellTypes
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: `Code smell detection failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  private detectCodeSmellsByPattern(code: string, fileExtension: string, types: string[]): any {
    const results: Record<string, any[]> = {
      'long-method': [],
      'large-class': [],
      'primitive-obsession': [],
      'long-parameter-list': [],
      'duplicate-code': [],
      'feature-envy': [],
      'shotgun-surgery': [],
      'switch-statements': []
    };

    const checkAll = types.includes('all');
    const lines = code.split('\n');

    // Long Method detection
    if (checkAll || types.includes('long-method')) {
      // Detect methods/functions based on language
      const methodPatterns: Record<string, RegExp> = {
        '.js': /function\s+(\w+)\s*\([^)]*\)\s*\{|\w+\s*=\s*function\s*\([^)]*\)\s*\{|(\w+)\s*\([^)]*\)\s*\{|(\w+)\s*=\s*\([^)]*\)\s*=>/,
        '.ts': /function\s+(\w+)\s*\([^)]*\)\s*\{|\w+\s*=\s*function\s*\([^)]*\)\s*\{|(\w+)\s*\([^)]*\)\s*\{|(\w+)\s*=\s*\([^)]*\)\s*=>|(\w+)\s*:\s*\([^)]*\)\s*=>/,
        '.py': /def\s+(\w+)\s*\([^)]*\)\s*:/,
        '.java': /(?:public|private|protected|static)?\s*\w+\s+(\w+)\s*\([^)]*\)\s*\{/,
        '.cs': /(?:public|private|protected|internal|static)?\s*\w+\s+(\w+)\s*\([^)]*\)\s*\{/,
        '.php': /function\s+(\w+)\s*\([^)]*\)\s*\{/,
        '.rb': /def\s+(\w+)/,
        '.go': /func\s+(\w+)\s*\([^)]*\)\s*\{/,
        '.rs': /fn\s+(\w+)\s*\([^)]*\)\s*\{/
      };

      const pattern = methodPatterns[fileExtension] || methodPatterns['.js'];

      // Find method boundaries
      let methodStart = -1;
      let methodName = '';
      let braceCount = 0;
      let inMethod = false;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        // Check for method start
        if (!inMethod) {
          const match = line.match(pattern);
          if (match) {
            methodStart = i;
            methodName = match[1] || match[2] || match[3] || match[4] || 'anonymous';
            inMethod = true;
            braceCount = (line.match(/\{/g) || []).length - (line.match(/\}/g) || []).length;

            // For Python, we don't use braces
            if (fileExtension === '.py') {
              // Find the end of the method by indentation
              let j = i + 1;
              // Add null check for regex match operation
              const indentMatch = line.match(/^\s*/);
              const baseIndent = indentMatch ? indentMatch[0].length : 0;

              while (j < lines.length) {
                const nextLine = lines[j];
                // Add null check for regex match operation
                const nextIndentMatch = nextLine.match(/^\s*/);
                const nextIndent = nextIndentMatch ? nextIndentMatch[0].length : 0;

                // If we find a line with same or less indentation, we've exited the method
                if (nextLine.trim() !== '' && nextIndent <= baseIndent) {
                  break;
                }

                j++;
              }

              const methodLength = j - methodStart;

              // Consider methods with more than 20 lines as "long"
              if (methodLength > 20) {
                results['long-method'].push({
                  name: methodName,
                  startLine: methodStart + 1, // 1-based line numbers
                  endLine: j,
                  length: methodLength,
                  severity: methodLength > 50 ? 'high' : methodLength > 30 ? 'medium' : 'low',
                  description: `Method '${methodName}' is ${methodLength} lines long, which exceeds the recommended maximum of 20 lines.`
                });
              }

              inMethod = false;
            }
          }
        } else if (fileExtension !== '.py') {
          // Update brace count for non-Python languages
          braceCount += (line.match(/\{/g) || []).length - (line.match(/\}/g) || []).length;

          // Check if method has ended
          if (braceCount === 0) {
            const methodLength = i - methodStart + 1;

            // Consider methods with more than 20 lines as "long"
            if (methodLength > 20) {
              results['long-method'].push({
                name: methodName,
                startLine: methodStart + 1, // 1-based line numbers
                endLine: i + 1,
                length: methodLength,
                severity: methodLength > 50 ? 'high' : methodLength > 30 ? 'medium' : 'low',
                description: `Method '${methodName}' is ${methodLength} lines long, which exceeds the recommended maximum of 20 lines.`
              });
            }

            inMethod = false;
          }
        }
      }
    }

    // Large Class detection
    if (checkAll || types.includes('large-class')) {
      // Detect classes based on language
      const classPatterns: Record<string, RegExp> = {
        '.js': /class\s+(\w+)/,
        '.ts': /class\s+(\w+)/,
        '.py': /class\s+(\w+)/,
        '.java': /class\s+(\w+)/,
        '.cs': /class\s+(\w+)/,
        '.php': /class\s+(\w+)/,
        '.rb': /class\s+(\w+)/,
        '.go': /type\s+(\w+)\s+struct/,
        '.rs': /struct\s+(\w+)/
      };

      const pattern = classPatterns[fileExtension] || classPatterns['.js'];

      // Find class boundaries
      let classStart = -1;
      let className = '';
      let braceCount = 0;
      let inClass = false;
      let methodCount = 0;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        // Check for class start
        if (!inClass) {
          const match = line.match(pattern);
          if (match) {
            classStart = i;
            className = match[1];
            inClass = true;
            braceCount = (line.match(/\{/g) || []).length - (line.match(/\}/g) || []).length;
            methodCount = 0;

            // For Python, we don't use braces
            if (fileExtension === '.py') {
              // Find the end of the class by indentation
              let j = i + 1;
              // Add null check for regex match operation
              const indentMatch = line.match(/^\s*/);
              const baseIndent = indentMatch ? indentMatch[0].length : 0;

              while (j < lines.length) {
                const nextLine = lines[j];
                // Add null check for regex match operation
                const nextIndentMatch = nextLine.match(/^\s*/);
                const nextIndent = nextIndentMatch ? nextIndentMatch[0].length : 0;

                // Count methods
                if (nextLine.match(/^\s*def\s+\w+/)) {
                  methodCount++;
                }

                // If we find a line with same or less indentation, we've exited the class
                if (nextLine.trim() !== '' && nextIndent <= baseIndent) {
                  break;
                }

                j++;
              }

              const classLength = j - classStart;

              // Consider classes with more than 200 lines or 10 methods as "large"
              if (classLength > 200 || methodCount > 10) {
                results['large-class'].push({
                  name: className,
                  startLine: classStart + 1, // 1-based line numbers
                  endLine: j,
                  length: classLength,
                  methods: methodCount,
                  severity: classLength > 500 || methodCount > 20 ? 'high' : classLength > 300 || methodCount > 15 ? 'medium' : 'low',
                  description: `Class '${className}' is ${classLength} lines long with ${methodCount} methods, which may indicate it has too many responsibilities.`
                });
              }

              inClass = false;
            }
          }
        } else if (fileExtension !== '.py') {
          // Update brace count for non-Python languages
          braceCount += (line.match(/\{/g) || []).length - (line.match(/\}/g) || []).length;

          // Count methods
          if (line.match(/\w+\s*\([^)]*\)\s*\{/) || line.match(/function\s+\w+\s*\(/)) {
            methodCount++;
          }

          // Check if class has ended
          if (braceCount === 0) {
            const classLength = i - classStart + 1;

            // Consider classes with more than 200 lines or 10 methods as "large"
            if (classLength > 200 || methodCount > 10) {
              results['large-class'].push({
                name: className,
                startLine: classStart + 1, // 1-based line numbers
                endLine: i + 1,
                length: classLength,
                methods: methodCount,
                severity: classLength > 500 || methodCount > 20 ? 'high' : classLength > 300 || methodCount > 15 ? 'medium' : 'low',
                description: `Class '${className}' is ${classLength} lines long with ${methodCount} methods, which may indicate it has too many responsibilities.`
              });
            }

            inClass = false;
          }
        }
      }
    }

    // Long Parameter List detection
    if (checkAll || types.includes('long-parameter-list')) {
      // Detect methods with long parameter lists
      const paramPatterns: Record<string, RegExp> = {
        '.js': /function\s+\w+\s*\(([^)]*)\)|\w+\s*=\s*function\s*\(([^)]*)\)|\w+\s*\(([^)]*)\)\s*\{|\w+\s*=\s*\(([^)]*)\)\s*=>/,
        '.ts': /function\s+\w+\s*\(([^)]*)\)|\w+\s*=\s*function\s*\(([^)]*)\)|\w+\s*\(([^)]*)\)\s*\{|\w+\s*=\s*\(([^)]*)\)\s*=>|\w+\s*:\s*\(([^)]*)\)\s*=>/,
        '.py': /def\s+\w+\s*\(([^)]*)\)/,
        '.java': /(?:public|private|protected|static)?\s*\w+\s+\w+\s*\(([^)]*)\)/,
        '.cs': /(?:public|private|protected|internal|static)?\s*\w+\s+\w+\s*\(([^)]*)\)/,
        '.php': /function\s+\w+\s*\(([^)]*)\)/,
        '.rb': /def\s+\w+(?:\(([^)]*)\))?/,
        '.go': /func\s+\w+\s*\(([^)]*)\)/,
        '.rs': /fn\s+\w+\s*\(([^)]*)\)/
      };

      const pattern = paramPatterns[fileExtension] || paramPatterns['.js'];

      // Find methods with long parameter lists
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const match = line.match(pattern);

        if (match) {
          const params = match[1] || match[2] || match[3] || match[4] || match[5] || '';
          const paramCount = params.split(',').filter(p => p.trim() !== '').length;

          // Consider methods with more than 4 parameters as having "long parameter lists"
          if (paramCount > 4) {
            const methodMatch = line.match(/\b(\w+)\s*\(/);
            const methodName = methodMatch ? methodMatch[1] : 'anonymous';

            results['long-parameter-list'].push({
              name: methodName,
              line: i + 1, // 1-based line numbers
              parameterCount: paramCount,
              parameters: params,
              severity: paramCount > 7 ? 'high' : paramCount > 5 ? 'medium' : 'low',
              description: `Method '${methodName}' has ${paramCount} parameters, which exceeds the recommended maximum of 4 parameters.`
            });
          }
        }
      }
    }

    // Switch Statement detection
    if (checkAll || types.includes('switch-statements')) {
      // Detect switch statements
      const switchPattern = /switch\s*\([^)]*\)\s*\{/g;
      let match;

      while ((match = switchPattern.exec(code)) !== null) {
        const lineNumber = code.substring(0, match.index).split('\n').length;

        // Find the end of the switch statement
        const switchStart = match.index;
        let braceCount = 1;
        let pos = switchStart + match[0].length;

        while (braceCount > 0 && pos < code.length) {
          if (code[pos] === '{') braceCount++;
          if (code[pos] === '}') braceCount--;
          pos++;
        }

        const switchCode = code.substring(switchStart, pos);
        const caseCount = (switchCode.match(/case\s+/g) || []).length;

        // Consider switch statements with more than 5 cases as potential code smells
        if (caseCount > 5) {
          results['switch-statements'].push({
            line: lineNumber,
            caseCount,
            severity: caseCount > 10 ? 'high' : caseCount > 7 ? 'medium' : 'low',
            description: `Switch statement with ${caseCount} cases found. Consider using polymorphism or the strategy pattern instead.`
          });
        }
      }
    }

    // Primitive Obsession detection (simplified)
    if (checkAll || types.includes('primitive-obsession')) {
      // Look for patterns that might indicate primitive obsession
      const primitiveTypes = ['string', 'int', 'float', 'double', 'boolean', 'bool', 'char', 'byte', 'short', 'long'];

      // Check for repeated primitive type usage in parameters
      for (const type of primitiveTypes) {
        const typePattern = new RegExp(`\\b${type}\\b`, 'g');
        // Convert matchAll() iterator to array using Array.from() to avoid downlevelIteration issues
        const matches = Array.from(code.matchAll(typePattern));

        if (matches.length > 10) {
          results['primitive-obsession'].push({
            primitiveType: type,
            occurrences: matches.length,
            severity: matches.length > 20 ? 'high' : matches.length > 15 ? 'medium' : 'low',
            description: `Excessive use of primitive type '${type}' (${matches.length} occurrences). Consider creating domain-specific types.`
          });
        }
      }
    }

    return results;
  }

  private async detectCodeSmellsWithLLM(code: string, language: string, types: string[], provider: any): Promise<any> {
    const checkAll = types.includes('all');
    const typesToCheck = checkAll ?
      ['long-method', 'large-class', 'primitive-obsession', 'long-parameter-list',
        'duplicate-code', 'feature-envy', 'shotgun-surgery', 'switch-statements'] :
      types;

    // Prepare prompt for LLM
    const prompt = `
Analyze the following ${language} code for code smells, specifically focusing on: ${typesToCheck.join(', ')}.
For each code smell found, provide:
1. The type of code smell
2. The location (line number or method/class name)
3. The severity (high, medium, low)
4. A brief description of the issue
5. A recommendation for refactoring

Code to analyze:
\`\`\`${language}
${code}
\`\`\`

Format your response as JSON with the following structure:
{
  "codeSmells": {
    "long-method": [
      {
        "name": "methodName",
        "startLine": number,
        "endLine": number,
        "length": number,
        "severity": "high|medium|low",
        "description": "string",
        "recommendation": "string"
      }
    ],
    "large-class": [...],
    "primitive-obsession": [...],
    "long-parameter-list": [...],
    "duplicate-code": [...],
    "feature-envy": [...],
    "shotgun-surgery": [...],
    "switch-statements": [...]
  }
}
`;

    const result = await provider.generate({
      prompt,
      systemPrompt: 'You are an expert software engineer specializing in code quality and refactoring. Analyze the provided code for code smells with high precision.',
      modelId: this.llmConfig.modelId,
      options: this.llmConfig.options
    });

    if (result.error) {
      return { available: false, error: result.error };
    }

    try {
      // Try to parse the LLM response as JSON
      const jsonMatch = result.content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsedResult = JSON.parse(jsonMatch[0]);
        return {
          available: true,
          codeSmells: parsedResult.codeSmells || {}
        };
      } else {
        return {
          available: false,
          error: 'LLM response could not be parsed as JSON'
        };
      }
    } catch (error) {
      return {
        available: false,
        error: `Failed to parse LLM response: ${error}`
      };
    }
  }

  private combineResults(patternResults: Record<string, any[]>, llmResults: any): Record<string, any[]> {
    const combined: Record<string, any[]> = { ...patternResults };

    // If LLM results are available, merge them with pattern results
    if (llmResults.available && llmResults.codeSmells) {
      for (const [type, smells] of Object.entries(llmResults.codeSmells)) {
        if (!combined[type]) {
          combined[type] = [];
        }

        // Add LLM-detected code smells, avoiding duplicates
        for (const smell of smells as any[]) {
          // Check if this code smell is already detected by pattern matching
          let isDuplicate = false;

          if (type === 'long-method' || type === 'large-class') {
            isDuplicate = combined[type].some(existing =>
              existing.name === smell.name &&
              Math.abs(existing.startLine - smell.startLine) < 5
            );
          } else if (type === 'long-parameter-list') {
            isDuplicate = combined[type].some(existing =>
              existing.name === smell.name &&
              existing.line === smell.line
            );
          } else if (type === 'switch-statements') {
            isDuplicate = combined[type].some(existing =>
              Math.abs(existing.line - smell.line) < 5
            );
          } else if (type === 'primitive-obsession') {
            isDuplicate = combined[type].some(existing =>
              existing.primitiveType === smell.primitiveType
            );
          } else {
            isDuplicate = combined[type].some(existing =>
              JSON.stringify(existing) === JSON.stringify(smell)
            );
          }

          if (!isDuplicate) {
            combined[type].push({
              ...smell,
              source: 'llm' // Mark as coming from LLM
            });
          }
        }
      }
    }

    return combined;
  }

  private generateRecommendations(results: Record<string, any[]>): Record<string, string[]> {
    const recommendations: Record<string, string[]> = {};

    // Long Method recommendations
    if (results['long-method'] && results['long-method'].length > 0) {
      recommendations['long-method'] = [
        'Extract smaller, focused methods from long methods.',
        'Use the Extract Method refactoring pattern to break down complex methods.',
        'Consider using the Single Responsibility Principle: each method should do only one thing.',
        'Look for comment blocks within methods - these often indicate where you can extract a new method.'
      ];
    }

    // Large Class recommendations
    if (results['large-class'] && results['large-class'].length > 0) {
      recommendations['large-class'] = [
        'Split large classes into smaller, more focused classes.',
        'Use the Extract Class refactoring pattern to move related fields and methods to a new class.',
        'Consider using composition instead of creating monolithic classes.',
        'Apply the Single Responsibility Principle at the class level.'
      ];
    }

    // Primitive Obsession recommendations
    if (results['primitive-obsession'] && results['primitive-obsession'].length > 0) {
      recommendations['primitive-obsession'] = [
        'Create small, specialized classes to replace primitive values.',
        'Use the Replace Data Value with Object refactoring pattern.',
        'Consider using enums or sealed classes for values with a fixed set of options.',
        'Look for primitive values that have special meaning or validation requirements.'
      ];
    }

    // Long Parameter List recommendations
    if (results['long-parameter-list'] && results['long-parameter-list'].length > 0) {
      recommendations['long-parameter-list'] = [
        'Create parameter objects to group related parameters.',
        'Use the Introduce Parameter Object refactoring pattern.',
        'Consider whether the method needs all those parameters or if some can be obtained in other ways.',
        'Look for parameter groups that are always passed together - these are good candidates for a new class.'
      ];
    }

    // Duplicate Code recommendations
    if (results['duplicate-code'] && results['duplicate-code'].length > 0) {
      recommendations['duplicate-code'] = [
        'Extract duplicated code into shared methods or utility classes.',
        'Use the Extract Method refactoring pattern for duplicated code blocks.',
        'Consider using inheritance or composition to share behavior.',
        'Apply the DRY (Don\'t Repeat Yourself) principle.'
      ];
    }

    // Feature Envy recommendations
    if (results['feature-envy'] && results['feature-envy'].length > 0) {
      recommendations['feature-envy'] = [
        'Move methods to the class they\'re most interested in.',
        'Use the Move Method refactoring pattern.',
        'Consider whether the current class structure accurately reflects the domain.',
        'Apply the Tell, Don\'t Ask principle to reduce feature envy.'
      ];
    }

    // Shotgun Surgery recommendations
    if (results['shotgun-surgery'] && results['shotgun-surgery'].length > 0) {
      recommendations['shotgun-surgery'] = [
        'Move related fields and methods into a single class.',
        'Use the Move Field and Move Method refactoring patterns.',
        'Consider whether you need to introduce a new abstraction.',
        'Look for concepts that are spread across multiple classes.'
      ];
    }

    // Switch Statements recommendations
    if (results['switch-statements'] && results['switch-statements'].length > 0) {
      recommendations['switch-statements'] = [
        'Replace switch statements with polymorphism.',
        'Use the Replace Conditional with Polymorphism refactoring pattern.',
        'Consider using the Strategy pattern for algorithms that vary.',
        'Look for switch statements that check the same condition in multiple places.'
      ];
    }

    return recommendations;
  }

  private countTotalSmells(results: Record<string, any[]>): number {
    return Object.values(results).reduce((total, smells) => total + smells.length, 0);
  }

  private getSeverityBreakdown(results: Record<string, any[]>): Record<string, number> {
    const breakdown = { high: 0, medium: 0, low: 0 };

    for (const smells of Object.values(results)) {
      for (const smell of smells) {
        if (smell.severity in breakdown) {
          breakdown[smell.severity as keyof typeof breakdown]++;
        }
      }
    }

    return breakdown;
  }
}

/**
 * Suggests and applies refactoring patterns to improve code quality
 */
export class RefactoringAdvisorTool implements ITool {
  readonly id = 'refactoringAdvisor';
  readonly name = 'Refactoring Advisor';
  readonly description = 'Suggests and applies refactoring patterns to improve code quality.';
  readonly type = 'multi-action';
  // Add index signature to fix "Element implicitly has an 'any' type" error
  readonly actions: { [key: string]: any } = {
    'suggest': {
      description: 'Suggest refactoring patterns for a file',
      schema: z.object({
        filePath: z.string().describe('Path to the file to analyze.'),
        refactoringTypes: z.array(z.enum([
          'extract-method',
          'extract-class',
          'introduce-parameter-object',
          'replace-conditional-with-polymorphism',
          'encapsulate-field',
          'replace-magic-number',
          'all'
        ])).optional().describe('Types of refactoring to suggest. Default is all.')
      }),
      inputSchema: z.object({
        filePath: z.string().describe('Path to the file to analyze.'),
        refactoringTypes: z.array(z.enum([
          'extract-method',
          'extract-class',
          'introduce-parameter-object',
          'replace-conditional-with-polymorphism',
          'encapsulate-field',
          'replace-magic-number',
          'all'
        ])).optional().describe('Types of refactoring to suggest. Default is all.')
      })
    },
    'preview': {
      description: 'Preview a specific refactoring',
      schema: z.object({
        filePath: z.string().describe('Path to the file to refactor.'),
        refactoringType: z.enum([
          'extract-method',
          'extract-class',
          'introduce-parameter-object',
          'replace-conditional-with-polymorphism',
          'encapsulate-field',
          'replace-magic-number'
        ]).describe('Type of refactoring to preview.'),
        startLine: z.number().describe('Start line of the code to refactor.'),
        endLine: z.number().describe('End line of the code to refactor.'),
        options: z.record(z.string(), z.any()).optional().describe('Additional options for the refactoring.')
      }),
      inputSchema: z.object({
        filePath: z.string().describe('Path to the file to refactor.'),
        refactoringType: z.enum([
          'extract-method',
          'extract-class',
          'introduce-parameter-object',
          'replace-conditional-with-polymorphism',
          'encapsulate-field',
          'replace-magic-number'
        ]).describe('Type of refactoring to preview.'),
        startLine: z.number().describe('Start line of the code to refactor.'),
        endLine: z.number().describe('End line of the code to refactor.'),
        options: z.record(z.string(), z.any()).optional().describe('Additional options for the refactoring.')
      })
    }
  };

  private llmConfig: LLMConfig = {
    provider: 'openai',
    modelId: 'gpt-3.5-turbo',
    options: { temperature: 0.1 }
  };

  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    try {
      if (!actionName || !this.actions[actionName]) {
        return {
          success: false,
          error: `Unknown action: ${actionName || 'undefined'}. Available actions: ${Object.keys(this.actions).join(', ')}`,
          toolId: this.id,
          actionName
        };
      }

      switch (actionName) {
        case 'suggest':
          return this.suggestRefactorings(input, actionName);
        case 'preview':
          return this.previewRefactoring(input, actionName);
        default:
          return {
            success: false,
            error: `Unknown action: ${actionName}`,
            toolId: this.id,
            actionName
          };
      }
    } catch (error: any) {
      return {
        success: false,
        error: `Refactoring advisor failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  private async suggestRefactorings(input: ToolInput, actionName: string): Promise<ToolResult> {
    const filePath = input.filePath as string;
    const refactoringTypes = (input.refactoringTypes as string[]) || ['all'];

    if (!filePath) {
      return {
        success: false,
        error: '\'filePath\' is required.',
        toolId: this.id,
        actionName
      };
    }

    try {
      // Validate file exists
      const uri = vscode.Uri.file(filePath);
      // Replace Promise.then().catch() with try/catch for better error handling
      let exists = false;
      try {
        await vscode.workspace.fs.stat(uri);
        exists = true;
      } catch {
        exists = false;
      }

      if (!exists) {
        return {
          success: false,
          error: `File not found: ${filePath}`,
          toolId: this.id,
          actionName
        };
      }

      // Get file content
      const document = await vscode.workspace.openTextDocument(filePath);
      const text = document.getText();
      const language = document.languageId;

      // Use LLM to suggest refactorings
      const provider = await llmService.getProviderForConfig(this.llmConfig);
      if (!provider) {
        return {
          success: false,
          error: 'No LLM provider available for refactoring suggestions.',
          toolId: this.id,
          actionName
        };
      }

      const suggestions = await this.suggestRefactoringsWithLLM(text, language, refactoringTypes, provider);

      if (!suggestions.success) {
        return {
          success: false,
          error: suggestions.error,
          toolId: this.id,
          actionName
        };
      }

      return {
        success: true,
        output: {
          suggestions: suggestions.refactorings,
          summary: this.generateRefactoringSummary(suggestions.refactorings)
        },
        toolId: this.id,
        actionName,
        metadata: {
          filePath,
          refactoringTypes: refactoringTypes.includes('all') ?
            ['extract-method', 'extract-class', 'introduce-parameter-object',
              'replace-conditional-with-polymorphism', 'encapsulate-field', 'replace-magic-number'] :
            refactoringTypes
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: `Refactoring suggestion failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  private async previewRefactoring(input: ToolInput, actionName: string): Promise<ToolResult> {
    const filePath = input.filePath as string;
    const refactoringType = input.refactoringType as string;
    const startLine = input.startLine as number;
    const endLine = input.endLine as number;
    const options = input.options as Record<string, any> || {};

    if (!filePath) {
      return {
        success: false,
        error: '\'filePath\' is required.',
        toolId: this.id,
        actionName
      };
    }

    if (!refactoringType) {
      return {
        success: false,
        error: '\'refactoringType\' is required.',
        toolId: this.id,
        actionName
      };
    }

    if (startLine === undefined || startLine === null) {
      return {
        success: false,
        error: '\'startLine\' is required.',
        toolId: this.id,
        actionName
      };
    }

    if (endLine === undefined || endLine === null) {
      return {
        success: false,
        error: '\'endLine\' is required.',
        toolId: this.id,
        actionName
      };
    }

    try {
      // Validate file exists
      const uri = vscode.Uri.file(filePath);
      // Replace Promise.then().catch() with try/catch for better error handling
      let exists = false;
      try {
        await vscode.workspace.fs.stat(uri);
        exists = true;
      } catch {
        exists = false;
      }

      if (!exists) {
        return {
          success: false,
          error: `File not found: ${filePath}`,
          toolId: this.id,
          actionName
        };
      }

      // Get file content
      const document = await vscode.workspace.openTextDocument(filePath);
      const text = document.getText();
      const language = document.languageId;

      // Validate line range
      const lineCount = document.lineCount;

      if (startLine < 1 || startLine > lineCount) {
        return {
          success: false,
          error: `Invalid startLine: ${startLine}. File has ${lineCount} lines.`,
          toolId: this.id,
          actionName
        };
      }

      if (endLine < startLine || endLine > lineCount) {
        return {
          success: false,
          error: `Invalid endLine: ${endLine}. File has ${lineCount} lines and startLine is ${startLine}.`,
          toolId: this.id,
          actionName
        };
      }

      // Extract the code to refactor
      const startOffset = document.offsetAt(new vscode.Position(startLine - 1, 0));
      const endOffset = document.offsetAt(new vscode.Position(endLine, 0));
      const codeToRefactor = text.substring(startOffset, endOffset);

      // Use LLM to preview refactoring
      const provider = await llmService.getProviderForConfig(this.llmConfig);
      if (!provider) {
        return {
          success: false,
          error: 'No LLM provider available for refactoring preview.',
          toolId: this.id,
          actionName
        };
      }

      const preview = await this.previewRefactoringWithLLM(text, codeToRefactor, startLine, endLine, refactoringType, language, options, provider);

      if (!preview.success) {
        return {
          success: false,
          error: preview.error,
          toolId: this.id,
          actionName
        };
      }

      return {
        success: true,
        output: {
          original: {
            code: codeToRefactor,
            startLine,
            endLine
          },
          refactored: preview.refactored,
          explanation: preview.explanation,
          refactoringType
        },
        toolId: this.id,
        actionName,
        metadata: {
          filePath,
          refactoringType,
          startLine,
          endLine
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: `Refactoring preview failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }

  private async suggestRefactoringsWithLLM(code: string, language: string, types: string[], provider: any): Promise<any> {
    const checkAll = types.includes('all');
    const typesToCheck = checkAll ?
      ['extract-method', 'extract-class', 'introduce-parameter-object',
        'replace-conditional-with-polymorphism', 'encapsulate-field', 'replace-magic-number'] :
      types;

    // Prepare prompt for LLM
    const prompt = `
Analyze the following ${language} code and suggest refactoring opportunities, specifically focusing on: ${typesToCheck.join(', ')}.
For each refactoring opportunity, provide:
1. The type of refactoring
2. The location (line numbers)
3. A brief description of why this refactoring would improve the code
4. A brief explanation of how to implement the refactoring

Code to analyze:
\`\`\`${language}
${code}
\`\`\`

Format your response as JSON with the following structure:
{
  "refactorings": [
    {
      "type": "extract-method|extract-class|introduce-parameter-object|replace-conditional-with-polymorphism|encapsulate-field|replace-magic-number",
      "startLine": number,
      "endLine": number,
      "description": "string",
      "implementation": "string"
    }
  ]
}
`;

    try {
      const result = await provider.generate({
        prompt,
        systemPrompt: 'You are an expert software engineer specializing in code quality and refactoring. Analyze the provided code and suggest refactoring opportunities with high precision.',
        modelId: this.llmConfig.modelId,
        options: this.llmConfig.options
      });

      if (result.error) {
        return { success: false, error: result.error };
      }

      // Try to parse the LLM response as JSON
      const jsonMatch = result.content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsedResult = JSON.parse(jsonMatch[0]);
        return {
          success: true,
          refactorings: parsedResult.refactorings || []
        };
      } else {
        return {
          success: false,
          error: 'LLM response could not be parsed as JSON'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to get refactoring suggestions: ${error}`
      };
    }
  }

  private async previewRefactoringWithLLM(fullCode: string, codeToRefactor: string, startLine: number, endLine: number, refactoringType: string, language: string, options: Record<string, any>, provider: any): Promise<any> {
    // Prepare options string
    const optionsString = Object.entries(options)
      .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
      .join('\n');

    // Prepare prompt for LLM
    const prompt = `
You are tasked with refactoring the following ${language} code using the "${refactoringType}" refactoring pattern.

Original code to refactor (lines ${startLine}-${endLine}):
\`\`\`${language}
${codeToRefactor}
\`\`\`

${optionsString ? `Additional options:\n${optionsString}\n` : ''}

Context (full file):
\`\`\`${language}
${fullCode}
\`\`\`

Please provide:
1. The refactored code
2. A detailed explanation of the changes made
3. Any additional files that need to be created (if applicable)

Format your response as JSON with the following structure:
{
  "refactored": {
    "code": "string (the refactored code)",
    "additionalFiles": [
      {
        "path": "string (relative path to the new file)",
        "content": "string (content of the new file)"
      }
    ]
  },
  "explanation": "string (detailed explanation of the refactoring)"
}
`;

    try {
      const result = await provider.generate({
        prompt,
        systemPrompt: 'You are an expert software engineer specializing in code quality and refactoring. Implement the requested refactoring with high precision.',
        modelId: this.llmConfig.modelId,
        options: this.llmConfig.options
      });

      if (result.error) {
        return { success: false, error: result.error };
      }

      // Try to parse the LLM response as JSON
      const jsonMatch = result.content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsedResult = JSON.parse(jsonMatch[0]);
        return {
          success: true,
          refactored: parsedResult.refactored || { code: '', additionalFiles: [] },
          explanation: parsedResult.explanation || 'No explanation provided.'
        };
      } else {
        return {
          success: false,
          error: 'LLM response could not be parsed as JSON'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: `Failed to preview refactoring: ${error}`
      };
    }
  }

  private generateRefactoringSummary(refactorings: any[]): string {
    if (refactorings.length === 0) {
      return 'No refactoring opportunities found.';
    }

    // Group refactorings by type
    const groupedRefactorings: Record<string, number> = {};

    for (const refactoring of refactorings) {
      const type = refactoring.type;
      groupedRefactorings[type] = (groupedRefactorings[type] || 0) + 1;
    }

    // Generate summary
    const summary = [
      `Found ${refactorings.length} refactoring opportunities:`,
      ...Object.entries(groupedRefactorings).map(([type, count]) => `- ${count} ${type} refactoring(s)`)
    ].join('\n');

    return summary;
  }
}
