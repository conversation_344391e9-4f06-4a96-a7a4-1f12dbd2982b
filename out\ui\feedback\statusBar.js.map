{"version": 3, "file": "statusBar.js", "sourceRoot": "", "sources": ["../../../src/ui/feedback/statusBar.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,yCAAsC;AACtC,qDAAkD;AAClD,2EAAwE;AAIxE;;GAEG;AACH,MAAa,gBAAgB;IACnB,iBAAiB,CAAuB;IACxC,kBAAkB,CAAuB;IACzC,kBAAkB,CAAuB;IACzC,sBAAsB,CAAuB;IAErD;QACE,8BAA8B;QAC9B,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CACxD,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAC/B,GAAG,CAAC,iDAAiD;SACtD,CAAC;QACF,IAAI,CAAC,iBAAiB,CAAC,IAAI,GAAG,kBAAkB,CAAC;QACjD,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,6BAA6B,CAAC;QAC/D,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,0BAA0B,CAAC;QAE5D,mCAAmC;QACnC,IAAI,CAAC,sBAAsB,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAC7D,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAC/B,EAAE,CACH,CAAC;QACF,IAAI,CAAC,sBAAsB,CAAC,IAAI,GAAG,wBAAwB,CAAC;QAC5D,IAAI,CAAC,sBAAsB,CAAC,OAAO,GAAG,wBAAwB,CAAC;QAC/D,IAAI,CAAC,sBAAsB,CAAC,OAAO,GAAG,uBAAuB,CAAC;QAE9D,2BAA2B;QAC3B,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CACzD,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAC/B,EAAE,CACH,CAAC;QAEF,2BAA2B;QAC3B,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CACzD,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAC/B,EAAE,CACH,CAAC;IACJ,CAAC;IAED;;SAEK;IACL,UAAU;QACR,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEzB,kCAAkC;QAClC,uBAAU,CAAC,kBAAkB,CAAC,GAAG,EAAE;YACjC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,mCAAmC;QACnC,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;YAC5C,IAAI,CAAC,CAAC,oBAAoB,CAAC,sBAAsB,CAAC;gBAChD,CAAC,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,EAAE,CAAC;gBAC9C,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC;QAC9B,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,CAAC;IACrC,CAAC;IAED;;SAEK;IACL,KAAK,CAAC,iBAAiB;QACrB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,mBAAmB,GAAG,MAAM,uBAAU,CAAC,sBAAsB,EAAE,CAAC;YACtE,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACrC,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,6BAA6B,CAAC;gBAC7D,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,oCAAoC,CAAC;gBACvE,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,sBAAsB,CAAC;gBACzD,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;gBAC/B,OAAO;YACT,CAAC;YAED,2BAA2B;YAC3B,MAAM,eAAe,GAAG,MAAM,uBAAU,CAAC,kBAAkB,EAAE,CAAC;YAC9D,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,wCAAwC;gBACxC,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,4BAA4B,CAAC;gBAC5D,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,6CAA6C,CAAC;gBAChF,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,sBAAsB,CAAC;gBACzD,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;gBAC/B,OAAO;YACT,CAAC;YAED,MAAM,WAAW,GAAG,MAAM,uBAAU,CAAC,qBAAqB,EAAE,CAAC;YAC7D,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,EAAE,WAAW,IAAI,GAAG,CAAC;YAE5D,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,aAAa,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,OAAO,KAAK,WAAW,GAAG,CAAC;YAC3G,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,kEAAkE,CAAC;YACrG,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,sBAAsB,CAAC;YACzD,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YAE7D,qEAAqE;YACrE,0DAA0D;YAC1D,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,4BAA4B,CAAC;YAC5D,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,iCAAiC,CAAC;YAEpE,oDAAoD;YACpD,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5E,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,yCAAyC,YAAY,EAAE,CAAC,CAAC;YAE/E,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,sBAAsB,CAAC;YACzD,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;QACjC,CAAC;IACH,CAAC;IAED;;SAEK;IACL,iBAAiB;QACf,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,2BAAY,CAAC,WAAW,EAAE,CAAC,YAAY,EAAE,CAAC;YAEzD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACxB,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,4BAA4B,CAAC;gBAC5D,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,4CAA4C,CAAC;gBAC/E,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,kBAAkB,CAAC;gBACrD,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;gBAC/B,OAAO;YACT,CAAC;YAED,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,aAAa,MAAM,CAAC,MAAM,SAAS,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;YACjG,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,GAAG,MAAM,CAAC,MAAM,SAAS,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC;YACpG,IAAI,CAAC,kBAAkB,CAAC,OAAO,GAAG,uBAAuB,CAAC;YAC1D,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YAC7D,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;QACjC,CAAC;IACH,CAAC;IAED;;;SAGK;IACL,oBAAoB,CAAC,QAAiB;QACpC,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,CAAC,iBAAiB,CAAC,IAAI,GAAG,sBAAsB,CAAC;YACrD,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,0BAA0B,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,iBAAiB,CAAC,IAAI,GAAG,kBAAkB,CAAC;YACjD,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,6BAA6B,CAAC;QACjE,CAAC;IACH,CAAC;IAED;;SAEK;IACL,OAAO;QACL,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,CAAC;QACtC,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;QAClC,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;IACpC,CAAC;CACF;AAvKD,4CAuKC;AAED;;GAEG;AACU,QAAA,gBAAgB,GAAG,IAAI,gBAAgB,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { Logger } from '../../logger';\nimport { llmService } from '../../llm/llmService';\nimport { AgentManager } from '../../agents/agentUtilities/agentManager';\nimport { shouldSuppressError as _shouldSuppressError } from './notifications';\nimport { getConfig as _getConfig } from '../../config';\n\n/**\n * Manages status bar items for Codessa\n */\nexport class StatusBarManager {\n  private mainStatusBarItem: vscode.StatusBarItem;\n  private modelStatusBarItem: vscode.StatusBarItem;\n  private agentStatusBarItem: vscode.StatusBarItem;\n  private dashboardStatusBarItem: vscode.StatusBarItem;\n\n  constructor() {\n    // Create main status bar item\n    this.mainStatusBarItem = vscode.window.createStatusBarItem(\n      vscode.StatusBarAlignment.Right,\n      100 // High priority (lower number = higher priority)\n    );\n    this.mainStatusBarItem.text = '$(hubot) Codessa';\n    this.mainStatusBarItem.tooltip = 'Codessa AI Coding Assistant';\n    this.mainStatusBarItem.command = 'codessa.showQuickActions';\n\n    // Create dashboard status bar item\n    this.dashboardStatusBarItem = vscode.window.createStatusBarItem(\n      vscode.StatusBarAlignment.Right,\n      97\n    );\n    this.dashboardStatusBarItem.text = '$(dashboard) Dashboard';\n    this.dashboardStatusBarItem.tooltip = 'Open Codessa Dashboard';\n    this.dashboardStatusBarItem.command = 'codessa.openDashboard';\n\n    // Create model status item\n    this.modelStatusBarItem = vscode.window.createStatusBarItem(\n      vscode.StatusBarAlignment.Right,\n      99\n    );\n\n    // Create agent status item\n    this.agentStatusBarItem = vscode.window.createStatusBarItem(\n      vscode.StatusBarAlignment.Right,\n      98\n    );\n  }\n\n  /**\n     * Initialize and show status bar items\n     */\n  initialize(): void {\n    this.updateModelStatus();\n    this.updateAgentStatus();\n\n    // Listen for LLM provider changes\n    llmService.onProvidersChanged(() => {\n      this.updateModelStatus();\n    });\n\n    // Listen for configuration changes\n    vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration('codessa.defaultModel') ||\n        e.affectsConfiguration('codessa.providers')) {\n        this.updateModelStatus();\n      }\n    });\n\n    this.mainStatusBarItem.show();\n    this.dashboardStatusBarItem.show();\n  }\n\n  /**\n     * Update model status bar item to show current default model\n     */\n  async updateModelStatus(): Promise<void> {\n    if (!this.modelStatusBarItem) {\n      return;\n    }\n\n    try {\n      const configuredProviders = await llmService.getConfiguredProviders();\n      if (configuredProviders.length === 0) {\n        this.modelStatusBarItem.text = '$(server) No LLM Configured';\n        this.modelStatusBarItem.tooltip = 'Click to configure an LLM provider';\n        this.modelStatusBarItem.command = 'codessa.openSettings';\n        this.modelStatusBarItem.show();\n        return;\n      }\n\n      // Get the default provider\n      const defaultProvider = await llmService.getDefaultProvider();\n      if (!defaultProvider) {\n        // Always use a neutral icon and message\n        this.modelStatusBarItem.text = '$(server) Default Provider';\n        this.modelStatusBarItem.tooltip = 'Click to configure the default LLM provider';\n        this.modelStatusBarItem.command = 'codessa.openSettings';\n        this.modelStatusBarItem.show();\n        return;\n      }\n\n      const modelConfig = await llmService.getDefaultModelConfig();\n      const temperature = modelConfig.options?.temperature ?? 0.7;\n\n      this.modelStatusBarItem.text = `$(server) ${modelConfig.provider}/${modelConfig.modelId} (${temperature})`;\n      this.modelStatusBarItem.tooltip = 'Current default LLM provider and model\\nClick to change settings';\n      this.modelStatusBarItem.command = 'codessa.openSettings';\n      this.modelStatusBarItem.show();\n    } catch (error) {\n      Logger.instance.error('Error updating model status:', error);\n\n      // Always use a neutral icon and message for errors in the status bar\n      // This prevents \"screaming\" at users with red error icons\n      this.modelStatusBarItem.text = '$(server) Default Provider';\n      this.modelStatusBarItem.tooltip = 'Click to configure LLM settings';\n\n      // Just log the error without showing it to the user\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      Logger.instance.debug(`Status bar error (not shown to user): ${errorMessage}`);\n\n      this.modelStatusBarItem.command = 'codessa.openSettings';\n      this.modelStatusBarItem.show();\n    }\n  }\n\n  /**\n     * Update agent status bar item to show available agents\n     */\n  updateAgentStatus(): void {\n    if (!this.agentStatusBarItem) {\n      return;\n    }\n\n    try {\n      const agents = AgentManager.getInstance().getAllAgents();\n\n      if (agents.length === 0) {\n        this.agentStatusBarItem.text = '$(person-add) Create Agent';\n        this.agentStatusBarItem.tooltip = 'No agents configured. Click to create one.';\n        this.agentStatusBarItem.command = 'codessa.addAgent';\n        this.agentStatusBarItem.show();\n        return;\n      }\n\n      this.agentStatusBarItem.text = `$(person) ${agents.length} Agent${agents.length > 1 ? 's' : ''}`;\n      this.agentStatusBarItem.tooltip = `${agents.length} agent${agents.length > 1 ? 's' : ''} available`;\n      this.agentStatusBarItem.command = 'codessa.showAgentList';\n      this.agentStatusBarItem.show();\n    } catch (error) {\n      Logger.instance.error('Error updating agent status:', error);\n      this.agentStatusBarItem.hide();\n    }\n  }\n\n  /**\n     * Show activity indicator when an agent is processing\n     * @param isActive Whether an agent is actively processing\n     */\n  setActivityIndicator(isActive: boolean): void {\n    if (isActive) {\n      this.mainStatusBarItem.text = '$(sync~spin) Codessa';\n      this.mainStatusBarItem.tooltip = 'Codessa is processing...';\n    } else {\n      this.mainStatusBarItem.text = '$(hubot) Codessa';\n      this.mainStatusBarItem.tooltip = 'Codessa AI Coding Assistant';\n    }\n  }\n\n  /**\n     * Dispose all status bar items\n     */\n  dispose(): void {\n    this.mainStatusBarItem.dispose();\n    this.dashboardStatusBarItem.dispose();\n    this.modelStatusBarItem.dispose();\n    this.agentStatusBarItem.dispose();\n  }\n}\n\n/**\n * Status bar manager singleton\n */\nexport const statusBarManager = new StatusBarManager();"]}