{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../src/config.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,8BASC;AAKD,8BAoDC;AAKD,oCAUC;AAKD,4CAQC;AAKD,kDAQC;AAGD,kCAEC;AAED,oDAEC;AAED,sDAcC;AAGD,4CAEC;AAED,wCAEC;AAED,kEAEC;AAED,gEAEC;AAED,gEAEC;AAGD,0CAEC;AAED,4CAGC;AAED,sDAEC;AAED,8CAEC;AAED,gDAEC;AAED,gDAEC;AAED,4CAEC;AAED,gDAEC;AAGD,8BAEC;AAED,gCAEC;AAGD,4CAEC;AAED,gDAEC;AAGD,wCAeC;AAED,gDAaC;AAED,gDAcC;AAED,wDAaC;AAED,0DAcC;AAED,8CAcC;AAED,sCAcC;AAGD,wCAEC;AAED,gDAEC;AAED,gDAEC;AAED,wDAEC;AAED,0DAEC;AAED,8CAEC;AAED,sCAEC;AAhWD,+CAAiC;AACjC,qCAAkC;AAElC;;GAEG;AACH,SAAgB,SAAS,CAAI,GAAW,EAAE,YAAe;IACvD,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC5D,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,CAAI,GAAG,CAAC,CAAC;QACjC,OAAO,KAAK,KAAK,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC;IACpD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,4CAA4C,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC;QACzE,OAAO,YAAY,CAAC;IACtB,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,SAAS,CAC7B,GAAW,EACX,KAAQ,EACR,SAAqC,MAAM,CAAC,mBAAmB,CAAC,MAAM;IAEtE,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAE5D,mBAAmB;QACnB,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,sCAAsC;QACtC,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;YACxC,eAAM,CAAC,IAAI,CAAC,sCAAsC,GAAG,qBAAqB,MAAM,EAAE,CAAC,CAAC;YACpF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,WAAW,EAAE,CAAC;YACrB,eAAM,CAAC,IAAI,CAAC,oCAAoC,MAAM,KAAK,WAAW,iCAAiC,CAAC,CAAC;QAC3G,CAAC;QAED,4CAA4C;QAC5C,IAAI,MAAM,KAAK,MAAM,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC;YACjD,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBACnE,eAAM,CAAC,IAAI,CAAC,sCAAsC,GAAG,mBAAmB,CAAC,CAAC;gBAC1E,OAAO,IAAI,CAAC;YACd,CAAC;YAAC,OAAO,WAAW,EAAE,CAAC;gBACrB,eAAM,CAAC,IAAI,CAAC,qCAAqC,WAAW,6BAA6B,CAAC,CAAC;YAC7F,CAAC;QACH,CAAC;QAED,8CAA8C;QAC9C,IAAI,MAAM,KAAK,MAAM,CAAC,mBAAmB,CAAC,SAAS;YAC3C,MAAM,CAAC,SAAS,CAAC,gBAAgB;YACjC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrD,IAAI,CAAC;gBACH,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,EAAE,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;gBACtE,eAAM,CAAC,IAAI,CAAC,sCAAsC,GAAG,sBAAsB,CAAC,CAAC;gBAC7E,OAAO,IAAI,CAAC;YACd,CAAC;YAAC,OAAO,cAAc,EAAE,CAAC;gBACxB,eAAM,CAAC,IAAI,CAAC,wCAAwC,cAAc,EAAE,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,sCAAsC;QACtC,MAAM,IAAI,KAAK,CAAC,qCAAqC,GAAG,uBAAuB,CAAC,CAAC;IACnF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,4CAA4C,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC;QACzE,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,YAAY,CAAC,GAAW;IAC5C,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC5D,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACvE,eAAM,CAAC,IAAI,CAAC,sCAAsC,GAAG,GAAG,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,6CAA6C,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC;QAC1E,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,gBAAgB;IAC9B,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC5D,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;IACvB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC7D,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CAAC,GAAW;IAC7C,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC5D,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC7B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,+CAA+C,GAAG,IAAI,EAAE,KAAK,CAAC,CAAC;QAC5E,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED,gBAAgB;AAChB,SAAgB,WAAW;IACzB,OAAO,SAAS,CAAS,UAAU,EAAE,MAAM,CAAC,CAAC;AAC/C,CAAC;AAED,SAAgB,oBAAoB;IAClC,OAAO,SAAS,CAAS,mBAAmB,EAAE,CAAC,CAAC,CAAC;AACnD,CAAC;AAED,SAAgB,qBAAqB;IACnC,0DAA0D;IAC1D,MAAM,gBAAgB,GAAG,SAAS,CAAS,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IACzE,MAAM,aAAa,GAAG,SAAS,CAAS,eAAe,EAAE,QAAQ,CAAC,CAAC;IAEnE,MAAM,aAAa,GAAc;QAC/B,QAAQ,EAAE,gBAAgB;QAC1B,OAAO,EAAE,aAAa;QACtB,OAAO,EAAE;YACP,WAAW,EAAE,GAAG;YAChB,SAAS,EAAE,IAAI;SAChB;KACF,CAAC;IACF,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,kBAAkB;AAClB,SAAgB,gBAAgB;IAC9B,OAAO,SAAS,CAAU,gBAAgB,EAAE,IAAI,CAAC,CAAC;AACpD,CAAC;AAED,SAAgB,cAAc;IAC5B,OAAO,SAAS,CAAS,oBAAoB,EAAE,IAAI,CAAC,CAAC;AACvD,CAAC;AAED,SAAgB,2BAA2B;IACzC,OAAO,SAAS,CAAS,2BAA2B,EAAE,GAAG,CAAC,CAAC;AAC7D,CAAC;AAED,SAAgB,0BAA0B;IACxC,OAAO,SAAS,CAAS,0BAA0B,EAAE,CAAC,CAAC,CAAC;AAC1D,CAAC;AAED,SAAgB,0BAA0B;IACxC,OAAO,SAAS,CAAS,gCAAgC,EAAE,GAAG,CAAC,CAAC;AAClE,CAAC;AAED,0BAA0B;AAC1B,SAAgB,eAAe;IAC7B,OAAO,SAAS,CAAS,yBAAyB,EAAE,EAAE,CAAC,CAAC;AAC1D,CAAC;AAED,SAAgB,gBAAgB;IAC9B,MAAM,UAAU,GAAG,2BAA2B,CAAC;IAC/C,OAAO,SAAS,CAAS,0BAA0B,EAAE,UAAU,CAAC,CAAC;AACnE,CAAC;AAED,SAAgB,qBAAqB;IACnC,OAAO,SAAS,CAAS,+BAA+B,EAAE,EAAE,CAAC,CAAC;AAChE,CAAC;AAED,SAAgB,iBAAiB;IAC/B,OAAO,SAAS,CAAS,2BAA2B,EAAE,EAAE,CAAC,CAAC;AAC5D,CAAC;AAED,SAAgB,kBAAkB;IAChC,OAAO,SAAS,CAAS,4BAA4B,EAAE,EAAE,CAAC,CAAC;AAC7D,CAAC;AAED,SAAgB,kBAAkB;IAChC,OAAO,SAAS,CAAS,4BAA4B,EAAE,EAAE,CAAC,CAAC;AAC7D,CAAC;AAED,SAAgB,gBAAgB;IAC9B,OAAO,SAAS,CAAS,0BAA0B,EAAE,wBAAwB,CAAC,CAAC;AACjF,CAAC;AAED,SAAgB,kBAAkB;IAChC,OAAO,SAAS,CAAS,4BAA4B,EAAE,0BAA0B,CAAC,CAAC;AACrF,CAAC;AAED,sBAAsB;AACtB,SAAgB,SAAS;IACvB,OAAO,SAAS,CAAgB,QAAQ,EAAE,EAAE,CAAC,CAAC;AAChD,CAAC;AAEM,KAAK,UAAU,UAAU,CAAC,MAAqB;IACpD,OAAO,MAAM,SAAS,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;AAC3C,CAAC;AAED,uBAAuB;AACvB,SAAgB,gBAAgB;IAC9B,OAAO,SAAS,CAAyB,eAAe,EAAE,EAAE,CAAC,CAAC;AAChE,CAAC;AAED,SAAgB,kBAAkB;IAChC,OAAO,SAAS,CAAyB,iBAAiB,EAAE,EAAE,CAAC,CAAC;AAClE,CAAC;AAED,6BAA6B;AAC7B,SAAgB,cAAc;IAC5B,MAAM,eAAe,GAAoB;QACvC,OAAO,EAAE,IAAI;QACb,aAAa,EAAE,EAAE;QACjB,qBAAqB,EAAE,EAAE;QACzB,iBAAiB,EAAE,UAAU;QAC7B,WAAW,EAAE,EAAE;QACf,eAAe,EAAE,EAAE;QACnB,YAAY,EAAE,EAAE;QAChB,qBAAqB,EAAE,IAAI;QAC3B,YAAY,EAAE,IAAI;QAClB,mBAAmB,EAAE,IAAI;QACzB,cAAc,EAAE,IAAI;KACrB,CAAC;IACF,OAAO,SAAS,CAAkB,8BAA8B,EAAE,eAAe,CAAC,CAAC;AACrF,CAAC;AAED,SAAgB,kBAAkB;IAChC,MAAM,eAAe,GAAoB;QACvC,OAAO,EAAE,IAAI;QACb,kBAAkB,EAAE,IAAI;QACxB,qBAAqB,EAAE,IAAI;QAC3B,qBAAqB,EAAE,IAAI;QAC3B,uBAAuB,EAAE,IAAI;QAC7B,mBAAmB,EAAE,IAAI;QACzB,mBAAmB,EAAE,GAAG;QACxB,oBAAoB,EAAE,CAAC;QACvB,sBAAsB,EAAE,UAAU;KACnC,CAAC;IACF,OAAO,SAAS,CAAkB,kCAAkC,EAAE,eAAe,CAAC,CAAC;AACzF,CAAC;AAED,SAAgB,kBAAkB;IAChC,MAAM,eAAe,GAAmB;QACtC,OAAO,EAAE,IAAI;QACb,uBAAuB,EAAE,IAAI;QAC7B,mBAAmB,EAAE,IAAI;QACzB,qBAAqB,EAAE,IAAI;QAC3B,eAAe,EAAE,IAAI;QACrB,cAAc,EAAE,IAAI;QACpB,iBAAiB,EAAE,QAAQ;QAC3B,YAAY,EAAE,IAAI;QAClB,mBAAmB,EAAE,GAAG;QACxB,kBAAkB,EAAE,EAAE;KACvB,CAAC;IACF,OAAO,SAAS,CAAiB,kCAAkC,EAAE,eAAe,CAAC,CAAC;AACxF,CAAC;AAED,SAAgB,sBAAsB;IACpC,MAAM,eAAe,GAAuB;QAC1C,OAAO,EAAE,IAAI;QACb,qBAAqB,EAAE,IAAI;QAC3B,oBAAoB,EAAE,IAAI;QAC1B,oBAAoB,EAAE,IAAI;QAC1B,gBAAgB,EAAE,IAAI;QACtB,kBAAkB,EAAE,MAAM;QAC1B,WAAW,EAAE,UAAU;QACvB,aAAa,EAAE,GAAG;QAClB,cAAc,EAAE,EAAE;KACnB,CAAC;IACF,OAAO,SAAS,CAAqB,sCAAsC,EAAE,eAAe,CAAC,CAAC;AAChG,CAAC;AAED,SAAgB,uBAAuB;IACrC,MAAM,eAAe,GAAgC;QACnD,uBAAuB,EAAE,IAAI;QAC7B,eAAe,EAAE,IAAI;QACrB,eAAe,EAAE,IAAI;QACrB,mBAAmB,EAAE,IAAI;QACzB,cAAc,EAAE,IAAI;QACpB,qBAAqB,EAAE,IAAI;QAC3B,iBAAiB,EAAE,IAAI;QACvB,kBAAkB,EAAE,IAAI;QACxB,wBAAwB,EAAE,IAAI;QAC9B,mBAAmB,EAAE,IAAI;KAC1B,CAAC;IACF,OAAO,SAAS,CAA8B,uCAAuC,EAAE,eAAe,CAAC,CAAC;AAC1G,CAAC;AAED,SAAgB,iBAAiB;IAC/B,MAAM,eAAe,GAA0B;QAC7C,aAAa,EAAE,IAAI;QACnB,iBAAiB,EAAE,IAAI;QACvB,cAAc,EAAE,IAAI;QACpB,oBAAoB,EAAE,IAAI;QAC1B,oBAAoB,EAAE,IAAI;QAC1B,iBAAiB,EAAE,IAAI;QACvB,iBAAiB,EAAE,IAAI;QACvB,oBAAoB,EAAE,IAAI;QAC1B,cAAc,EAAE,IAAI;QACpB,oBAAoB,EAAE,IAAI;KAC3B,CAAC;IACF,OAAO,SAAS,CAAwB,iCAAiC,EAAE,eAAe,CAAC,CAAC;AAC9F,CAAC;AAED,SAAgB,aAAa;IAC3B,MAAM,eAAe,GAAuB;QAC1C,oBAAoB,EAAE,IAAI;QAC1B,uBAAuB,EAAE,IAAI;QAC7B,qBAAqB,EAAE,IAAI;QAC3B,gBAAgB,EAAE,IAAI;QACtB,uBAAuB,EAAE,IAAI;QAC7B,gBAAgB,EAAE,IAAI;QACtB,uBAAuB,EAAE,IAAI;QAC7B,yBAAyB,EAAE,IAAI;QAC/B,wBAAwB,EAAE,IAAI;QAC9B,6BAA6B,EAAE,IAAI;KACpC,CAAC;IACF,OAAO,SAAS,CAAqB,6BAA6B,EAAE,eAAe,CAAC,CAAC;AACvF,CAAC;AAED,yCAAyC;AAClC,KAAK,UAAU,cAAc,CAAC,QAAkC;IACrE,OAAO,MAAM,SAAS,CAAC,8BAA8B,EAAE,QAAQ,CAAC,CAAC;AACnE,CAAC;AAEM,KAAK,UAAU,kBAAkB,CAAC,QAAkC;IACzE,OAAO,MAAM,SAAS,CAAC,kCAAkC,EAAE,QAAQ,CAAC,CAAC;AACvE,CAAC;AAEM,KAAK,UAAU,kBAAkB,CAAC,QAAiC;IACxE,OAAO,MAAM,SAAS,CAAC,kCAAkC,EAAE,QAAQ,CAAC,CAAC;AACvE,CAAC;AAEM,KAAK,UAAU,sBAAsB,CAAC,QAAqC;IAChF,OAAO,MAAM,SAAS,CAAC,sCAAsC,EAAE,QAAQ,CAAC,CAAC;AAC3E,CAAC;AAEM,KAAK,UAAU,uBAAuB,CAAC,QAA8C;IAC1F,OAAO,MAAM,SAAS,CAAC,uCAAuC,EAAE,QAAQ,CAAC,CAAC;AAC5E,CAAC;AAEM,KAAK,UAAU,iBAAiB,CAAC,QAAwC;IAC9E,OAAO,MAAM,SAAS,CAAC,iCAAiC,EAAE,QAAQ,CAAC,CAAC;AACtE,CAAC;AAEM,KAAK,UAAU,aAAa,CAAC,QAAqC;IACvE,OAAO,MAAM,SAAS,CAAC,6BAA6B,EAAE,QAAQ,CAAC,CAAC;AAClE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { logger } from './logger';\n\n/**\n * Get a configuration value from VS Code settings\n */\nexport function getConfig<T>(key: string, defaultValue: T): T {\n  try {\n    const config = vscode.workspace.getConfiguration('codessa');\n    const value = config.get<T>(key);\n    return value !== undefined ? value : defaultValue;\n  } catch (error) {\n    logger.error(`Error reading configuration key 'codessa.${key}':`, error);\n    return defaultValue;\n  }\n}\n\n/**\n * Set a configuration value in VS Code settings\n */\nexport async function setConfig<T>(\n  key: string,\n  value: T,\n  target: vscode.ConfigurationTarget = vscode.ConfigurationTarget.Global\n): Promise<boolean> {\n  try {\n    const config = vscode.workspace.getConfiguration('codessa');\n\n    // Validate the key\n    if (!key || typeof key !== 'string') {\n      throw new Error('Invalid configuration key');\n    }\n\n    // Try with the specified target first\n    try {\n      await config.update(key, value, target);\n      logger.info(`Updated configuration key 'codessa.${key}' at target level ${target}`);\n      return true;\n    } catch (targetError) {\n      logger.warn(`Failed to update at target level ${target}: ${targetError}. Trying alternative targets...`);\n    }\n\n    // If the specified target fails, try Global\n    if (target !== vscode.ConfigurationTarget.Global) {\n      try {\n        await config.update(key, value, vscode.ConfigurationTarget.Global);\n        logger.info(`Updated configuration key 'codessa.${key}' at Global level`);\n        return true;\n      } catch (globalError) {\n        logger.warn(`Failed to update at Global level: ${globalError}. Trying Workspace level...`);\n      }\n    }\n\n    // If Global fails, try Workspace if available\n    if (target !== vscode.ConfigurationTarget.Workspace &&\n            vscode.workspace.workspaceFolders &&\n            vscode.workspace.workspaceFolders.length > 0) {\n      try {\n        await config.update(key, value, vscode.ConfigurationTarget.Workspace);\n        logger.info(`Updated configuration key 'codessa.${key}' at Workspace level`);\n        return true;\n      } catch (workspaceError) {\n        logger.warn(`Failed to update at Workspace level: ${workspaceError}`);\n      }\n    }\n\n    // If we get here, all attempts failed\n    throw new Error(`Failed to update setting 'codessa.${key}' at any target level`);\n  } catch (error) {\n    logger.error(`Error writing configuration key 'codessa.${key}':`, error);\n    return false;\n  }\n}\n\n/**\n * Delete a configuration value\n */\nexport async function deleteConfig(key: string): Promise<boolean> {\n  try {\n    const config = vscode.workspace.getConfiguration('codessa');\n    await config.update(key, undefined, vscode.ConfigurationTarget.Global);\n    logger.info(`Deleted configuration key 'codessa.${key}'`);\n    return true;\n  } catch (error) {\n    logger.error(`Error deleting configuration key 'codessa.${key}':`, error);\n    return false;\n  }\n}\n\n/**\n * Get all configuration keys\n */\nexport function getAllConfigKeys(): string[] {\n  try {\n    const config = vscode.workspace.getConfiguration('codessa');\n    return config.keys();\n  } catch (error) {\n    logger.error('Error getting all configuration keys:', error);\n    return [];\n  }\n}\n\n/**\n * Get configuration inspection (includes default, global, workspace values)\n */\nexport function getConfigInspection(key: string): any {\n  try {\n    const config = vscode.workspace.getConfiguration('codessa');\n    return config.inspect(key);\n  } catch (error) {\n    logger.error(`Error inspecting configuration key 'codessa.${key}':`, error);\n    return undefined;\n  }\n}\n\n// Core settings\nexport function getLogLevel(): string {\n  return getConfig<string>('logLevel', 'info');\n}\n\nexport function getMaxToolIterations(): number {\n  return getConfig<number>('maxToolIterations', 5);\n}\n\nexport function getDefaultModelConfig(): LLMConfig {\n  // Get selected provider and model from chat view settings\n  const selectedProvider = getConfig<string>('selectedProvider', 'ollama');\n  const selectedModel = getConfig<string>('selectedModel', 'llama3');\n\n  const defaultConfig: LLMConfig = {\n    provider: selectedProvider,\n    modelId: selectedModel,\n    options: {\n      temperature: 0.7,\n      maxTokens: 2000\n    }\n  };\n  return defaultConfig;\n}\n\n// Memory settings\nexport function getMemoryEnabled(): boolean {\n  return getConfig<boolean>('memory.enabled', true);\n}\n\nexport function getMaxMemories(): number {\n  return getConfig<number>('memory.maxMemories', 1000);\n}\n\nexport function getMemoryRelevanceThreshold(): number {\n  return getConfig<number>('memory.relevanceThreshold', 0.7);\n}\n\nexport function getMemoryContextWindowSize(): number {\n  return getConfig<number>('memory.contextWindowSize', 5);\n}\n\nexport function getConversationHistorySize(): number {\n  return getConfig<number>('memory.conversationHistorySize', 100);\n}\n\n// Provider configurations\nexport function getOpenAIApiKey(): string {\n  return getConfig<string>('providers.openai.apiKey', '');\n}\n\nexport function getOpenAIBaseUrl(): string {\n  const defaultUrl = 'https://api.openai.com/v1';\n  return getConfig<string>('providers.openai.baseUrl', defaultUrl);\n}\n\nexport function getOpenAIOrganization(): string {\n  return getConfig<string>('providers.openai.organization', '');\n}\n\nexport function getGoogleAIApiKey(): string {\n  return getConfig<string>('providers.googleai.apiKey', '');\n}\n\nexport function getMistralAIApiKey(): string {\n  return getConfig<string>('providers.mistralai.apiKey', '');\n}\n\nexport function getAnthropicApiKey(): string {\n  return getConfig<string>('providers.anthropic.apiKey', '');\n}\n\nexport function getOllamaBaseUrl(): string {\n  return getConfig<string>('providers.ollama.baseUrl', 'http://localhost:11434');\n}\n\nexport function getLMStudioBaseUrl(): string {\n  return getConfig<string>('providers.lmstudio.baseUrl', 'http://localhost:1234/v1');\n}\n\n// Agent configuration\nexport function getAgents(): AgentConfig[] {\n  return getConfig<AgentConfig[]>('agents', []);\n}\n\nexport async function saveAgents(agents: AgentConfig[]): Promise<boolean> {\n  return await setConfig('agents', agents);\n}\n\n// Prompt configuration\nexport function getSystemPrompts(): Record<string, string> {\n  return getConfig<Record<string, string>>('systemPrompts', {});\n}\n\nexport function getPromptVariables(): Record<string, string> {\n  return getConfig<Record<string, string>>('promptVariables', {});\n}\n\n// Advanced Features Settings\nexport function getGoddessMode(): GoddessSettings {\n  const defaultSettings: GoddessSettings = {\n    enabled: true,\n    adaptiveLevel: 85,\n    emotionalIntelligence: 90,\n    motivationalStyle: 'adaptive',\n    wisdomLevel: 95,\n    creativityLevel: 80,\n    empathyLevel: 85,\n    personalityAdaptation: true,\n    moodAnalysis: true,\n    contextualResponses: true,\n    divineGuidance: true\n  };\n  return getConfig<GoddessSettings>('advancedFeatures.goddessMode', defaultSettings);\n}\n\nexport function getQuantumAnalysis(): QuantumSettings {\n  const defaultSettings: QuantumSettings = {\n    enabled: true,\n    patternRecognition: true,\n    superpositionAnalysis: true,\n    entanglementDetection: true,\n    parallelUniverseTesting: true,\n    quantumInterference: true,\n    confidenceThreshold: 0.7,\n    maxParallelUniverses: 5,\n    quantumComplexityLevel: 'advanced'\n  };\n  return getConfig<QuantumSettings>('advancedFeatures.quantumAnalysis', defaultSettings);\n}\n\nexport function getNeuralSynthesis(): NeuralSettings {\n  const defaultSettings: NeuralSettings = {\n    enabled: true,\n    brainInspiredGeneration: true,\n    synapticConnections: true,\n    consciousnessAnalysis: true,\n    creativityBoost: true,\n    neuralLearning: true,\n    networkComplexity: 'medium',\n    learningRate: 0.01,\n    creativityThreshold: 0.7,\n    consciousnessLevel: 75\n  };\n  return getConfig<NeuralSettings>('advancedFeatures.neuralSynthesis', defaultSettings);\n}\n\nexport function getTimeTravelDebugging(): TimeTravelSettings {\n  const defaultSettings: TimeTravelSettings = {\n    enabled: true,\n    futureIssuePrediction: true,\n    alternativeTimelines: true,\n    mitigationStrategies: true,\n    temporalAnalysis: true,\n    predictionAccuracy: 'high',\n    timeHorizon: '3-months',\n    riskThreshold: 0.6,\n    maxPredictions: 10\n  };\n  return getConfig<TimeTravelSettings>('advancedFeatures.timeTravelDebugging', defaultSettings);\n}\n\nexport function getWorkflowEnhancements(): WorkflowEnhancementSettings {\n  const defaultSettings: WorkflowEnhancementSettings = {\n    advancedFeaturesEnabled: true,\n    autoEnhancement: true,\n    goddessGuidance: true,\n    quantumOptimization: true,\n    neuralInsights: true,\n    timeTravelPredictions: true,\n    adaptiveExecution: true,\n    performanceMetrics: true,\n    userSatisfactionTracking: true,\n    goddessRatingSystem: true\n  };\n  return getConfig<WorkflowEnhancementSettings>('advancedFeatures.workflowEnhancements', defaultSettings);\n}\n\nexport function getUIEnhancements(): UIEnhancementSettings {\n  const defaultSettings: UIEnhancementSettings = {\n    advancedTheme: true,\n    goddessAnimations: true,\n    quantumEffects: true,\n    neuralVisualizations: true,\n    timeTravelIndicators: true,\n    adaptiveInterface: true,\n    emotionalFeedback: true,\n    motivationalElements: true,\n    wisdomTooltips: true,\n    creativityIndicators: true\n  };\n  return getConfig<UIEnhancementSettings>('advancedFeatures.uiEnhancements', defaultSettings);\n}\n\nexport function getAdvancedAI(): AdvancedAISettings {\n  const defaultSettings: AdvancedAISettings = {\n    multiModalProcessing: true,\n    contextualUnderstanding: true,\n    emotionalIntelligence: true,\n    creativeThinking: true,\n    intuitiveProblemSolving: true,\n    adaptiveLearning: true,\n    consciousnessSimulation: true,\n    quantumInspiredAlgorithms: true,\n    neuralNetworkIntegration: true,\n    temporalReasoningCapabilities: true\n  };\n  return getConfig<AdvancedAISettings>('advancedFeatures.advancedAI', defaultSettings);\n}\n\n// Setter functions for advanced features\nexport async function setGoddessMode(settings: Partial<GoddessSettings>): Promise<boolean> {\n  return await setConfig('advancedFeatures.goddessMode', settings);\n}\n\nexport async function setQuantumAnalysis(settings: Partial<QuantumSettings>): Promise<boolean> {\n  return await setConfig('advancedFeatures.quantumAnalysis', settings);\n}\n\nexport async function setNeuralSynthesis(settings: Partial<NeuralSettings>): Promise<boolean> {\n  return await setConfig('advancedFeatures.neuralSynthesis', settings);\n}\n\nexport async function setTimeTravelDebugging(settings: Partial<TimeTravelSettings>): Promise<boolean> {\n  return await setConfig('advancedFeatures.timeTravelDebugging', settings);\n}\n\nexport async function setWorkflowEnhancements(settings: Partial<WorkflowEnhancementSettings>): Promise<boolean> {\n  return await setConfig('advancedFeatures.workflowEnhancements', settings);\n}\n\nexport async function setUIEnhancements(settings: Partial<UIEnhancementSettings>): Promise<boolean> {\n  return await setConfig('advancedFeatures.uiEnhancements', settings);\n}\n\nexport async function setAdvancedAI(settings: Partial<AdvancedAISettings>): Promise<boolean> {\n  return await setConfig('advancedFeatures.advancedAI', settings);\n}\n\n// Type definitions\nexport interface LLMConfig {\n    provider: string;\n    modelId: string;\n    options?: {\n        temperature?: number;\n        maxTokens?: number;\n        topP?: number;\n        frequencyPenalty?: number;\n        presencePenalty?: number;\n        stopSequences?: string[];\n        [key: string]: any;\n    };\n}\n\nexport interface AgentConfig {\n    id: string;\n    name: string;\n    description?: string;\n    systemPromptName: string;\n    llm?: LLMConfig;\n    tools?: string[];\n    isSupervisor?: boolean;\n    chainedAgentIds?: string[];\n    advancedFeatures?: {\n        goddessMode?: boolean;\n        quantumAnalysis?: boolean;\n        neuralSynthesis?: boolean;\n        timeTravelDebugging?: boolean;\n        adaptivePersonality?: boolean;\n    };\n}\n\nexport interface GoddessSettings {\n    enabled: boolean;\n    adaptiveLevel: number; // 0-100\n    emotionalIntelligence: number; // 0-100\n    motivationalStyle: 'encouraging' | 'wise' | 'playful' | 'supportive' | 'challenging' | 'adaptive';\n    wisdomLevel: number; // 0-100\n    creativityLevel: number; // 0-100\n    empathyLevel: number; // 0-100\n    personalityAdaptation: boolean;\n    moodAnalysis: boolean;\n    contextualResponses: boolean;\n    divineGuidance: boolean;\n}\n\nexport interface QuantumSettings {\n    enabled: boolean;\n    patternRecognition: boolean;\n    superpositionAnalysis: boolean;\n    entanglementDetection: boolean;\n    parallelUniverseTesting: boolean;\n    quantumInterference: boolean;\n    confidenceThreshold: number; // 0-1\n    maxParallelUniverses: number;\n    quantumComplexityLevel: 'basic' | 'intermediate' | 'advanced' | 'expert';\n}\n\nexport interface NeuralSettings {\n    enabled: boolean;\n    brainInspiredGeneration: boolean;\n    synapticConnections: boolean;\n    consciousnessAnalysis: boolean;\n    creativityBoost: boolean;\n    neuralLearning: boolean;\n    networkComplexity: 'low' | 'medium' | 'high' | 'ultra';\n    learningRate: number; // 0-1\n    creativityThreshold: number; // 0-1\n    consciousnessLevel: number; // 0-100\n}\n\nexport interface TimeTravelSettings {\n    enabled: boolean;\n    futureIssuePrediction: boolean;\n    alternativeTimelines: boolean;\n    mitigationStrategies: boolean;\n    temporalAnalysis: boolean;\n    predictionAccuracy: 'low' | 'medium' | 'high' | 'ultra';\n    timeHorizon: '1-week' | '1-month' | '3-months' | '6-months' | '1-year';\n    riskThreshold: number; // 0-1\n    maxPredictions: number;\n}\n\nexport interface WorkflowEnhancementSettings {\n    advancedFeaturesEnabled: boolean;\n    autoEnhancement: boolean;\n    goddessGuidance: boolean;\n    quantumOptimization: boolean;\n    neuralInsights: boolean;\n    timeTravelPredictions: boolean;\n    adaptiveExecution: boolean;\n    performanceMetrics: boolean;\n    userSatisfactionTracking: boolean;\n    goddessRatingSystem: boolean;\n}\n\nexport interface UIEnhancementSettings {\n    advancedTheme: boolean;\n    goddessAnimations: boolean;\n    quantumEffects: boolean;\n    neuralVisualizations: boolean;\n    timeTravelIndicators: boolean;\n    adaptiveInterface: boolean;\n    emotionalFeedback: boolean;\n    motivationalElements: boolean;\n    wisdomTooltips: boolean;\n    creativityIndicators: boolean;\n}\n\nexport interface AdvancedAISettings {\n    multiModalProcessing: boolean;\n    contextualUnderstanding: boolean;\n    emotionalIntelligence: boolean;\n    creativeThinking: boolean;\n    intuitiveProblemSolving: boolean;\n    adaptiveLearning: boolean;\n    consciousnessSimulation: boolean;\n    quantumInspiredAlgorithms: boolean;\n    neuralNetworkIntegration: boolean;\n    temporalReasoningCapabilities: boolean;\n}\n\n"]}