/**
 * Model Context Protocol (MCP) Workflow
 *
 * This module provides a workflow for the Model Context Protocol (MCP),
 * which is a standard for exchanging context between different AI models
 * and tools.
 */

import { StructuredTool } from './corePolyfill';
import { createReActWorkflow } from './templates';
import { GraphDefinition } from './types';
import { Agent } from '../agentUtilities/agent';

// Import the MCPManager from its new location
import { mcpManager } from '../../mcp';

/**
 * Creates an MCP workflow
 */
export function createMCPWorkflow(
  agent: Agent,
  options: {
    name?: string;
    description?: string;
    tools?: StructuredTool[];
  } = {}
): GraphDefinition {
  const name = options.name || 'MCP Workflow';
  const description = options.description || 'Workflow for Model Context Protocol integration';

  // Log that we're creating an MCP workflow
  mcpManager.addToolResult(
    'createMCPWorkflow',
    'Creating a new MCP workflow',
    { name, description },
    { workflowId: `mcp-workflow-${Date.now()}` }
  );

  // Create workflow
  return createReActWorkflow(
    `mcp-workflow-${Date.now()}`,
    name,
    description,
    agent,
    options.tools || []
  );
}
