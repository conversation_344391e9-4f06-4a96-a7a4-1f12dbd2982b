// Type definitions for VS Code mock

declare module 'vscode' {
  export interface Disposable {
    dispose(): any;
  }

  export interface Event<T> {
    (listener: (e: T) => any, thisArgs?: any, disposables?: Disposable[]): Disposable;
  }

  export interface OutputChannel extends Disposable {
    readonly name: string;
    append(value: string): void;
    appendLine(value: string): void;
    clear(): void;
    show(preserveFocus?: boolean): void;
    hide(): void;
  }

  export interface WorkspaceConfiguration {
    get<T>(section: string, defaultValue: T): T;
    get<T>(section: string): T | undefined;
    has(section: string): boolean;
    inspect<T>(section: string): {
      key: string;
      defaultValue?: T;
      globalValue?: T;
      workspaceValue?: T;
      workspaceFolderValue?: T;
    } | undefined;
    update(section: string, value: any, configurationTarget?: boolean | ConfigurationTarget): Thenable<void>;
  }

  export enum ConfigurationTarget {
    Global = 1,
    Workspace = 2,
    WorkspaceFolder = 3,
    Default = 0,
    Memory = 4,
    MemoryConfiguration = 5
  }

  export interface Progress<T> {
    report(value: T): void;
  }

  export interface CancellationToken {
    readonly isCancellationRequested: boolean;
    readonly onCancellationRequested: Event<any>;
  }

  export class CancellationTokenSource {
    token: CancellationToken;
    cancel(): void;
    dispose(): void;
  }

  export interface Uri {
    readonly scheme: string;
    readonly authority: string;
    readonly path: string;
    readonly query: string;
    readonly fragment: string;
    readonly fsPath: string;
    with(change: {
      scheme?: string;
      authority?: string | null;
      path?: string | null;
      query?: string | null;
      fragment?: string | null;
    }): Uri;
    toString(skipEncoding?: boolean): string;
    toJSON(): any;
  }

  export interface TextDocument {
    readonly uri: Uri;
    readonly fileName: string;
    readonly isUntitled: boolean;
    readonly languageId: string;
    readonly version: number;
    readonly isDirty: boolean;
    readonly isClosed: boolean;
    save(): Thenable<boolean>;
    getText(range?: any): string;
    lineCount: number;
  }

  export interface TextEditor {
    readonly document: TextDocument;
    selection: any;
    selections: any[];
    visibleRanges: any[];
    viewColumn?: number;
  }

  export enum ProgressLocation {
    SourceControl = 1,
    Window = 10,
    Notification = 15
  }

  export interface ProgressOptions {
    location: ProgressLocation | { viewId: string };
    title?: string;
    cancellable?: boolean;
  }

  export interface Window {
    showInformationMessage(message: string, ...items: string[]): Thenable<string | undefined>;
    showWarningMessage(message: string, ...items: string[]): Thenable<string | undefined>;
    showErrorMessage(message: string, ...items: string[]): Thenable<string | undefined>;
    showQuickPick(items: string[] | Thenable<string[]>, options?: any): Thenable<string | undefined>;
    showInputBox(options?: any): Thenable<string | undefined>;
    createOutputChannel(name: string): OutputChannel;
    withProgress<R>(options: ProgressOptions, task: (progress: Progress<{ message?: string; increment?: number }>, token: CancellationToken) => Thenable<R>): Thenable<R>;
    onDidChangeActiveTextEditor: Event<TextEditor | undefined>;
    activeTextEditor?: TextEditor;
    visibleTextEditors: TextEditor[];
  }

  export interface Workspace {
    workspaceFolders?: { uri: Uri; name: string; index: number }[];
    getConfiguration(section?: string): WorkspaceConfiguration;
    openTextDocument(uri: Uri): Thenable<TextDocument>;
    openTextDocument(fileName: string): Thenable<TextDocument>;
    onDidChangeWorkspaceFolders: Event<any>;
    onDidChangeTextDocument: Event<any>;
    onDidOpenTextDocument: Event<TextDocument>;
    onDidCloseTextDocument: Event<TextDocument>;
    onDidSaveTextDocument: Event<TextDocument>;
    onWillSaveTextDocument: Event<any>;
    onDidChangeConfiguration: Event<any>;
    fs: {
      readFile(uri: Uri): Thenable<Uint8Array>;
      writeFile(uri: Uri, content: Uint8Array): Thenable<void>;
      stat(uri: Uri): Thenable<{ type: number; ctime: number; mtime: number; size: number }>;
      readDirectory(uri: Uri): Thenable<[string, number][]>;
    };
    findFiles(include: string, exclude?: string, maxResults?: number, token?: CancellationToken): Thenable<Uri[]>;
    asRelativePath(pathOrUri: string | Uri, includeWorkspaceFolder?: boolean): string;
    createFileSystemWatcher(globPattern: string, ignoreCreateEvents?: boolean, ignoreChangeEvents?: boolean, ignoreDeleteEvents?: boolean): any;
  }

  export interface Extension<T> {
    readonly id: string;
    readonly extensionPath: string;
    readonly isActive: boolean;
    readonly packageJSON: any;
    readonly exports: T;
    activate(): Thenable<T>;
  }

  export interface ExtensionContext {
    readonly extension: Extension<any>;
    readonly extensionPath: string;
    readonly globalState: any;
    readonly globalStoragePath: string;
    readonly logPath: string;
    readonly storagePath: string | undefined;
    readonly storageUri: Uri | undefined;
    readonly subscriptions: { dispose(): any }[];
    readonly workspaceState: any;
    asAbsolutePath(relativePath: string): string;
  }

  export interface ExtensionContext {
    readonly extension: Extension<any>;
    readonly extensionPath: string;
    readonly globalState: any;
    readonly globalStoragePath: string;
    readonly logPath: string;
    readonly storagePath: string | undefined;
    readonly storageUri: Uri | undefined;
    readonly subscriptions: { dispose(): any }[];
    readonly workspaceState: any;
    asAbsolutePath(relativePath: string): string;
  }

  export interface Extension<T> {
    readonly id: string;
    readonly extensionPath: string;
    readonly isActive: boolean;
    readonly packageJSON: any;
    readonly exports: T;
    activate(): Thenable<T>;
  }

  export interface Env {
    readonly appName: string;
    readonly appRoot: string;
    readonly language: string;
    readonly machineId: string;
    readonly remoteName: string | undefined;
    readonly sessionId: string;
    readonly shell: string;
    readonly uriScheme: string;
    readonly appHost: string;
    clipboard: {
      readText(): Thenable<string>;
      writeText(value: string): Thenable<void>;
    };
    asExternalUri(target: Uri): Thenable<Uri>;
    openExternal(target: Uri): Thenable<boolean>;
  }

  export interface Commands {
    executeCommand<T = any>(command: string, ...args: any[]): Thenable<T | undefined>;
    registerCommand(command: string, callback: (...args: any[]) => any, thisArg?: any): Disposable;
    registerTextEditorCommand(command: string, callback: (textEditor: TextEditor, edit: any, ...args: any[]) => void, thisArg?: any): Disposable;
  }

  export const version: string;
  export const env: Env;
  export const window: Window;
  export const workspace: Workspace;
  export const commands: Commands;
  export const extensions: {
    getExtension(extensionId: string): Extension<any> | undefined;
    all: Extension<any>[];
    onDidChange: Event<void>;
  };

  export function Uri: {
    file(path: string): Uri;
    parse(value: string): Uri;
    joinPath(base: Uri, ...pathSegments: string[]): Uri;
  };

  export function Disposable: {
    from(...disposableLikes: { dispose: () => any }[]): Disposable;
  };
}
