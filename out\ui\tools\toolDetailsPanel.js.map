{"version": 3, "file": "toolDetailsPanel.js", "sourceRoot": "", "sources": ["../../../src/ui/tools/toolDetailsPanel.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAGjC,MAAa,gBAAgB;IACpB,MAAM,CAAC,YAAY,CAA+B;IACxC,MAAM,CAAsB;IAC5B,aAAa,CAAa;IAC1B,KAAK,CAAQ;IAEvB,MAAM,CAAU,QAAQ,GAAG,qBAAqB,CAAC;IAExD,YAAoB,KAA0B,EAAE,YAAwB,EAAE,IAAW;QACnF,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;IACvD,CAAC;IAEM,MAAM,CAAC,YAAY,CAAC,YAAwB,EAAE,IAAW;QAC9D,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC;QACtG,IAAI,gBAAgB,CAAC,YAAY,EAAE,CAAC;YAClC,gBAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACpD,gBAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,gBAAgB,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;YACvG,OAAO;QACT,CAAC;QACD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC5C,gBAAgB,CAAC,QAAQ,EACzB,iBAAiB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE,EAAE,EACvC,MAAM,IAAI,MAAM,CAAC,UAAU,CAAC,GAAG,EAC/B,EAAE,aAAa,EAAE,IAAI,EAAE,CACxB,CAAC;QACF,gBAAgB,CAAC,YAAY,GAAG,IAAI,gBAAgB,CAAC,KAAK,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;IAClF,CAAC;IAEO,kBAAkB;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QACxB,OAAO;;;;2BAIgB,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE;;;;;;;;;;;;6EAY8B,IAAI,CAAC,EAAE;+EACL,IAAI,CAAC,IAAI,IAAI,KAAK;sFACX,IAAI,CAAC,WAAW,IAAI,0BAA0B;+EACrD,IAAI,CAAC,IAAI,IAAI,KAAK;kFACf,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM;kEAC5E,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM;;QAErH,CAAC;IACP,CAAC;;AAzDH,4CA0DC", "sourcesContent": ["import * as vscode from 'vscode';\nimport { ITool } from '../../tools/tool.ts.backup';\n\nexport class ToolDetailsPanel {\n  public static currentPanel: ToolDetailsPanel | undefined;\n  private readonly _panel: vscode.WebviewPanel;\n  private readonly _extensionUri: vscode.Uri;\n  private readonly _tool: ITool;\n\n  public static readonly viewType = 'codessa.toolDetails';\n\n  private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri, tool: ITool) {\n    this._panel = panel;\n    this._extensionUri = extensionUri;\n    this._tool = tool;\n    this._panel.webview.html = this._getHtmlForWebview();\n  }\n\n  public static createOrShow(extensionUri: vscode.Uri, tool: ITool) {\n    const column = vscode.window.activeTextEditor ? vscode.window.activeTextEditor.viewColumn : undefined;\n    if (ToolDetailsPanel.currentPanel) {\n      ToolDetailsPanel.currentPanel._panel.reveal(column);\n      ToolDetailsPanel.currentPanel._panel.webview.html = ToolDetailsPanel.currentPanel._getHtmlForWebview();\n      return;\n    }\n    const panel = vscode.window.createWebviewPanel(\n      ToolDetailsPanel.viewType,\n      `Tool Details: ${tool.name || tool.id}`,\n      column || vscode.ViewColumn.One,\n      { enableScripts: true }\n    );\n    ToolDetailsPanel.currentPanel = new ToolDetailsPanel(panel, extensionUri, tool);\n  }\n\n  private _getHtmlForWebview(): string {\n    const tool = this._tool;\n    return `<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <title>Tool Details: ${tool.name || tool.id}</title>\n    <style>\n        body { font-family: sans-serif; padding: 16px; }\n        h1 { font-size: 1.5em; }\n        .section { margin-bottom: 1em; }\n        .label { font-weight: bold; }\n        .value { margin-left: 0.5em; }\n        pre { background: #f4f4f4; padding: 8px; border-radius: 4px; }\n    </style>\n</head>\n<body>\n    <h1>Tool Details</h1>\n    <div class=\"section\"><span class=\"label\">ID:</span><span class=\"value\">${tool.id}</span></div>\n    <div class=\"section\"><span class=\"label\">Name:</span><span class=\"value\">${tool.name || 'N/A'}</span></div>\n    <div class=\"section\"><span class=\"label\">Description:</span><span class=\"value\">${tool.description || 'No description provided.'}</span></div>\n    <div class=\"section\"><span class=\"label\">Type:</span><span class=\"value\">${tool.type || 'N/A'}</span></div>\n    <div class=\"section\"><span class=\"label\">Actions:</span><span class=\"value\">${tool.actions ? Object.keys(tool.actions).join(', ') : 'None'}</span></div>\n    <div class=\"section\"><span class=\"label\">Config:</span><pre>${tool.config ? JSON.stringify(tool.config, null, 2) : 'None'}</pre></div>\n</body>\n</html>`;\n  }\n}\n"]}