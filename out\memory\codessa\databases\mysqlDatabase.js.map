{"version": 3, "file": "mysqlDatabase.js", "sourceRoot": "", "sources": ["../../../../src/memory/codessa/databases/mysqlDatabase.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,4CAAyC;AACzC,4CAA4C;AAC5C,sDAAwC;AAExC;;GAEG;AACH,MAAa,aAAa;IAChB,IAAI,CAAyB;IAC7B,IAAI,CAAS;IACb,IAAI,CAAS;IACb,IAAI,CAAS;IACb,QAAQ,CAAS;IACjB,QAAQ,CAAS;IACjB,KAAK,CAAS;IACd,WAAW,GAAG,KAAK,CAAC;IAE5B;QACE,IAAI,CAAC,IAAI,GAAG,IAAA,kBAAS,EAAS,4BAA4B,EAAE,WAAW,CAAC,CAAC;QACzE,IAAI,CAAC,IAAI,GAAG,IAAA,kBAAS,EAAS,4BAA4B,EAAE,IAAI,CAAC,CAAC;QAClE,IAAI,CAAC,IAAI,GAAG,IAAA,kBAAS,EAAS,4BAA4B,EAAE,MAAM,CAAC,CAAC;QACpE,IAAI,CAAC,QAAQ,GAAG,IAAA,kBAAS,EAAS,gCAAgC,EAAE,EAAE,CAAC,CAAC;QACxE,IAAI,CAAC,QAAQ,GAAG,IAAA,kBAAS,EAAS,gCAAgC,EAAE,SAAS,CAAC,CAAC;QAC/E,IAAI,CAAC,KAAK,GAAG,IAAA,kBAAS,EAAS,6BAA6B,EAAE,UAAU,CAAC,CAAC;IAC5E,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,UAAU;QACrB,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;YACxD,CAAC;YAED,yBAAyB;YACzB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,UAAU,CAAC;gBAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,kBAAkB,EAAE,IAAI;gBACxB,eAAe,EAAE,EAAE;gBACnB,UAAU,EAAE,CAAC;aACd,CAAC,CAAC;YAEH,kBAAkB;YAClB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YAEnD,IAAI,CAAC;gBACH,gBAAgB;gBAChB,MAAM,UAAU,CAAC,KAAK,CAAC;iDACkB,IAAI,CAAC,KAAK;;;;;;iBAM1C,CAAC,CAAC;gBAEX,MAAM,UAAU,CAAC,KAAK,CAAC;iDACkB,IAAI,CAAC,KAAK;;;;6DAIE,IAAI,CAAC,KAAK;;iBAEtD,CAAC,CAAC;gBAEX,iBAAiB;gBACjB,MAAM,UAAU,CAAC,KAAK,CAAC;qDACsB,IAAI,CAAC,KAAK,iBAAiB,IAAI,CAAC,KAAK;iBACzE,CAAC,CAAC;gBAEX,MAAM,UAAU,CAAC,KAAK,CAAC;qDACsB,IAAI,CAAC,KAAK,gBAAgB,IAAI,CAAC,KAAK;iBACxE,CAAC,CAAC;gBAEX,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,eAAM,CAAC,IAAI,CAAC,yDAAyD,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACxF,CAAC;oBAAS,CAAC;gBACT,UAAU,CAAC,OAAO,EAAE,CAAC;YACvB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC5D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,MAA+B;QACxE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QAEnD,IAAI,CAAC;YACH,oCAAoC;YACpC,MAAM,EAAE,GAAG,MAAM,CAAC,EAAY,CAAC;YAC/B,MAAM,OAAO,GAAG,MAAM,CAAC,OAAiB,CAAC;YACzC,MAAM,SAAS,GAAG,MAAM,CAAC,SAAmB,CAAC;YAC7C,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAmC,CAAC;YAE5D,gBAAgB;YAChB,MAAM,UAAU,CAAC,KAAK,CACpB,eAAe,IAAI,CAAC,KAAK;;;;;4CAKW,EACpC,CAAC,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CACnD,CAAC;YAEF,yBAAyB;YACzB,IAAI,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnD,6BAA6B;gBAC7B,MAAM,UAAU,CAAC,KAAK,CACpB,eAAe,IAAI,CAAC,KAAK,2BAA2B,EACpD,CAAC,EAAE,CAAC,CACL,CAAC;gBAEF,kBAAkB;gBAClB,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;oBAChC,MAAM,UAAU,CAAC,KAAK,CACpB,eAAe,IAAI,CAAC,KAAK,sCAAsC,EAC/D,CAAC,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAClB,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,kBAAkB,UAAU,EAAE,CAAC,CAAC;YACvE,OAAO,EAAE,CAAC;QACZ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACzE,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,UAAU,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,EAAU;QACnD,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QAEnD,IAAI,CAAC;YACH,aAAa;YACb,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,UAAU,CAAC,KAAK,CACnC,iBAAiB,IAAI,CAAC,KAAK,eAAe,EAC1C,CAAC,EAAE,CAAC,CACL,CAAC;YAEF,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC3C,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAA4B,CAAC;gBAElD,iBAAiB;gBACjB,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAkB,CAAC,CAAC;gBAExD,WAAW;gBACX,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,UAAU,CAAC,KAAK,CACtC,mBAAmB,IAAI,CAAC,KAAK,2BAA2B,EACxD,CAAC,EAAE,CAAC,CACL,CAAC;gBAEF,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACjD,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAmC,CAAC;oBAC5D,QAAQ,CAAC,IAAI,GAAI,OAAqC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAa,CAAC,CAAC;gBACvF,CAAC;gBAED,OAAO,MAAM,CAAC;YAChB,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,oBAAoB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACjF,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,UAAU,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,EAAU,EAAE,MAA+B;QACvF,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QAEnD,IAAI,CAAC;YACH,oCAAoC;YACpC,MAAM,OAAO,GAAG,MAAM,CAAC,OAAiB,CAAC;YACzC,MAAM,SAAS,GAAG,MAAM,CAAC,SAAmB,CAAC;YAC7C,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAmC,CAAC;YAE5D,gBAAgB;YAChB,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,UAAU,CAAC,KAAK,CACrC,UAAU,IAAI,CAAC,KAAK,4DAA4D,EAChF,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,CACnD,CAAC;YAEF,MAAM,YAAY,GAAG,MAA+B,CAAC;YAErD,yBAAyB;YACzB,IAAI,QAAQ,EAAE,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnD,uBAAuB;gBACvB,MAAM,UAAU,CAAC,KAAK,CACpB,eAAe,IAAI,CAAC,KAAK,2BAA2B,EACpD,CAAC,EAAE,CAAC,CACL,CAAC;gBAEF,kBAAkB;gBAClB,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;oBAChC,MAAM,UAAU,CAAC,KAAK,CACpB,eAAe,IAAI,CAAC,KAAK,sCAAsC,EAC/D,CAAC,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAClB,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,kBAAkB,UAAU,EAAE,CAAC,CAAC;YACzE,OAAO,YAAY,CAAC,YAAY,GAAG,CAAC,CAAC;QACvC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,kBAAkB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAClF,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,UAAU,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,EAAU;QACtD,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QAEnD,IAAI,CAAC;YACH,gBAAgB;YAChB,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,UAAU,CAAC,KAAK,CACrC,eAAe,IAAI,CAAC,KAAK,eAAe,EACxC,CAAC,EAAE,CAAC,CACL,CAAC;YAEF,MAAM,YAAY,GAAG,MAA+B,CAAC;YAErD,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,oBAAoB,UAAU,EAAE,CAAC,CAAC;YAC3E,OAAO,YAAY,CAAC,YAAY,GAAG,CAAC,CAAC;QACvC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,oBAAoB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACpF,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,UAAU,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,KAA8B,EAAE,KAAc;QAC1F,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QAEnD,IAAI,CAAC;YACH,cAAc;YACd,IAAI,GAAG,GAAG,iBAAiB,IAAI,CAAC,KAAK,EAAE,CAAC;YACxC,MAAM,MAAM,GAAc,EAAE,CAAC;YAC7B,MAAM,UAAU,GAAa,EAAE,CAAC;YAEhC,iBAAiB;YACjB,IAAI,KAAK,CAAC,EAAE,EAAE,CAAC;gBACb,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAC1B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACxB,CAAC;YAED,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;gBACxB,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAClC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YACnC,CAAC;YAED,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;gBACtB,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAClC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YACjC,CAAC;YAED,uCAAuC;YACvC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC1B,GAAG,IAAI,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC9C,CAAC;YAED,eAAe;YACf,GAAG,IAAI,0BAA0B,CAAC;YAElC,YAAY;YACZ,IAAI,KAAK,EAAE,CAAC;gBACV,GAAG,IAAI,UAAU,CAAC;gBAClB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrB,CAAC;YAED,gBAAgB;YAChB,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YAEnD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzB,OAAO,EAAE,CAAC;YACZ,CAAC;YAED,kBAAkB;YAClB,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAE,IAAkC,CAAC,GAAG,CAAC,KAAK,EAAE,GAA4B,EAAE,EAAE;gBAC/G,iBAAiB;gBACjB,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAkB,CAAC,CAAC;gBAElD,WAAW;gBACX,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,UAAU,CAAC,KAAK,CACtC,mBAAmB,IAAI,CAAC,KAAK,2BAA2B,EACxD,CAAC,GAAG,CAAC,EAAE,CAAC,CACT,CAAC;gBAEF,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACjD,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAmC,CAAC;oBACzD,QAAQ,CAAC,IAAI,GAAI,OAAqC,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,GAAa,CAAC,CAAC;gBAC7F,CAAC;gBAED,OAAO,GAAG,CAAC;YACb,CAAC,CAAC,CAAC,CAAC;YAEJ,8BAA8B;YAC9B,MAAM,SAAS,GAAG,KAAK,CAAC,IAA4B,CAAC;YACrD,IAAI,SAAS,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClE,OAAO,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;oBAC7B,MAAM,cAAc,GAAG,MAAM,CAAC,QAAmC,CAAC;oBAClE,MAAM,UAAU,GAAI,cAAc,CAAC,IAAiB,IAAI,EAAE,CAAC;oBAC3D,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,GAAW,EAAE,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;gBACnE,CAAC,CAAC,CAAC;YACL,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2CAA2C,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9E,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,UAAU,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,eAAe,CAAC,UAAkB;QAC7C,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACpC,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,UAAU,KAAK,UAAU,EAAE,CAAC;YAC9B,MAAM,IAAI,KAAK,CAAC,cAAc,UAAU,gBAAgB,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QAEnD,IAAI,CAAC;YACH,wBAAwB;YACxB,MAAM,UAAU,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,KAAK,OAAO,CAAC,CAAC;YAEzD,qBAAqB;YACrB,MAAM,UAAU,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YAEpD,eAAM,CAAC,KAAK,CAAC,uCAAuC,UAAU,EAAE,CAAC,CAAC;QACpE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACjE,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,UAAU,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC;IACH,CAAC;CACF;AApZD,sCAoZC", "sourcesContent": ["import type { IDatabase } from '../../types';\nimport { logger } from '../../../logger';\nimport { getConfig } from '../../../config';\nimport * as mysql from 'mysql2/promise';\n\n/**\n * MySQL database implementation\n */\nexport class MySQLDatabase implements IDatabase {\n  private pool: mysql.Pool | undefined;\n  private host: string;\n  private port: number;\n  private user: string;\n  private password: string;\n  private database: string;\n  private table: string;\n  private initialized = false;\n\n  constructor() {\n    this.host = getConfig<string>('memory.database.mysql.host', 'localhost');\n    this.port = getConfig<number>('memory.database.mysql.port', 3306);\n    this.user = getConfig<string>('memory.database.mysql.user', 'root');\n    this.password = getConfig<string>('memory.database.mysql.password', '');\n    this.database = getConfig<string>('memory.database.mysql.database', 'codessa');\n    this.table = getConfig<string>('memory.database.mysql.table', 'memories');\n  }\n\n  /**\n     * Initialize the database\n     */\n  public async initialize(): Promise<void> {\n    try {\n      if (!this.database) {\n        throw new Error('MySQL database name not configured');\n      }\n\n      // Create connection pool\n      this.pool = mysql.createPool({\n        host: this.host,\n        port: this.port,\n        user: this.user,\n        password: this.password,\n        database: this.database,\n        waitForConnections: true,\n        connectionLimit: 10,\n        queueLimit: 0\n      });\n\n      // Test connection\n      const connection = await this.pool.getConnection();\n\n      try {\n        // Create tables\n        await connection.query(`\n                    CREATE TABLE IF NOT EXISTS ${this.table} (\n                        id VARCHAR(255) PRIMARY KEY,\n                        content TEXT NOT NULL,\n                        timestamp BIGINT NOT NULL,\n                        metadata JSON NOT NULL\n                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci\n                `);\n\n        await connection.query(`\n                    CREATE TABLE IF NOT EXISTS ${this.table}_tags (\n                        memory_id VARCHAR(255) NOT NULL,\n                        tag VARCHAR(255) NOT NULL,\n                        PRIMARY KEY (memory_id, tag),\n                        FOREIGN KEY (memory_id) REFERENCES ${this.table}(id) ON DELETE CASCADE\n                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci\n                `);\n\n        // Create indexes\n        await connection.query(`\n                    CREATE INDEX IF NOT EXISTS idx_${this.table}_timestamp ON ${this.table}(timestamp)\n                `);\n\n        await connection.query(`\n                    CREATE INDEX IF NOT EXISTS idx_${this.table}_tags_tag ON ${this.table}_tags(tag)\n                `);\n\n        this.initialized = true;\n        logger.info(`MySQL database initialized successfully with database ${this.database}`);\n      } finally {\n        connection.release();\n      }\n    } catch (error) {\n      logger.error('Failed to initialize MySQL database:', error);\n      throw error;\n    }\n  }\n\n  /**\n     * Add a record\n     */\n  public async addRecord(collection: string, record: Record<string, unknown>): Promise<string> {\n    if (!this.pool || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    if (collection !== 'memories') {\n      throw new Error(`Collection ${collection} not supported`);\n    }\n\n    const connection = await this.pool.getConnection();\n\n    try {\n      // Extract fields with proper typing\n      const id = record.id as string;\n      const content = record.content as string;\n      const timestamp = record.timestamp as number;\n      const metadata = record.metadata as Record<string, unknown>;\n\n      // Insert record\n      await connection.query(\n        `INSERT INTO ${this.table} (id, content, timestamp, metadata)\n                VALUES (?, ?, ?, ?)\n                ON DUPLICATE KEY UPDATE\n                content = VALUES(content),\n                timestamp = VALUES(timestamp),\n                metadata = VALUES(metadata)`,\n        [id, content, timestamp, JSON.stringify(metadata)]\n      );\n\n      // Insert tags if present\n      if (metadata?.tags && Array.isArray(metadata.tags)) {\n        // Delete existing tags first\n        await connection.query(\n          `DELETE FROM ${this.table}_tags WHERE memory_id = ?`,\n          [id]\n        );\n\n        // Insert new tags\n        for (const tag of metadata.tags) {\n          await connection.query(\n            `INSERT INTO ${this.table}_tags (memory_id, tag) VALUES (?, ?)`,\n            [id, String(tag)]\n          );\n        }\n      }\n\n      logger.debug(`Added record with ID ${id} to collection ${collection}`);\n      return id;\n    } catch (error) {\n      logger.error(`Failed to add record to collection ${collection}:`, error);\n      throw error;\n    } finally {\n      connection.release();\n    }\n  }\n\n  /**\n     * Get a record by ID\n     */\n  public async getRecord(collection: string, id: string): Promise<Record<string, unknown> | undefined> {\n    if (!this.pool || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    if (collection !== 'memories') {\n      throw new Error(`Collection ${collection} not supported`);\n    }\n\n    const connection = await this.pool.getConnection();\n\n    try {\n      // Get record\n      const [rows] = await connection.query(\n        `SELECT * FROM ${this.table} WHERE id = ?`,\n        [id]\n      );\n\n      if (Array.isArray(rows) && rows.length > 0) {\n        const record = rows[0] as Record<string, unknown>;\n\n        // Parse metadata\n        record.metadata = JSON.parse(record.metadata as string);\n\n        // Get tags\n        const [tagRows] = await connection.query(\n          `SELECT tag FROM ${this.table}_tags WHERE memory_id = ?`,\n          [id]\n        );\n\n        if (Array.isArray(tagRows) && tagRows.length > 0) {\n          const metadata = record.metadata as Record<string, unknown>;\n          metadata.tags = (tagRows as Record<string, unknown>[]).map(row => row.tag as string);\n        }\n\n        return record;\n      }\n\n      return undefined;\n    } catch (error) {\n      logger.error(`Failed to get record ${id} from collection ${collection}:`, error);\n      throw error;\n    } finally {\n      connection.release();\n    }\n  }\n\n  /**\n     * Update a record\n     */\n  public async updateRecord(collection: string, id: string, record: Record<string, unknown>): Promise<boolean> {\n    if (!this.pool || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    if (collection !== 'memories') {\n      throw new Error(`Collection ${collection} not supported`);\n    }\n\n    const connection = await this.pool.getConnection();\n\n    try {\n      // Extract fields with proper typing\n      const content = record.content as string;\n      const timestamp = record.timestamp as number;\n      const metadata = record.metadata as Record<string, unknown>;\n\n      // Update record\n      const [result] = await connection.query(\n        `UPDATE ${this.table} SET content = ?, timestamp = ?, metadata = ? WHERE id = ?`,\n        [content, timestamp, JSON.stringify(metadata), id]\n      );\n\n      const updateResult = result as mysql.ResultSetHeader;\n\n      // Update tags if present\n      if (metadata?.tags && Array.isArray(metadata.tags)) {\n        // Delete existing tags\n        await connection.query(\n          `DELETE FROM ${this.table}_tags WHERE memory_id = ?`,\n          [id]\n        );\n\n        // Insert new tags\n        for (const tag of metadata.tags) {\n          await connection.query(\n            `INSERT INTO ${this.table}_tags (memory_id, tag) VALUES (?, ?)`,\n            [id, String(tag)]\n          );\n        }\n      }\n\n      logger.debug(`Updated record with ID ${id} in collection ${collection}`);\n      return updateResult.affectedRows > 0;\n    } catch (error) {\n      logger.error(`Failed to update record ${id} in collection ${collection}:`, error);\n      throw error;\n    } finally {\n      connection.release();\n    }\n  }\n\n  /**\n     * Delete a record\n     */\n  public async deleteRecord(collection: string, id: string): Promise<boolean> {\n    if (!this.pool || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    if (collection !== 'memories') {\n      throw new Error(`Collection ${collection} not supported`);\n    }\n\n    const connection = await this.pool.getConnection();\n\n    try {\n      // Delete record\n      const [result] = await connection.query(\n        `DELETE FROM ${this.table} WHERE id = ?`,\n        [id]\n      );\n\n      const deleteResult = result as mysql.ResultSetHeader;\n\n      logger.debug(`Deleted record with ID ${id} from collection ${collection}`);\n      return deleteResult.affectedRows > 0;\n    } catch (error) {\n      logger.error(`Failed to delete record ${id} from collection ${collection}:`, error);\n      throw error;\n    } finally {\n      connection.release();\n    }\n  }\n\n  /**\n     * Query records\n     */\n  public async queryRecords(collection: string, query: Record<string, unknown>, limit?: number): Promise<Record<string, unknown>[]> {\n    if (!this.pool || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    if (collection !== 'memories') {\n      throw new Error(`Collection ${collection} not supported`);\n    }\n\n    const connection = await this.pool.getConnection();\n\n    try {\n      // Build query\n      let sql = `SELECT * FROM ${this.table}`;\n      const params: unknown[] = [];\n      const conditions: string[] = [];\n\n      // Add conditions\n      if (query.id) {\n        conditions.push('id = ?');\n        params.push(query.id);\n      }\n\n      if (query.fromTimestamp) {\n        conditions.push('timestamp >= ?');\n        params.push(query.fromTimestamp);\n      }\n\n      if (query.toTimestamp) {\n        conditions.push('timestamp <= ?');\n        params.push(query.toTimestamp);\n      }\n\n      // Add WHERE clause if conditions exist\n      if (conditions.length > 0) {\n        sql += ' WHERE ' + conditions.join(' AND ');\n      }\n\n      // Add ORDER BY\n      sql += ' ORDER BY timestamp DESC';\n\n      // Add LIMIT\n      if (limit) {\n        sql += ' LIMIT ?';\n        params.push(limit);\n      }\n\n      // Execute query\n      const [rows] = await connection.query(sql, params);\n\n      if (!Array.isArray(rows)) {\n        return [];\n      }\n\n      // Process results\n      const records = await Promise.all((rows as Record<string, unknown>[]).map(async (row: Record<string, unknown>) => {\n        // Parse metadata\n        row.metadata = JSON.parse(row.metadata as string);\n\n        // Get tags\n        const [tagRows] = await connection.query(\n          `SELECT tag FROM ${this.table}_tags WHERE memory_id = ?`,\n          [row.id]\n        );\n\n        if (Array.isArray(tagRows) && tagRows.length > 0) {\n          const metadata = row.metadata as Record<string, unknown>;\n          metadata.tags = (tagRows as Record<string, unknown>[]).map(tagRow => tagRow.tag as string);\n        }\n\n        return row;\n      }));\n\n      // Filter by tags if specified\n      const queryTags = query.tags as string[] | undefined;\n      if (queryTags && Array.isArray(queryTags) && queryTags.length > 0) {\n        return records.filter(record => {\n          const recordMetadata = record.metadata as Record<string, unknown>;\n          const recordTags = (recordMetadata.tags as string[]) || [];\n          return queryTags.some((tag: string) => recordTags.includes(tag));\n        });\n      }\n\n      return records;\n    } catch (error) {\n      logger.error(`Failed to query records from collection ${collection}:`, error);\n      throw error;\n    } finally {\n      connection.release();\n    }\n  }\n\n  /**\n     * Clear all records in a collection\n     */\n  public async clearCollection(collection: string): Promise<void> {\n    if (!this.pool || !this.initialized) {\n      throw new Error('Database not initialized');\n    }\n\n    if (collection !== 'memories') {\n      throw new Error(`Collection ${collection} not supported`);\n    }\n\n    const connection = await this.pool.getConnection();\n\n    try {\n      // Delete all tags first\n      await connection.query(`DELETE FROM ${this.table}_tags`);\n\n      // Delete all records\n      await connection.query(`DELETE FROM ${this.table}`);\n\n      logger.debug(`Cleared all records from collection ${collection}`);\n    } catch (error) {\n      logger.error(`Failed to clear collection ${collection}:`, error);\n      throw error;\n    } finally {\n      connection.release();\n    }\n  }\n}\n"]}