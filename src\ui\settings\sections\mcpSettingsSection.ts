// MCP Settings Section
import { showModal } from '../components/modal';

export function renderMCPSettingsSection(container: HTMLElement, settings: any) {
  container.innerHTML = `
        <div class="settings-section">
            <h2 class="settings-section-title">MCP Settings</h2>
            <div class="settings-group">
                <label for="mcp-endpoint">MCP Endpoint</label>
                <input id="mcp-endpoint" type="text" value="${settings.mcpEndpoint || ''}" placeholder="https://mcp.example.com/api" />
            </div>
            <div class="settings-group">
                <label for="mcp-api-key">API Key</label>
                <input id="mcp-api-key" type="password" value="${settings.mcpApiKey || ''}" placeholder="Enter your API key" />
            </div>
            <div class="settings-group">
                <label for="mcp-timeout">Timeout (seconds)</label>
                <input id="mcp-timeout" type="number" min="1" max="120" value="${settings.mcpTimeout || 30}" />
            </div>
            <button id="mcp-save-btn" class="settings-save-btn">Save MCP Settings</button>
        </div>
    `;

  container.querySelector('#mcp-save-btn')?.addEventListener('click', () => {
    const endpoint = (container.querySelector('#mcp-endpoint') as HTMLInputElement).value;
    const apiKey = (container.querySelector('#mcp-api-key') as HTMLInputElement).value;
    const timeout = parseInt((container.querySelector('#mcp-timeout') as HTMLInputElement).value, 10);
    settings.mcpEndpoint = endpoint;
    settings.mcpApiKey = apiKey;
    settings.mcpTimeout = timeout;
    showModal({
      title: 'MCP Settings Saved',
      content: 'Your MCP settings have been updated.',
      onConfirm: () => {}
    });
  });
}
