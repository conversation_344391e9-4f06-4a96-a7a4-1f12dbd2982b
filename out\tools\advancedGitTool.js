"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GitBranchGraphTool = exports.GitTagTool = exports.GitRebaseTool = exports.GitCherryPickTool = exports.GitRevertTool = exports.GitStashTool = void 0;
const vscode = __importStar(require("vscode"));
const cp = __importStar(require("child_process"));
const zod_1 = require("zod");
class GitStashTool {
    id = 'stash';
    name = 'Git Stash';
    description = 'Stash changes in the current repository.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({
        args: zod_1.z.string().optional().describe('Additional arguments for git stash.')
    });
    inputSchema = {
        type: 'object',
        properties: {
            args: { type: 'string', description: 'Additional arguments for git stash.' }
        },
        required: []
    };
    async execute(actionName, input, _context) {
        const args = input.args || '';
        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        try {
            const result = await new Promise((resolve, reject) => {
                cp.exec(`git stash ${args}`, { cwd }, (err, stdout, stderr) => {
                    if (err && !stdout)
                        return reject(stderr || err.message);
                    resolve(stdout || stderr);
                });
            });
            return { success: true, output: result.trim(), toolId: this.id, actionName };
        }
        catch (error) {
            return { success: false, error: `Git stash failed: ${error.message || error}`, toolId: this.id, actionName };
        }
    }
}
exports.GitStashTool = GitStashTool;
class GitRevertTool {
    id = 'revert';
    name = 'Git Revert';
    description = 'Revert commits in the current repository.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({
        args: zod_1.z.string().describe('Arguments for git revert (e.g., commit hash).')
    });
    inputSchema = {
        type: 'object',
        properties: {
            args: { type: 'string', description: 'Arguments for git revert (e.g., commit hash).' }
        },
        required: ['args']
    };
    async execute(actionName, input, _context) {
        const args = input.args;
        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        try {
            const result = await new Promise((resolve, reject) => {
                cp.exec(`git revert ${args}`, { cwd }, (err, stdout, stderr) => {
                    if (err && !stdout)
                        return reject(stderr || err.message);
                    resolve(stdout || stderr);
                });
            });
            return { success: true, output: result.trim(), toolId: this.id, actionName };
        }
        catch (error) {
            return { success: false, error: `Git revert failed: ${error.message || error}`, toolId: this.id, actionName };
        }
    }
}
exports.GitRevertTool = GitRevertTool;
class GitCherryPickTool {
    id = 'cherryPick';
    name = 'Git Cherry-Pick';
    description = 'Cherry-pick commits in the current repository.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({
        args: zod_1.z.string().describe('Arguments for git cherry-pick (e.g., commit hash).')
    });
    inputSchema = {
        type: 'object',
        properties: {
            args: { type: 'string', description: 'Arguments for git cherry-pick (e.g., commit hash).' }
        },
        required: ['args']
    };
    async execute(actionName, input, _context) {
        const args = input.args;
        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        try {
            const result = await new Promise((resolve, reject) => {
                cp.exec(`git cherry-pick ${args}`, { cwd }, (err, stdout, stderr) => {
                    if (err && !stdout)
                        return reject(stderr || err.message);
                    resolve(stdout || stderr);
                });
            });
            return { success: true, output: result.trim(), toolId: this.id, actionName };
        }
        catch (error) {
            return { success: false, error: `Git cherry-pick failed: ${error.message || error}`, toolId: this.id, actionName };
        }
    }
}
exports.GitCherryPickTool = GitCherryPickTool;
class GitRebaseTool {
    id = 'rebase';
    name = 'Git Rebase';
    description = 'Rebase branches or commits in the current repository.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({
        args: zod_1.z.string().describe('Arguments for git rebase (e.g., branch name).')
    });
    inputSchema = {
        type: 'object',
        properties: {
            args: { type: 'string', description: 'Arguments for git rebase (e.g., branch name).' }
        },
        required: ['args']
    };
    async execute(actionName, input, _context) {
        const args = input.args;
        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        try {
            const result = await new Promise((resolve, reject) => {
                cp.exec(`git rebase ${args}`, { cwd }, (err, stdout, stderr) => {
                    if (err && !stdout)
                        return reject(stderr || err.message);
                    resolve(stdout || stderr);
                });
            });
            return { success: true, output: result.trim(), toolId: this.id, actionName };
        }
        catch (error) {
            return { success: false, error: `Git rebase failed: ${error.message || error}`, toolId: this.id, actionName };
        }
    }
}
exports.GitRebaseTool = GitRebaseTool;
class GitTagTool {
    id = 'tag';
    name = 'Git Tag';
    description = 'Create, list, or delete tags in the current repository.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({
        args: zod_1.z.string().optional().describe('Arguments for git tag (e.g., tag name, -d for delete).')
    });
    inputSchema = {
        type: 'object',
        properties: {
            args: { type: 'string', description: 'Arguments for git tag (e.g., tag name, -d for delete).' }
        },
        required: []
    };
    async execute(actionName, input, _context) {
        const args = input.args || '';
        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        try {
            const result = await new Promise((resolve, reject) => {
                cp.exec(`git tag ${args}`, { cwd }, (err, stdout, stderr) => {
                    if (err && !stdout)
                        return reject(stderr || err.message);
                    resolve(stdout || stderr);
                });
            });
            return { success: true, output: result.trim(), toolId: this.id, actionName };
        }
        catch (error) {
            return { success: false, error: `Git tag failed: ${error.message || error}`, toolId: this.id, actionName };
        }
    }
}
exports.GitTagTool = GitTagTool;
class GitBranchGraphTool {
    id = 'branchGraph';
    name = 'Git Branch Graph';
    description = 'Show a graphical log of branches.';
    type = 'single-action'; // Required by ITool
    actions = {}; // Required by ITool
    singleActionSchema = zod_1.z.object({});
    inputSchema = {
        type: 'object',
        properties: {},
        required: []
    };
    async execute(actionName, _input, _context) {
        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        try {
            const result = await new Promise((resolve, reject) => {
                cp.exec('git log --oneline --graph --all --decorate', { cwd }, (err, stdout, stderr) => {
                    if (err && !stdout)
                        return reject(stderr || err.message);
                    resolve(stdout || stderr);
                });
            });
            return { success: true, output: result.trim(), toolId: this.id, actionName };
        }
        catch (error) {
            return { success: false, error: `Git branch graph failed: ${error.message || error}`, toolId: this.id, actionName };
        }
    }
}
exports.GitBranchGraphTool = GitBranchGraphTool;
//# sourceMappingURL=advancedGitTool.js.map