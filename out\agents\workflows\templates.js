"use strict";
/**
 * Codessa workflow templates
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createChatWorkflow = createChatWorkflow;
exports.createReActWorkflow = createReActWorkflow;
exports.createMultiAgentWorkflow = createMultiAgentWorkflow;
exports.createMemoryEnhancedWorkflow = createMemoryEnhancedWorkflow;
exports.createTDDWorkflow = createTDDWorkflow;
exports.createSprintPlanningWorkflow = createSprintPlanningWorkflow;
const corePolyfill_1 = require("./corePolyfill");
const zod_1 = require("zod");
const graph_1 = require("./graph");
const workflowRegistry_1 = require("./workflowRegistry");
const logger_1 = require("../../logger");
// Custom tool implementations
class MemoryRetrievalTool extends corePolyfill_1.Tool {
    _vectorStore;
    name = 'memory-retrieval';
    description = 'Retrieves relevant memories based on the current conversation';
    schema = zod_1.z.object({
        input: zod_1.z.string().optional().describe('The query to search for in memories (optional string)')
    }).strip().transform((obj) => obj.input ?? '');
    constructor(_vectorStore) {
        super('memory-retrieval', 'Retrieves relevant memories based on the current conversation');
        this._vectorStore = _vectorStore;
    }
    async _call({ input }) {
        const query = input || '';
        logger_1.Logger.instance.info('Retrieving memories for:', query);
        const results = await this._vectorStore.similaritySearch(query, 5);
        return results.map(r => (r.content ?? r.pageContent ?? '')).join('\n\n');
    }
}
class MemorySaveTool extends corePolyfill_1.Tool {
    _vectorStore;
    name = 'memory-save';
    description = 'Saves important information from the conversation to memory';
    schema = zod_1.z.object({
        input: zod_1.z.string().optional().describe('The content to save to memory (optional string)')
    }).strip().transform((obj) => obj.input ?? '');
    constructor(_vectorStore) {
        super('memory-save', 'Saves important information from the conversation to memory');
        this._vectorStore = _vectorStore;
    }
    async _call({ input }) {
        const content = input || '';
        logger_1.Logger.instance.info('Saving memory:', content);
        await this._vectorStore.addDocuments([{ pageContent: content, metadata: {} }]);
        return 'Memory saved successfully';
    }
}
/**
 * Create a simple chat workflow
 */
function createChatWorkflow(id, name, description, agent) {
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const agentNode = graph_1.Codessa.createAgentNode('agent', 'Agent', agent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Create edges
    const edges = [
        { name: 'input-to-agent', source: 'input', target: 'agent', type: 'default' },
        { name: 'agent-to-output', source: 'agent', target: 'output', type: 'default' }
    ];
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [inputNode, agentNode, outputNode],
        edges,
        startNodeId: 'input',
        operationMode: 'ask'
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a ReAct agent workflow with tool integration
 */
function createReActWorkflow(id, name, description, agent, tools) {
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const agentNode = graph_1.Codessa.createAgentNode('agent', 'Agent', agent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Create tool nodes
    const toolNodes = tools.map((tool, index) => {
        return graph_1.Codessa.createToolNode(`tool-${index}`, tool.name, tool);
    });
    // Create edges
    const edges = [
        { name: 'input-to-agent', source: 'input', target: 'agent', type: 'default' }
    ];
    // Add edges from agent to tools and back
    toolNodes.forEach((toolNode, index) => {
        edges.push({
            name: `agent-to-tool-${index}`,
            source: 'agent',
            target: toolNode.id,
            type: 'default',
            condition: async (state) => {
                const lastOutput = state.outputs['agent'];
                return lastOutput && lastOutput.includes(`Using tool: ${tools[index].name}`);
            }
        });
        edges.push({
            name: `tool-${index}-to-agent`,
            source: toolNode.id,
            target: 'agent',
            type: 'default'
        });
    });
    // Add edge from agent to output
    edges.push({
        name: 'agent-to-output',
        source: 'agent',
        target: 'output',
        type: 'default',
        condition: async (state) => {
            const lastOutput = state.outputs['agent'];
            return lastOutput && lastOutput.includes('Final Answer:');
        }
    });
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [inputNode, agentNode, ...toolNodes, outputNode],
        edges,
        startNodeId: 'input',
        operationMode: 'chat'
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a multi-agent workflow for collaborative problem solving
 */
function createMultiAgentWorkflow(id, name, description, agents, supervisorAgent) {
    if (agents.length === 0) {
        throw new Error('At least one agent is required');
    }
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const supervisorNode = graph_1.Codessa.createAgentNode('supervisor', 'Supervisor', supervisorAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Create agent nodes
    const agentNodes = agents.map((agent, index) => {
        return graph_1.Codessa.createAgentNode(`agent-${index}`, agent.name, agent);
    });
    // Create edges
    const edges = [
        { name: 'input-to-supervisor', source: 'input', target: 'supervisor', type: 'default' }
    ];
    // Add edges from supervisor to agents
    agentNodes.forEach((agentNode, index) => {
        edges.push({
            name: `supervisor-to-agent-${index}`,
            source: 'supervisor',
            target: agentNode.id,
            type: 'default',
            condition: async (state) => {
                const lastOutput = state.outputs['supervisor'];
                return lastOutput && lastOutput.includes(`Delegating to: ${agents[index].name}`);
            }
        });
        edges.push({
            name: `agent-${index}-to-supervisor`,
            source: agentNode.id,
            target: 'supervisor',
            type: 'default'
        });
    });
    // Add edge from supervisor to output
    edges.push({
        name: 'supervisor-to-output',
        source: 'supervisor',
        target: 'output',
        type: 'default',
        condition: async (state) => {
            const lastOutput = state.outputs['supervisor'];
            return lastOutput && lastOutput.includes('Final Consensus:');
        }
    });
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [inputNode, supervisorNode, ...agentNodes, outputNode],
        edges,
        startNodeId: 'input',
        operationMode: 'multi-agent'
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a memory-enhanced workflow with vector store integration
 */
function createMemoryEnhancedWorkflow(id, name, description, agent, vectorStore) {
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const memoryRetrievalTool = new MemoryRetrievalTool(vectorStore);
    const memoryRetrievalNode = graph_1.Codessa.createToolNode('memory-retrieval', 'Memory Retrieval', memoryRetrievalTool);
    const agentNode = graph_1.Codessa.createAgentNode('agent', 'Agent', agent);
    const memorySaveTool = new MemorySaveTool(vectorStore);
    const memorySaveNode = graph_1.Codessa.createToolNode('memory-save', 'Memory Save', memorySaveTool);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Create edges
    const edges = [
        { name: 'input-to-memory-retrieval', source: 'input', target: 'memory-retrieval', type: 'default' },
        { name: 'memory-retrieval-to-agent', source: 'memory-retrieval', target: 'agent', type: 'default' },
        { name: 'agent-to-memory-save', source: 'agent', target: 'memory-save', type: 'default' },
        { name: 'memory-save-to-output', source: 'memory-save', target: 'output', type: 'default' }
    ];
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [inputNode, memoryRetrievalNode, agentNode, memorySaveNode, outputNode],
        edges,
        startNodeId: 'input',
        operationMode: 'chat'
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a workflow for Test-Driven Development (TDD)
 */
function createTDDWorkflow(id, name, description, implementationAgent, testingAgent, refactoringAgent) {
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const testWriterNode = graph_1.Codessa.createAgentNode('test-writer', 'Test Writer', testingAgent);
    const implementerNode = graph_1.Codessa.createAgentNode('implementer', 'Implementation', implementationAgent);
    const testerNode = graph_1.Codessa.createAgentNode('tester', 'Test Runner', testingAgent);
    const refactorNode = graph_1.Codessa.createAgentNode('refactor', 'Refactoring', refactoringAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Create edges for TDD cycle
    const edges = [
        { name: 'input-to-test-writer', source: 'input', target: 'test-writer', type: 'default' },
        { name: 'test-writer-to-implementer', source: 'test-writer', target: 'implementer', type: 'default' },
        { name: 'implementer-to-tester', source: 'implementer', target: 'tester', type: 'default' },
        {
            name: 'tester-to-refactor',
            source: 'tester',
            target: 'refactor',
            type: 'default',
            condition: async (state) => {
                const testOutput = state.outputs['tester'];
                return testOutput && testOutput.includes('Tests passed');
            }
        },
        {
            name: 'tester-to-implementer',
            source: 'tester',
            target: 'implementer',
            type: 'default',
            condition: async (state) => {
                const testOutput = state.outputs['tester'];
                return testOutput && testOutput.includes('Tests failed');
            }
        },
        { name: 'refactor-to-tester', source: 'refactor', target: 'tester', type: 'default' },
        {
            name: 'tester-to-output',
            source: 'tester',
            target: 'output',
            type: 'default',
            condition: async (state) => {
                const testOutput = state.outputs['tester'];
                return testOutput &&
                    testOutput.includes('Tests passed') &&
                    testOutput.includes('Code quality metrics satisfied');
            }
        }
    ];
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [inputNode, testWriterNode, implementerNode, testerNode, refactorNode, outputNode],
        edges,
        startNodeId: 'input',
        operationMode: 'debugging'
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
/**
 * Create a workflow for Agile Sprint Planning
 */
function createSprintPlanningWorkflow(id, name, description, productOwnerAgent, techLeadAgent, estimatorAgent) {
    // Create nodes
    const inputNode = graph_1.Codessa.createInputNode('input', 'Input');
    const backlogGroomingNode = graph_1.Codessa.createAgentNode('backlog-grooming', 'Backlog Grooming', productOwnerAgent);
    const technicalAnalysisNode = graph_1.Codessa.createAgentNode('tech-analysis', 'Technical Analysis', techLeadAgent);
    const estimationNode = graph_1.Codessa.createAgentNode('estimation', 'Story Estimation', estimatorAgent);
    const prioritizationNode = graph_1.Codessa.createAgentNode('prioritization', 'Sprint Prioritization', productOwnerAgent);
    const capacityPlanningNode = graph_1.Codessa.createAgentNode('capacity-planning', 'Capacity Planning', techLeadAgent);
    const outputNode = graph_1.Codessa.createOutputNode('output', 'Output');
    // Create edges for sprint planning flow
    const edges = [
        { name: 'input-to-backlog-grooming', source: 'input', target: 'backlog-grooming', type: 'default' },
        { name: 'backlog-grooming-to-tech-analysis', source: 'backlog-grooming', target: 'tech-analysis', type: 'default' },
        { name: 'tech-analysis-to-estimation', source: 'tech-analysis', target: 'estimation', type: 'default' },
        { name: 'estimation-to-prioritization', source: 'estimation', target: 'prioritization', type: 'default' },
        { name: 'prioritization-to-capacity-planning', source: 'prioritization', target: 'capacity-planning', type: 'default' },
        { name: 'capacity-planning-to-output', source: 'capacity-planning', target: 'output', type: 'default' }
    ];
    // Create workflow definition
    const workflow = {
        id,
        name,
        description,
        version: '1.0.0',
        nodes: [
            inputNode,
            backlogGroomingNode,
            technicalAnalysisNode,
            estimationNode,
            prioritizationNode,
            capacityPlanningNode,
            outputNode
        ],
        edges,
        startNodeId: 'input',
        operationMode: 'agentic'
    };
    // Register workflow
    workflowRegistry_1.workflowRegistry.registerWorkflow(workflow);
    return workflow;
}
//# sourceMappingURL=templates.js.map