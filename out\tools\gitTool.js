"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.gitTool = exports.GitTool = void 0;
const vscode = __importStar(require("vscode"));
const util = __importStar(require("util"));
const cp = __importStar(require("child_process"));
const advancedGitTool_1 = require("./advancedGitTool");
const zod_1 = require("zod");
const path = __importStar(require("path"));
const logger_1 = require("../logger");
class GitTool {
    execPromise = util.promisify(cp.exec);
    id = 'git';
    name = 'Git Operations (Advanced)';
    description = 'Stage, commit, push, pull, branch, diff, log, stash, revert, cherry-pick, rebase, tag, and show branch graph.';
    type = 'multi-action'; // Required by ITool
    actions = {
        'status': {
            id: 'status',
            name: 'Git Status',
            description: 'Show git status.',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({
                args: zod_1.z.string().optional().describe('Additional arguments for git status command')
            }),
            inputSchema: {
                type: 'object',
                properties: {
                    args: { type: 'string', description: 'Additional arguments for git status command' }
                },
                required: []
            },
            async execute(actionName, input, context) {
                try {
                    // Validate input against schema
                    const parsedInput = this.singleActionSchema.safeParse(input);
                    if (!parsedInput.success) {
                        return {
                            success: false,
                            error: `Invalid input: ${parsedInput.error.message}`,
                            toolId: 'status',
                            actionName
                        };
                    }
                    // Safely extract and sanitize args
                    const args = (input.args || '').trim();
                    // Check if workspace folder exists
                    if (!vscode.workspace.workspaceFolders || vscode.workspace.workspaceFolders.length === 0) {
                        return {
                            success: false,
                            error: 'No workspace folder open. Git commands require an open workspace.',
                            toolId: 'status',
                            actionName
                        };
                    }
                    const cwd = vscode.workspace.workspaceFolders[0].uri.fsPath;
                    // Execute git command with proper error handling
                    const result = await new Promise((resolve, reject) => {
                        cp.exec(`git status ${args}`, { cwd }, (err, stdout, stderr) => {
                            if (err) {
                                resolve({
                                    stdout: stdout || '',
                                    stderr: stderr || err.message,
                                    error: err
                                });
                            }
                            else {
                                resolve({
                                    stdout: stdout || '',
                                    stderr: stderr || ''
                                });
                            }
                        });
                    });
                    // Handle the result
                    if (result.error) {
                        return {
                            success: false,
                            error: `Git status failed: ${result.stderr}`,
                            toolId: 'status',
                            actionName,
                            output: result.stdout.trim() // Include any stdout that might have been generated
                        };
                    }
                    return {
                        success: true,
                        output: result.stdout.trim() || result.stderr.trim(),
                        toolId: 'status',
                        actionName
                    };
                }
                catch (error) {
                    return {
                        success: false,
                        error: `Unexpected error executing git status: ${error.message}`,
                        toolId: 'status',
                        actionName
                    };
                }
            }
        },
        'add': {
            id: 'add',
            name: 'Git Add',
            description: 'Stage files for commit.',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({
                args: zod_1.z.string().describe('Files to stage or staging options')
            }),
            inputSchema: {
                type: 'object',
                properties: {
                    args: { type: 'string', description: 'Files to stage or staging options' }
                },
                required: ['args']
            },
            async execute(actionName, input, context) {
                try {
                    // Validate input against schema
                    const parsedInput = this.singleActionSchema.safeParse(input);
                    if (!parsedInput.success) {
                        return {
                            success: false,
                            error: `Invalid input: ${parsedInput.error.message}`,
                            toolId: 'add',
                            actionName
                        };
                    }
                    // Check if args is provided (required parameter)
                    if (!input.args) {
                        return {
                            success: false,
                            error: 'The \'args\' parameter is required for git add. Specify files to stage.',
                            toolId: 'add',
                            actionName
                        };
                    }
                    // Safely extract and sanitize args
                    const args = input.args.trim();
                    // Check if workspace folder exists
                    if (!vscode.workspace.workspaceFolders || vscode.workspace.workspaceFolders.length === 0) {
                        return {
                            success: false,
                            error: 'No workspace folder open. Git commands require an open workspace.',
                            toolId: 'add',
                            actionName
                        };
                    }
                    const cwd = vscode.workspace.workspaceFolders[0].uri.fsPath;
                    // Execute git command with proper error handling
                    const result = await new Promise((resolve, reject) => {
                        cp.exec(`git add ${args}`, { cwd }, (err, stdout, stderr) => {
                            if (err) {
                                resolve({
                                    stdout: stdout || '',
                                    stderr: stderr || err.message,
                                    error: err
                                });
                            }
                            else {
                                resolve({
                                    stdout: stdout || '',
                                    stderr: stderr || ''
                                });
                            }
                        });
                    });
                    // Handle the result
                    if (result.error) {
                        return {
                            success: false,
                            error: `Git add failed: ${result.stderr}`,
                            toolId: 'add',
                            actionName,
                            output: result.stdout.trim() // Include any stdout that might have been generated
                        };
                    }
                    // If no output was generated (common for git add), provide a helpful message
                    const output = result.stdout.trim() || result.stderr.trim() || `Successfully staged: ${args}`;
                    return {
                        success: true,
                        output,
                        toolId: 'add',
                        actionName
                    };
                }
                catch (error) {
                    return {
                        success: false,
                        error: `Unexpected error executing git add: ${error.message}`,
                        toolId: 'add',
                        actionName
                    };
                }
            }
        },
        'commit': {
            id: 'commit',
            name: 'Git Commit',
            description: 'Commit staged changes.',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({
                args: zod_1.z.string().describe('Commit message and options')
            }),
            inputSchema: {
                type: 'object',
                properties: {
                    args: { type: 'string', description: 'Commit message and options' }
                },
                required: ['args']
            },
            async execute(actionName, input, context) {
                const args = input.args;
                const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                const result = await new Promise((resolve, reject) => {
                    cp.exec(`git commit ${args}`, { cwd }, (err, stdout, stderr) => {
                        if (err && !stdout)
                            return reject(stderr || err.message);
                        resolve(stdout || stderr);
                    });
                });
                return { success: true, output: result.trim(), toolId: 'commit', actionName };
            }
        },
        'push': {
            id: 'push',
            name: 'Git Push',
            description: 'Push commits to remote.',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({
                args: zod_1.z.string().describe('Push options and target')
            }),
            inputSchema: {
                type: 'object',
                properties: {
                    args: { type: 'string', description: 'Push options and target' }
                },
                required: ['args']
            },
            async execute(actionName, input, context) {
                const args = input.args;
                const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                const result = await new Promise((resolve, reject) => {
                    cp.exec(`git push ${args}`, { cwd }, (err, stdout, stderr) => {
                        if (err && !stdout)
                            return reject(stderr || err.message);
                        resolve(stdout || stderr);
                    });
                });
                return { success: true, output: result.trim(), toolId: 'push', actionName };
            }
        },
        'pull': {
            id: 'pull',
            name: 'Git Pull',
            description: 'Pull from remote.',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({
                args: zod_1.z.string().describe('Pull options and source')
            }),
            inputSchema: {
                type: 'object',
                properties: {
                    args: { type: 'string', description: 'Pull options and source' }
                },
                required: ['args']
            },
            async execute(actionName, input, context) {
                const args = input.args;
                const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                const result = await new Promise((resolve, reject) => {
                    cp.exec(`git pull ${args}`, { cwd }, (err, stdout, stderr) => {
                        if (err && !stdout)
                            return reject(stderr || err.message);
                        resolve(stdout || stderr);
                    });
                });
                return { success: true, output: result.trim(), toolId: 'pull', actionName };
            }
        },
        'branch': {
            id: 'branch',
            name: 'Git Branch',
            description: 'Manage branches.',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({
                args: zod_1.z.string().optional().describe('Branch options and names')
            }),
            inputSchema: {
                type: 'object',
                properties: {
                    args: { type: 'string', description: 'Branch options and names' }
                },
                required: []
            },
            async execute(actionName, input, context) {
                const args = input.args || '';
                const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                const result = await new Promise((resolve, reject) => {
                    cp.exec(`git branch ${args}`, { cwd }, (err, stdout, stderr) => {
                        if (err && !stdout)
                            return reject(stderr || err.message);
                        resolve(stdout || stderr);
                    });
                });
                return { success: true, output: result.trim(), toolId: 'branch', actionName };
            }
        },
        'diff': {
            id: 'diff',
            name: 'Git Diff',
            description: 'Show diff.',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({
                args: zod_1.z.string().optional().describe('Diff options and targets')
            }),
            inputSchema: {
                type: 'object',
                properties: {
                    args: { type: 'string', description: 'Diff options and targets' }
                },
                required: []
            },
            async execute(actionName, input, context) {
                const args = input.args || '';
                const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                const result = await new Promise((resolve, reject) => {
                    cp.exec(`git diff ${args}`, { cwd }, (err, stdout, stderr) => {
                        if (err && !stdout)
                            return reject(stderr || err.message);
                        resolve(stdout || stderr);
                    });
                });
                return { success: true, output: result.trim(), toolId: 'diff', actionName };
            }
        },
        'log': {
            id: 'log',
            name: 'Git Log',
            description: 'Show log.',
            type: 'single-action', // Required by ITool
            actions: {}, // Required by ITool
            singleActionSchema: zod_1.z.object({
                args: zod_1.z.string().optional().describe('Log options and filters')
            }),
            inputSchema: {
                type: 'object',
                properties: {
                    args: { type: 'string', description: 'Log options and filters' }
                },
                required: []
            },
            async execute(actionName, input, context) {
                const args = input.args || '';
                const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                const result = await new Promise((resolve, reject) => {
                    cp.exec(`git log ${args}`, { cwd }, (err, stdout, stderr) => {
                        if (err && !stdout)
                            return reject(stderr || err.message);
                        resolve(stdout || stderr);
                    });
                });
                return { success: true, output: result.trim(), toolId: 'log', actionName };
            }
        },
        'stash': {
            ...new advancedGitTool_1.GitStashTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                // Placeholder implementation
                const args = input.args || '';
                const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                const result = await new Promise((resolve, reject) => {
                    cp.exec(`git stash ${args}`, { cwd }, (err, stdout, stderr) => {
                        if (err && !stdout)
                            return reject(stderr || err.message);
                        resolve(stdout || stderr);
                    });
                });
                return { success: true, output: result.trim(), toolId: 'stash', actionName };
            }
        },
        'revert': {
            ...new advancedGitTool_1.GitRevertTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                // Placeholder implementation
                const args = input.args || '';
                const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                const result = await new Promise((resolve, reject) => {
                    cp.exec(`git revert ${args}`, { cwd }, (err, stdout, stderr) => {
                        if (err && !stdout)
                            return reject(stderr || err.message);
                        resolve(stdout || stderr);
                    });
                });
                return { success: true, output: result.trim(), toolId: 'revert', actionName };
            }
        },
        'cherryPick': {
            ...new advancedGitTool_1.GitCherryPickTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                // Placeholder implementation
                const args = input.args || '';
                const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                const result = await new Promise((resolve, reject) => {
                    cp.exec(`git cherry-pick ${args}`, { cwd }, (err, stdout, stderr) => {
                        if (err && !stdout)
                            return reject(stderr || err.message);
                        resolve(stdout || stderr);
                    });
                });
                return { success: true, output: result.trim(), toolId: 'cherryPick', actionName };
            }
        },
        'rebase': {
            ...new advancedGitTool_1.GitRebaseTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                // Placeholder implementation
                const args = input.args || '';
                const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                const result = await new Promise((resolve, reject) => {
                    cp.exec(`git rebase ${args}`, { cwd }, (err, stdout, stderr) => {
                        if (err && !stdout)
                            return reject(stderr || err.message);
                        resolve(stdout || stderr);
                    });
                });
                return { success: true, output: result.trim(), toolId: 'rebase', actionName };
            }
        },
        'tag': {
            ...new advancedGitTool_1.GitTagTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                // Placeholder implementation
                const args = input.args || '';
                const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                const result = await new Promise((resolve, reject) => {
                    cp.exec(`git tag ${args}`, { cwd }, (err, stdout, stderr) => {
                        if (err && !stdout)
                            return reject(stderr || err.message);
                        resolve(stdout || stderr);
                    });
                });
                return { success: true, output: result.trim(), toolId: 'tag', actionName };
            }
        },
        'branchGraph': {
            ...new advancedGitTool_1.GitBranchGraphTool(),
            type: 'single-action',
            actions: {},
            async execute(actionName, input, context) {
                // Placeholder implementation
                const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                const result = await new Promise((resolve, reject) => {
                    cp.exec('git log --graph --oneline --all --decorate', { cwd }, (err, stdout, stderr) => {
                        if (err && !stdout)
                            return reject(stderr || err.message);
                        resolve(stdout || stderr);
                    });
                });
                return { success: true, output: result.trim(), toolId: 'branchGraph', actionName };
            }
        },
    };
    async getCommitHistory(filePath, startCommit, endCommit) {
        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!cwd) {
            throw new Error('No workspace folder open.');
        }
        const range = startCommit && endCommit ? `${startCommit}..${endCommit}` : '';
        const command = `git log --pretty=format:'%H|%at|%an|%s' ${range} -- "${filePath}"`;
        try {
            const { stdout } = await this.execPromise(command, { cwd });
            if (!stdout) {
                return [];
            }
            return stdout.trim().split('\n').map(line => {
                const [hash, date, author, message] = line.split('|');
                const commit = {
                    hash,
                    author,
                    date: new Date(parseInt(date, 10) * 1000).toISOString(),
                    message,
                    changes: [],
                };
                return commit;
            });
        }
        catch (error) {
            console.error('Error getting commit history:', error);
            return [];
        }
    }
    async getChanges(commitHash, filePath) {
        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!cwd) {
            throw new Error('No workspace folder open.');
        }
        const command = `git show --pretty=tformat: --numstat ${commitHash} -- "${filePath}"`;
        try {
            const { stdout } = await this.execPromise(command, { cwd });
            if (!stdout) {
                return [];
            }
            return stdout.trim().split('\n').map(line => {
                const [additions, deletions, path] = line.split('\t');
                const numAdditions = parseInt(additions, 10) || 0;
                const numDeletions = parseInt(deletions, 10) || 0;
                let changeType = 'modified';
                if (numAdditions > 0 && numDeletions === 0)
                    changeType = 'added';
                if (numDeletions > 0 && numAdditions === 0)
                    changeType = 'deleted';
                const change = {
                    type: changeType,
                    description: `File ${path} was ${changeType}.`,
                    impactLevel: 2, // medium
                    metadata: {
                        filePath: path,
                        additions: numAdditions,
                        deletions: numDeletions,
                    },
                };
                return change;
            });
        }
        catch (error) {
            console.error(`Error getting changes for commit ${commitHash}:`, error);
            return [];
        }
    }
    async getFileContentAtCommit(commitHash, filePath) {
        const cwd = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
        if (!cwd) {
            throw new Error('No workspace folder open.');
        }
        const sanitizedFilePath = path.normalize(filePath).replace(/^(\.\.(\/|\\|$))+/, '');
        const command = `git show ${commitHash}:"${sanitizedFilePath}"`;
        try {
            const { stdout } = await this.execPromise(command, { cwd });
            return stdout;
        }
        catch (error) {
            logger_1.logger.warn(`Could not get content of ${filePath} at commit ${commitHash}. It might not have existed. Error: ${error}`);
            return '';
        }
    }
    async execute(actionName, input, context) {
        try {
            // For backward compatibility, check if actionName is undefined and try to get it from input
            const action = actionName || input.action;
            if (!action) {
                const availableActions = Object.keys(this.actions).join(', ');
                return {
                    success: false,
                    error: `Action parameter is required. Available actions: ${availableActions}`,
                    toolId: this.id,
                    actionName,
                    metadata: {
                        availableActions: Object.keys(this.actions)
                    }
                };
            }
            // Validate that the action exists
            const actionTool = this.actions[action];
            if (!actionTool) {
                const availableActions = Object.keys(this.actions).join(', ');
                return {
                    success: false,
                    error: `Unknown git action: '${action}'. Available actions: ${availableActions}`,
                    toolId: this.id,
                    actionName,
                    metadata: {
                        availableActions: Object.keys(this.actions),
                        requestedAction: action
                    }
                };
            }
            // Create a clean copy of the input without the action property
            const actionInput = { ...input };
            if ('action' in actionInput) {
                delete actionInput.action;
            }
            // Check if the action tool has the new execute method signature
            if (actionTool.execute.length >= 2) {
                // Pass the action as the actionName parameter to the nested tool
                return await actionTool.execute(action, actionInput, context);
            }
            else {
                // Fallback for older tools that don't have the new signature
                const result = await actionTool.execute(actionInput, context);
                // Ensure the result has the required properties
                if (result) {
                    if (!result.toolId) {
                        result.toolId = action;
                    }
                    if (!result.actionName) {
                        result.actionName = action;
                    }
                }
                return result;
            }
        }
        catch (error) {
            // Catch any unexpected errors in the execution flow
            return {
                success: false,
                error: `Unexpected error in Git tool: ${error.message}`,
                toolId: this.id,
                actionName,
                metadata: {
                    errorStack: error.stack
                }
            };
        }
    }
}
exports.GitTool = GitTool;
exports.gitTool = new GitTool();
//# sourceMappingURL=gitTool.js.map