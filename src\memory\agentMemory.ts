import type { Agent } from '../agents/agentUtilities/agent';
import { logger } from '../logger';
import type { MemoryEntry, MemorySource, MemoryType } from './types';
import { memoryManager } from './memoryManager';
import { getConfig } from '../config';

/**
 * Agent memory integration
 */
export class AgentMemory {
  private agentRef: WeakRef<Agent>;
  private conversationHistory: MemoryEntry[] = [];
  private relevantMemories: MemoryEntry[] = [];

  constructor(agent: Agent) {
    this.agentRef = new WeakRef(agent);
  }

  private get agent(): Agent | undefined {
    return this.agentRef.deref();
  }

  /**
     * Add a memory entry to the agent's memory
     */
  public async addMemory(entry: Omit<MemoryEntry, 'id' | 'timestamp'>): Promise<MemoryEntry> {
    try {
      const agent = this.agent;
      if (!agent) {
        throw new Error('Agent reference is no longer available');
      }

      // Create memory entry with agent context
      const memory = await memoryManager.addMemory({
        content: entry.content,
        metadata: {
          ...entry.metadata,
          agentId: agent.id,
          agentName: agent.name
        }
      });

      logger.debug(`Added memory to agent: ${entry.content.substring(0, 50)}...`);
      return memory;
    } catch (error) {
      logger.error('Failed to add memory to agent:', error);
      throw error;
    }
  }

  /**
     * Add a message to the agent's memory
     */
  public async addMessage(role: 'user' | 'assistant', content: string): Promise<void> {
    try {
      const agent = this.agent;
      if (!agent) {
        logger.warn('Cannot add message: Agent reference is no longer available');
        return;
      }

      // Create memory entry
      const memory = await memoryManager.addMemory({
        content,
        metadata: {
          source: 'conversation' as MemorySource,
          type: role === 'user' ? 'human' as MemoryType : 'ai' as MemoryType,
          agentId: agent.id,
          agentName: agent.name
        }
      });

      // Add to conversation history
      this.conversationHistory.push(memory);

      // Trim conversation history if needed
      const maxHistorySize = getConfig<number>('memory.conversationHistorySize', 100);
      if (this.conversationHistory.length > maxHistorySize) {
        this.conversationHistory = this.conversationHistory.slice(-maxHistorySize);
      }

      logger.debug(`Added ${role} message to agent memory: ${content.substring(0, 50)}...`);
    } catch (error) {
      logger.error('Failed to add message to agent memory:', error);
    }
  }

  /**
     * Get conversation history
     */
  public getConversationHistory(): MemoryEntry[] {
    return [...this.conversationHistory];
  }

  /**
     * Get relevant memories for a query
     */
  public async getRelevantMemories(query: string): Promise<MemoryEntry[]> {
    try {
      const agent = this.agent;
      if (!agent) {
        logger.warn('Cannot get relevant memories: Agent reference is no longer available');
        return [];
      }

      // Check if memory is enabled
      const memoryEnabled = getConfig<boolean>('memory.enabled', true);
      if (!memoryEnabled) {
        return [];
      }

      // Search for relevant memories
      this.relevantMemories = await memoryManager.searchSimilarMemories(query, {
        limit: getConfig<number>('memory.contextWindowSize', 5),
        filter: {
          source: 'conversation',
          agentId: agent.id
        }
      });

      return this.relevantMemories;
    } catch (error) {
      logger.error('Failed to get relevant memories:', error);
      return [];
    }
  }

  /**
     * Clear conversation history
     */
  public clearConversationHistory(): void {
    this.conversationHistory = [];
  }

  /**
     * Format memories for inclusion in prompts
     */
  public formatMemoriesForPrompt(memories: MemoryEntry[]): string {
    if (memories.length === 0) {
      return '';
    }

    const formattedMemories = memories.map(memory => {
      const role = memory.metadata.type === 'human' ? 'User' : 'Assistant';
      return `${role}: ${memory.content}`;
    }).join('\n\n');

    return `\n\nRelevant conversation history:\n${formattedMemories}\n\n`;
  }

  /**
     * Get a summary of the agent's memory
     */
  public async getMemorySummary(): Promise<string> {
    try {
      const agent = this.agent;
      if (!agent) {
        logger.warn('Cannot get memory summary: Agent reference is no longer available');
        return 'Agent reference is no longer available';
      }

      // Get all memories for this agent
      const memories = await memoryManager.searchMemories({
        query: '',
        limit: 1000,
        filter: {
          agentId: agent.id
        }
      });

      if (memories.length === 0) {
        return 'No memories available.';
      }

      // Count by type
      const typeCounts: Record<string, number> = {};
      for (const memory of memories) {
        const type = memory.metadata.type;
        typeCounts[type] = (typeCounts[type] || 0) + 1;
      }

      // Format summary
      const summary = [
        `Total memories: ${memories.length}`,
        'Memory types:',
        ...Object.entries(typeCounts).map(([type, count]) => `- ${type}: ${count}`)
      ].join('\n');

      return summary;
    } catch (error) {
      logger.error('Failed to get memory summary:', error);
      return 'Failed to get memory summary.';
    }
  }
}

/**
 * Get agent memory for an agent
 */
const agentMemories = new WeakMap<Agent, AgentMemory>();

export function getAgentMemory(agent: Agent): AgentMemory {
  if (!agent) {
    throw new Error('Agent cannot be null or undefined');
  }

  if (!agentMemories.has(agent)) {
    agentMemories.set(agent, new AgentMemory(agent));
  }

  const memory = agentMemories.get(agent);
  if (!memory) {
    throw new Error('Failed to create or retrieve agent memory');
  }

  return memory;
}
