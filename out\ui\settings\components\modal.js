"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setModalTheme = setModalTheme;
exports.showModal = showModal;
// Reusable modal component for settings panel
const themeConfig_1 = require("../themeConfig");
let currentTheme = themeConfig_1.defaultUIThemeConfig;
function setModalTheme(theme) {
    currentTheme = theme;
}
function showModal(options) {
    // Detect destructive action
    const isDestructive = /delete|remove|permanently/i.test(options.title + options.content);
    const modalTheme = currentTheme.modal;
    // Extract warning for highlight
    let contentHTML = options.content;
    const warningMatch = /((cannot|can't) be undone\.?)/i.exec(options.content);
    if (warningMatch) {
        contentHTML = options.content.replace(warningMatch[1], `<span style='color:#b91c1c;font-weight:600;display:inline-flex;align-items:center;gap:4px;'>⚠️ ${warningMatch[1]}</span>`);
    }
    // Remove any existing modal
    const prev = document.getElementById('settings-global-modal');
    if (prev)
        prev.remove();
    // Modal overlay
    const overlay = document.createElement('div');
    overlay.id = 'settings-global-modal';
    overlay.tabIndex = -1;
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100vw';
    overlay.style.height = '100vh';
    overlay.style.background = 'rgba(0,0,0,0.32)'; // keep overlay semi-transparent
    overlay.style.zIndex = '9999';
    overlay.style.display = 'flex';
    overlay.style.alignItems = 'center';
    overlay.style.justifyContent = 'center';
    overlay.style.opacity = '0';
    overlay.style.transition = 'opacity 0.22s cubic-bezier(.4,0,.2,1)';
    // Modal dialog
    const modal = document.createElement('div');
    modal.className = 'settings-modal-dialog';
    modal.setAttribute('role', 'dialog');
    modal.setAttribute('aria-modal', 'true');
    modal.style.background = modalTheme.background;
    modal.style.color = modalTheme.foreground;
    modal.style.borderRadius = modalTheme.borderRadius;
    modal.style.boxShadow = modalTheme.boxShadow;
    modal.style.border = `2px solid ${modalTheme.border}`;
    modal.style.padding = '38px 34px 28px 34px';
    modal.style.minWidth = '340px';
    modal.style.maxWidth = '520px';
    modal.style.maxHeight = '90vh';
    modal.style.overflowY = 'auto';
    modal.style.position = 'relative';
    modal.style.opacity = '0';
    modal.style.transform = `scale(${modalTheme.animation.scale})`;
    modal.style.transition = `opacity ${modalTheme.animation.duration} cubic-bezier(.4,0,.2,1), transform ${modalTheme.animation.duration} cubic-bezier(.4,0,.2,1)`;
    // Modal header
    const header = document.createElement('div');
    header.style.fontSize = modalTheme.headerFontSize;
    header.style.fontWeight = modalTheme.headerFontWeight;
    header.style.marginBottom = '22px';
    header.style.letterSpacing = '0.01em';
    if (isDestructive) {
        header.innerHTML = `<span style='display:inline-flex;align-items:center;gap:10px;'>${modalTheme.warningIcon} <span>${options.title}</span></span>`;
    }
    else {
        header.textContent = options.title;
    }
    modal.appendChild(header);
    // Modal content
    const contentDiv = document.createElement('div');
    contentDiv.innerHTML = contentHTML;
    contentDiv.style.marginBottom = '26px';
    contentDiv.style.fontSize = modalTheme.contentFontSize;
    modal.appendChild(contentDiv);
    // Button row
    const btnRow = document.createElement('div');
    btnRow.style.display = 'flex';
    btnRow.style.justifyContent = 'flex-end';
    btnRow.style.gap = '18px';
    btnRow.style.marginTop = '8px';
    const cancelBtn = document.createElement('button');
    cancelBtn.textContent = 'Cancel';
    cancelBtn.className = 'modal-btn-cancel';
    cancelBtn.style.background = modalTheme.button.background;
    cancelBtn.style.color = modalTheme.button.color;
    cancelBtn.style.border = modalTheme.button.border;
    cancelBtn.style.borderRadius = modalTheme.button.borderRadius;
    cancelBtn.style.padding = '7px 22px';
    cancelBtn.style.fontSize = '1em';
    cancelBtn.style.cursor = 'pointer';
    cancelBtn.onclick = () => {
        overlay.remove();
        if (options.onCancel)
            options.onCancel();
    };
    const confirmBtn = document.createElement('button');
    confirmBtn.textContent = isDestructive ? 'Delete' : 'Save';
    confirmBtn.className = 'modal-btn-confirm';
    if (isDestructive) {
        confirmBtn.style.background = modalTheme.buttonDestructive.background;
        confirmBtn.style.color = modalTheme.buttonDestructive.color;
        confirmBtn.style.border = modalTheme.buttonDestructive.border;
        confirmBtn.style.boxShadow = modalTheme.buttonDestructive.shadow;
        confirmBtn.style.borderRadius = modalTheme.buttonDestructive.borderRadius;
    }
    else {
        confirmBtn.style.background = modalTheme.button.background;
        confirmBtn.style.color = modalTheme.button.color;
        confirmBtn.style.border = modalTheme.button.border;
        confirmBtn.style.borderRadius = modalTheme.button.borderRadius;
    }
    confirmBtn.style.padding = '7px 22px';
    confirmBtn.style.fontSize = '1em';
    confirmBtn.style.cursor = 'pointer';
    confirmBtn.onclick = () => {
        overlay.remove();
        options.onConfirm();
    };
    // Accessibility: focus confirmBtn by default
    setTimeout(() => {
        confirmBtn.focus();
        // Animate overlay and modal
        overlay.style.opacity = '1';
        modal.style.opacity = '1';
        modal.style.transform = 'scale(1)';
    }, 0);
    btnRow.appendChild(cancelBtn);
    btnRow.appendChild(confirmBtn);
    modal.appendChild(btnRow);
    // Close on overlay click (but not modal click)
    overlay.addEventListener('mousedown', (e) => {
        if (e.target === overlay) {
            overlay.remove();
            if (options.onCancel)
                options.onCancel();
        }
    });
    // Close on Escape key
    overlay.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            overlay.remove();
            if (options.onCancel)
                options.onCancel();
        }
    });
    overlay.appendChild(modal);
    document.body.appendChild(overlay);
    overlay.focus();
}
//# sourceMappingURL=modal.js.map