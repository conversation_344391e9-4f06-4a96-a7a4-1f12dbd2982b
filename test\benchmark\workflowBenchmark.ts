import { performance, PerformanceObserver } from 'perf_hooks';
import { mkdirSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';
// Import core workflow types with aliases to avoid conflicts
import type { Workflow as WorkflowType } from '../../src/workflows/workflow';
import type { WorkflowContext as WorkflowContextSource } from '../../src/workflows/workflowContext';
import { Workflow } from '../../src/workflows/workflow';
import { WorkflowStep } from '../../src/workflows/workflowStep';
import { MockAgent } from '../mocks/agent';
import { MockMemoryProvider } from '../mocks/memoryManager';

// Import the benchmark configuration
import { benchmarkConfig } from './benchmark.config';

// Define a custom WorkflowStepResult type for testing
interface CustomWorkflowStepResult {
  success: boolean;
  output?: any;
  error?: Error;
  metadata?: Record<string, any>;
  nextStepId?: string;
}

// Import types from types.ts
import { 
  BenchmarkStats as StatsType, 
  WorkflowBenchmarkResult, 
  BenchmarkWorkflowConfig 
} from './types';

// Local type for workflow configuration used in this file
type WorkflowConfig = {
  name: string;
  description: string;
  iterations: number;
  warmupRuns: number;
  steps: number;  // Changed from stepCount to steps to match BenchmarkWorkflowConfig
  stepDelay: number;
  memoryOperations: {
    read: number;
    write: number;
    search?: number;
  };
};

// Ensure output directory exists
const outputDir = benchmarkConfig.output.directory;
if (!existsSync(outputDir)) {
  mkdirSync(outputDir, { recursive: true });
}

// Setup performance monitoring
const perfObserver = new PerformanceObserver((items) => {
  items.getEntries().forEach((entry) => {
    console.log(`[Performance] ${entry.name}: ${entry.duration.toFixed(2)}ms`);
  });
});

perfObserver.observe({ entryTypes: ['measure'], buffered: true });

// Create test agents
const agents = {
  supervisor: new MockAgent({
    name: 'Supervisor',
    role: 'supervisor',
    responseTime: 100,
    successRate: 0.98
  }),
  coordinator: new MockAgent({
    name: 'Coordinator',
    role: 'coordinator',
    responseTime: 80,
    successRate: 0.95
  }),
  specialist: new MockAgent({
    name: 'Specialist',
    role: 'specialist',
    responseTime: 150,
    successRate: 0.9
  }),
  executor: new MockAgent({
    name: 'Executor',
    role: 'executor',
    responseTime: 50,
    successRate: 0.99
  })
};

// Create mock memory provider
const memoryProvider = new MockMemoryProvider();

// Define types for workflow step results
export interface WorkflowStepResult {
  success: boolean;
  output?: any;
  nextStepId?: string;
  metadata?: Record<string, any>;
  error?: Error;
}

// Import types from source with aliases to avoid conflicts
import { 
  WorkflowHistoryEntry as SourceWorkflowHistoryEntry,
  WorkflowStepResult as SourceWorkflowStepResult
} from '../../src/workflows/workflowContext';

// Use the imported StatsType for benchmark statistics
type BenchmarkStats = StatsType;

// Define the BenchmarkStepResult type for benchmark results
export interface BenchmarkStepResult {
  duration: number;
  memory: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
  };
  metadata?: {
    quantumAnalysis?: any;
    neuralInsights?: any;
    goddessGuidance?: any;
    [key: string]: any;
  };
}

// Create a compatible WorkflowStepResult type for benchmarking
export interface WorkflowStepResult extends Omit<SourceWorkflowStepResult, 'success'> {
  success: boolean;
  output?: any;
  nextStepId?: string;
  metadata?: Record<string, any>;
  error?: Error;
}

// Create a compatible WorkflowHistoryEntry type for benchmarking
export interface WorkflowHistoryEntry {
  stepId: string;
  startTime: Date;
  endTime: Date;
  duration: number;
  result: WorkflowStepResult;
  error: Error | null;
  metadata: Record<string, any>;
  
  // Alias for backward compatibility
  timestamp: Date;
}

// Helper function to create a WorkflowHistoryEntry
function createWorkflowHistoryEntry(stepId: string, result: WorkflowStepResult, error: Error | null = null): WorkflowHistoryEntry {
  const now = new Date();
  return {
    stepId,
    startTime: now,
    endTime: now,
    duration: 0,
    result,
    error,
    metadata: {},
    timestamp: now
  };
}

// Create a mock workflow registry for testing
class MockWorkflowRegistry {
  private workflows: Map<string, any> = new Map();

  register(workflow: any) {
    this.workflows.set(workflow.id, workflow);
    return this;
  }

  get(id: string): any | undefined {
    return this.workflows.get(id);
  }

  list(): any[] {
    return Array.from(this.workflows.values());
  }

  createWorkflowInstance(workflowId: string, options: any = {}): any {
    const workflow = this.get(workflowId);
    if (!workflow) {
      throw new Error(`Workflow with ID ${workflowId} not found`);
    }
    return {
      ...workflow,
      ...options,
      id: `${workflowId}-${Date.now()}`,
      status: 'initialized',
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }
}

// Create a singleton instance of the mock registry
const workflowRegistry = new MockWorkflowRegistry();

// Define WorkflowContext interface to match expected structure
interface WorkflowContext {
  workflow: any;
  agent: any;
  inputs: Record<string, any>;
  outputs: Record<string, any>;
  variables: Record<string, any>;
  history: any[];
  revolutionaryFeatures: {
    goddessMode: boolean;
    quantumAnalysis: boolean;
    neuralSynthesis: boolean;
    timeTravelDebugging: boolean;
    adaptivePersonality: boolean;
  };
  emotionalContext: {
    mood: string;
    confidence: number;
    sentiment: number;
  };
  quantumState: {
    superposition: boolean;
    entanglement: Record<string, any>;
    observation: any;
  };
  data: Record<string, any>;
  startTime: Date;
  endTime: Date | null;
  status: 'running' | 'completed' | 'failed' | 'paused' | 'cancelled';
  metadata: Record<string, any>;
}

// Create a mock WorkflowContext for testing
function createMockWorkflowContext(workflow: any, agent: any, inputs: any = {}): any {
  const now = new Date();
  return {
    workflow,
    agent,
    inputs,
    outputs: {},
    variables: {},
    history: [],
    revolutionaryFeatures: {
      goddessMode: false,
      quantumAnalysis: false,
      neuralSynthesis: false,
      timeTravelDebugging: false,
      adaptivePersonality: false,
    },
    emotionalContext: {
      mood: 'neutral',
      confidence: 0.8,
      sentiment: 0.5,
    },
    quantumState: {
      superposition: false,
      entanglement: {},
      observation: null,
    },
    // Add required WorkflowContext properties
    data: {},
    startTime: now,
    endTime: now,
    status: 'running' as const,
    metadata: {},
  } as const; // Type assertion to satisfy TypeScript
}

// Helper function to create a properly typed benchmark result
function createBenchmarkResult(params: {
  complexity: 'simple' | 'medium' | 'complex';
  timestamp: string;
  iterations: number;
  successRate: number;
  duration: BenchmarkStats;
  memory: BenchmarkStats;
  steps: Record<string, BenchmarkStats>;
  config: BenchmarkWorkflowConfig;
}): WorkflowBenchmarkResult {
  return {
    complexity: params.complexity,
    timestamp: params.timestamp,
    iterations: params.iterations,
    successRate: params.successRate,
    duration: params.duration,
    memory: params.memory,
    steps: params.steps,
    config: params.config
  };
}

// Create a test workflow for benchmarking
function createTestWorkflow(config: WorkflowConfig | BenchmarkWorkflowConfig): Workflow {
  // Create a workflow with the given configuration
  const workflowName = 'name' in config ? config.name : `benchmark-${Date.now()}`;
  const workflow = new Workflow(
    `benchmark-${Date.now()}`,
    workflowName
  );
  
  // Convert BenchmarkWorkflowConfig to WorkflowConfig if needed
  const workflowConfig: WorkflowConfig = 'iterations' in config 
    ? config as WorkflowConfig 
    : {
        name: 'Benchmark Workflow',
        description: 'Generated for benchmark testing',
        iterations: 1,
        warmupRuns: 0,
        steps: config.steps,
        stepDelay: config.stepDelay,
        memoryOperations: {
          read: config.memoryOperations?.read || 0,
          write: config.memoryOperations?.write || 0,
          search: config.memoryOperations?.search || 0
        }
      };
  
  // Add steps based on configuration
  for (let i = 0; i < config.steps; i++) {
    const stepId = `step-${i + 1}`;
    const step = new WorkflowStep({
      id: stepId,
      name: `Step ${i + 1}`,
      description: `Benchmark step ${i + 1}`,
      execute: async (context: any) => {
        // Simulate work based on step configuration
        const workTime = config.stepDelay || 0;
        await new Promise(resolve => setTimeout(resolve, workTime));
        
        // Simulate memory operations if configured
        if (config.memoryOperations) {
          // Simulate memory reads
          for (let r = 0; r < (config.memoryOperations.read || 0); r++) {
            // Simulate read operation
            const data = Buffer.alloc(1024); // 1KB per read
          }
          
          // Simulate memory writes
          for (let w = 0; w < (config.memoryOperations.write || 0); w++) {
            // Simulate write operation
            const data = Buffer.alloc(1024); // 1KB per write
          }
          
          // Simulate memory searches if configured
          if (config.memoryOperations.search) {
            for (let s = 0; s < (config.memoryOperations.search || 0); s++) {
              // Simulate search operation
              const data = Buffer.alloc(1024); // 1KB per search
            }
          }
        }
        
        return {
          success: true,
          output: `Completed step ${i + 1}`,
          nextStepId: i < config.steps - 1 ? `step-${i + 2}` : undefined,
          metadata: {
            stepNumber: i + 1,
            timestamp: new Date().toISOString()
          }
        };
      },
      timeout: 30000 // 30s default timeout
    });
    
    workflow.addStep(step);
    
    // Set up transitions between steps
    if (i < config.steps - 1) {
      workflow.addTransition(stepId, `step-${i + 2}`);
    }
  }
  
  // Set initial step if there are any steps
  if (config.steps > 0) {
    workflow.setInitialStep('step-1');
  }
  
  return workflow;
}

// Benchmark runner
export class WorkflowBenchmark {
  private results: WorkflowBenchmarkResult[] = [];
  private workflowRegistry = new MockWorkflowRegistry();
  
  // Create a mock agent for testing
  private mockAgent = new MockAgent({
    name: 'BenchmarkAgent',
    role: 'benchmark',
    responseTime: 10,
    successRate: 1.0
  });
  
  /**
   * Calculate statistics for benchmark results
   */
  private calculateStats(values: number[]): StatsType {
    if (!values || values.length === 0) {
      return {
        min: 0,
        max: 0,
        average: 0,
        median: 0,
        p95: 0,
        p99: 0,
        total: 0
      };
    }
    
    // Sort values for percentiles and median
    const sorted = [...values].sort((a, b) => a - b);
    const sum = sorted.reduce((a, b) => a + b, 0);
    const average = sum / sorted.length;
    
    // Calculate percentiles using the helper method
    const getPercentile = (p: number) => {
      const index = Math.floor(p * (sorted.length - 1));
      return sorted[Math.min(index, sorted.length - 1)];
    };
    
    return {
      min: Math.min(...sorted),
      max: Math.max(...sorted),
      average,
      median: getPercentile(0.5),
      p95: getPercentile(0.95),
      p99: getPercentile(0.99),
      total: sum
    };
  }
  
  async run() {
    console.log('Starting workflow benchmarks...\n');
    
    // Test each workflow complexity
    for (const [complexity, config] of Object.entries(benchmarkConfig.workflows)) {
      await this.benchmarkWorkflow(complexity as 'simple' | 'medium' | 'complex');
    }
    
    // Save results
    
    console.log('\nBenchmark completed!');
  }
  
  private async saveResults() {
    if (this.results.length === 0) {
      console.log('No results to save.');
      return;
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const outputFile = join(outputDir, `benchmark-results-${timestamp}.json`);
    
    try {
      writeFileSync(outputFile, JSON.stringify(this.results, null, 2));
      console.log(`\nResults saved to: ${outputFile}`);
    } catch (error) {
      console.error('Failed to save results:', error);
    }
  }
  
  private async benchmarkWorkflow(complexity: 'simple' | 'medium' | 'complex') {
    console.log(`\n=== Benchmarking ${complexity} workflow ===`);
    
    // Create and register the workflow
    const workflowDef = createTestWorkflow(benchmarkConfig.workflows[complexity]);
    this.workflowRegistry.register(workflowDef);
    
    const iterations = benchmarkConfig.iterations;
    const durations: number[] = [];
    const memoryUsages: number[] = [];
    const stepDurations: Record<string, number[]> = {};
    let successCount = 0;
    
    // Warm-up run
    console.log('  Warming up...');
    await this.runWorkflow(workflowDef.id);
    
    // Run benchmark iterations
    console.log(`  Running ${iterations} iterations...`);
    for (let i = 0; i < iterations; i++) {
      process.stdout.write(`  Iteration ${i + 1}/${iterations}... `);
      
      const startTime = performance.now();
      const startMemory = process.memoryUsage().heapUsed;
      
      const result = await this.runWorkflow(workflowDef.id);
      
      const duration = performance.now() - startTime;
      const endMemory = process.memoryUsage().heapUsed;
      const memoryUsed = (endMemory - startMemory) / (1024 * 1024); // MB
      
      if (result.success) {
        successCount++;
        durations.push(duration);
        memoryUsages[i] = memoryUsed;
        
        // Record step durations
        result.steps.forEach((step: any) => {
          if (!stepDurations[step.id]) {
            stepDurations[step.id] = [];
          }
          stepDurations[step.id].push(step.duration);
        });
        
        console.log(`${duration.toFixed(2)}ms`);
      } else {
        console.log(`failed: ${result.error}`);
      }
    }
    
    // Calculate statistics
    const stats = this.calculateStats(durations);
    const memoryStats = this.calculateStats(Object.values(memoryUsages));
    
    // Calculate step statistics
    const stepStats: Record<string, any> = {};
    Object.entries(stepDurations).forEach(([stepId, durations]) => {
      stepStats[stepId] = this.calculateStats(durations as number[]);
    });
    
    // Save results
    const result = createBenchmarkResult({
      complexity,
      timestamp: new Date().toISOString(),
      iterations,
      successRate: successCount / iterations,
      duration: stats,
      memory: memoryStats,
      steps: stepStats,
      config: benchmarkConfig.workflows[complexity]
    });
    
    this.results.push(result);
    
    // Log summary
    console.log(`\n  ${complexity.toUpperCase()} WORKFLOW RESULTS`);
    console.log('  ------------------');
    console.log(`  Success rate: ${(result.successRate * 100).toFixed(1)}%`);
    console.log(`  Duration (avg): ${result.duration.average.toFixed(2)}ms`);
    console.log(`  Memory usage (avg): ${result.memory.average.toFixed(2)}MB`);
    console.log(`  Steps: ${Object.keys(stepStats).length}`);
  }
  
  private async runWorkflow(workflowId: string) {
    const workflow = this.workflowRegistry.createWorkflowInstance(workflowId);
    const context = createMockWorkflowContext(
      workflow,
      this.mockAgent,
      { input: 'Test input' }
    );
    
    const steps: Array<{
      id: string;
      name: string;
      duration: number;
      success: boolean;
      error?: string;
      metadata?: any;
    }> = [];
    
    let currentStepId = workflow.getDefinition().startStepId;
    let result: WorkflowStepResult | undefined;
    
    try {
      while (currentStepId) {
        const step = workflow.getDefinition().steps.find(s => s.id === currentStepId);
        if (!step) {
          throw new Error(`Step ${currentStepId} not found`);
        }
        
        const stepStartTime = performance.now();
        const stepResult = await step.execute(context);
        const stepEndTime = performance.now();
        
        // Add step to history with timing and memory info
        const benchmarkResult = stepResult as unknown as BenchmarkStepResult;
        const historyEntry = createWorkflowHistoryEntry(
          step.id,
          {
            success: true,
            output: { result: 'Mock output' },
            nextStepId: step.nextStepId,
            metadata: {}
          }
        );
        
        context.history.push(historyEntry as any);
        
        steps.push({
          id: step.id,
          name: step.name,
          duration: benchmarkResult.duration || (stepEndTime - stepStartTime),
          success: stepResult.success,
          error: stepResult.error,
          metadata: benchmarkResult.metadata
        });
        
        if (!stepResult.success) {
          return { success: false, error: stepResult.error, steps };
        }
        
        currentStepId = stepResult.nextStepId || '';
        result = stepResult;
      }
      
      return { success: true, result, steps };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      return { 
        success: false, 
        error: errorMessage,
        steps: steps
      };
    }
  }
  
  // calculateStats and getPercentile methods are already defined above
  
  // saveResults method is already defined above
}

// Run the benchmark
async function main() {
  try {
    const benchmark = new WorkflowBenchmark();
    await benchmark.run();
  } catch (error) {
    console.error('Benchmark failed:', error);
    process.exit(1);
  }
}

main();
