/* Advanced Settings Styles */

.advanced-settings {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.advanced-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    color: white;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.advanced-header h2 {
    font-size: 2.5em;
    margin: 0 0 15px 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.advanced-description {
    font-size: 1.1em;
    margin: 0;
    opacity: 0.9;
    line-height: 1.6;
}

.settings-section {
    background: var(--vscode-editor-background);
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.settings-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
    animation: shimmer 3s infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.goddess-section {
    border-color: #ff6b6b;
    box-shadow: 0 5px 20px rgba(255, 107, 107, 0.2);
}

.quantum-section {
    border-color: #4ecdc4;
    box-shadow: 0 5px 20px rgba(78, 205, 196, 0.2);
}

.neural-section {
    border-color: #667eea;
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.2);
}

.timetravel-section {
    border-color: #feca57;
    box-shadow: 0 5px 20px rgba(254, 202, 87, 0.2);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--vscode-panel-border);
}

.section-header h3 {
    font-size: 1.5em;
    margin: 0;
    color: var(--vscode-foreground);
}

.feature-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

.goddess-section input:checked + .toggle-slider {
    background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
}

.quantum-section input:checked + .toggle-slider {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
}

.neural-section input:checked + .toggle-slider {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.timetravel-section input:checked + .toggle-slider {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

.toggle-label {
    font-weight: bold;
    color: var(--vscode-foreground);
}

.goddess-settings-grid,
.quantum-settings-grid,
.neural-settings-grid,
.timetravel-settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.setting-group {
    background: var(--vscode-input-background);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--vscode-input-border);
}

.setting-group label {
    display: block;
    font-weight: bold;
    margin-bottom: 10px;
    color: var(--vscode-foreground);
}

.slider-container {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 5px;
}

.slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: var(--vscode-scrollbarSlider-background);
    outline: none;
    -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.slider-value {
    min-width: 50px;
    text-align: center;
    font-weight: bold;
    color: var(--vscode-foreground);
    background: var(--vscode-button-background);
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.9em;
}

.settings-select {
    width: 100%;
    padding: 10px;
    background: var(--vscode-dropdown-background);
    color: var(--vscode-dropdown-foreground);
    border: 1px solid var(--vscode-dropdown-border);
    border-radius: 4px;
    font-size: 14px;
}

.setting-group small {
    color: var(--vscode-descriptionForeground);
    font-style: italic;
    line-height: 1.4;
}

.goddess-features,
.quantum-features,
.neural-features,
.timetravel-features {
    background: var(--vscode-editor-inactiveSelectionBackground);
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
}

.goddess-features h4,
.quantum-features h4,
.neural-features h4,
.timetravel-features h4 {
    margin: 0 0 15px 0;
    color: var(--vscode-foreground);
    font-size: 1.2em;
}

.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    padding: 10px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.checkbox-item:hover {
    background: var(--vscode-list-hoverBackground);
}

.checkbox-item input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--vscode-button-background);
}

.checkbox-item span {
    color: var(--vscode-foreground);
    font-weight: 500;
}

.settings-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 40px;
    padding: 30px;
    background: var(--vscode-editor-inactiveSelectionBackground);
    border-radius: 12px;
}

.save-button {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
}

.save-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(78, 205, 196, 0.6);
}

.save-button.success {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
}

.save-button.error {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.reset-button {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(149, 165, 166, 0.4);
}

.reset-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(149, 165, 166, 0.6);
}

/* Advanced Workflow Settings Styles */
.advanced-workflow-settings {
    max-width: 1200px;
    margin: 0 auto;
}

.workflow-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    color: white;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.workflow-header h2 {
    font-size: 2.2em;
    margin: 0 0 10px 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.workflow-description {
    font-size: 1.05em;
    margin: 0;
    opacity: 0.9;
    line-height: 1.5;
}

.workflow-core-settings,
.advanced-enhancements,
.workflow-analytics,
.auto-enhancement {
    background: var(--vscode-editor-background);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
    border: 2px solid var(--vscode-panel-border);
    transition: all 0.3s ease;
}

.workflow-core-settings:hover,
.advanced-enhancements:hover,
.workflow-analytics:hover,
.auto-enhancement:hover {
    border-color: var(--vscode-button-background);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.enhancement-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.enhancement-card {
    background: var(--vscode-input-background);
    border-radius: 10px;
    padding: 20px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.enhancement-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
    animation: shimmer 3s infinite;
    pointer-events: none;
}

.goddess-enhancement {
    border-color: #ff6b6b;
}

.goddess-enhancement:hover {
    border-color: #ff6b6b;
    box-shadow: 0 5px 20px rgba(255, 107, 107, 0.3);
}

.quantum-enhancement {
    border-color: #4ecdc4;
}

.quantum-enhancement:hover {
    border-color: #4ecdc4;
    box-shadow: 0 5px 20px rgba(78, 205, 196, 0.3);
}

.neural-enhancement {
    border-color: #667eea;
}

.neural-enhancement:hover {
    border-color: #667eea;
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
}

.timetravel-enhancement {
    border-color: #feca57;
}

.timetravel-enhancement:hover {
    border-color: #feca57;
    box-shadow: 0 5px 20px rgba(254, 202, 87, 0.3);
}

.enhancement-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.enhancement-icon {
    font-size: 1.5em;
    margin-right: 10px;
}

.enhancement-header h4 {
    margin: 0;
    flex: 1;
    color: var(--vscode-foreground);
}

.mini-toggle {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 22px;
}

.mini-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.mini-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 22px;
}

.mini-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .mini-slider {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

input:checked + .mini-slider:before {
    transform: translateX(18px);
}

.enhancement-settings {
    margin-top: 15px;
}

.enhancement-settings label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
    color: var(--vscode-foreground);
}

.mini-select {
    width: 100%;
    padding: 6px;
    background: var(--vscode-dropdown-background);
    color: var(--vscode-dropdown-foreground);
    border: 1px solid var(--vscode-dropdown-border);
    border-radius: 4px;
    font-size: 13px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .goddess-settings-grid,
    .quantum-settings-grid,
    .neural-settings-grid,
    .timetravel-settings-grid,
    .settings-grid,
    .enhancement-grid {
        grid-template-columns: 1fr;
    }

    .checkbox-grid {
        grid-template-columns: 1fr;
    }

    .settings-actions {
        flex-direction: column;
        align-items: center;
    }

    .save-button,
    .reset-button {
        width: 100%;
        max-width: 300px;
    }

    .enhancement-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .enhancement-header h4 {
        margin-bottom: 5px;
    }
}