{"version": 3, "file": "advancedDocsTool.js", "sourceRoot": "", "sources": ["../../src/tools/advancedDocsTool.ts"], "names": [], "mappings": ";;;AAEA,kDAA+C;AAE/C,6BAAwB;AAExB,MAAa,oBAAoB;IACtB,EAAE,GAAG,QAAQ,CAAC;IACd,IAAI,GAAG,0BAA0B,CAAC;IAClC,WAAW,GAAG,0CAA0C,CAAC;IACzD,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,mBAAmB,CAAC;QAC9C,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,wBAAwB,CAAC;KACxF,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,mBAAmB,EAAE;YAC1D,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC,EAAE,WAAW,EAAE,wBAAwB,EAAE;SAC9G;QACD,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;KAC3B,CAAC;IACM,SAAS,GAAc;QAC7B,QAAQ,EAAE,QAAQ;QAClB,OAAO,EAAE,eAAe;QACxB,OAAO,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE;KAC/B,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,CAAC;QAClC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,CAAC;QAClC,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvE,IAAI,CAAC,QAAQ;YAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,6BAA6B,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC5G,MAAM,MAAM,GAAG,YAAY,IAAI,6CAA6C,IAAI,EAAE,CAAC;QACnF,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,qCAAqC,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;QAC5L,IAAI,MAAM,CAAC,KAAK;YAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC9F,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;IAChF,CAAC;CACF;AAjCD,oDAiCC;AAED,MAAa,uBAAuB;IACzB,EAAE,GAAG,WAAW,CAAC;IACjB,IAAI,GAAG,iCAAiC,CAAC;IACzC,WAAW,GAAG,kDAAkD,CAAC;IACjE,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,6BAA6B,CAAC;KAC1D,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,6BAA6B,EAAE;SACtE;QACD,QAAQ,EAAE,CAAC,OAAO,CAAC;KACpB,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,iFAAiF;QACjF,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,+BAA+B,KAAK,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;IAC9G,CAAC;CACF;AApBD,0DAoBC;AAED,MAAa,wBAAwB;IAC1B,EAAE,GAAG,YAAY,CAAC;IAClB,IAAI,GAAG,uBAAuB,CAAC;IAC/B,WAAW,GAAG,gDAAgD,CAAC;IAC/D,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,IAAI,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,wCAAwC,CAAC;KACpE,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,wCAAwC,EAAE;SAChF;QACD,QAAQ,EAAE,CAAC,MAAM,CAAC;KACnB,CAAC;IACM,SAAS,GAAc;QAC7B,QAAQ,EAAE,QAAQ;QAClB,OAAO,EAAE,eAAe;QACxB,OAAO,EAAE,EAAE,WAAW,EAAE,GAAG,EAAE;KAC9B,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,MAAM,IAAI,GAAG,KAAK,CAAC,IAAc,CAAC;QAClC,MAAM,QAAQ,GAAG,MAAM,uBAAU,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACvE,IAAI,CAAC,QAAQ;YAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,6BAA6B,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC5G,MAAM,MAAM,GAAG,iFAAiF,IAAI,EAAE,CAAC;QACvG,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,yCAAyC,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;QAChM,IAAI,MAAM,CAAC,KAAK;YAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;QAC9F,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;IAChF,CAAC;CACF;AA9BD,4DA8BC;AAED,MAAa,8BAA8B;IAChC,EAAE,GAAG,QAAQ,CAAC;IACd,IAAI,GAAG,6BAA6B,CAAC;IACrC,WAAW,GAAG,yDAAyD,CAAC;IACxE,IAAI,GAAG,eAAe,CAAC,CAAC,oBAAoB;IAC5C,OAAO,GAAwB,EAAE,CAAC,CAAC,oBAAoB;IACvD,kBAAkB,GAAG,OAAC,CAAC,MAAM,CAAC;QACrC,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,uCAAuC,CAAC;KACrE,CAAC,CAAC;IACM,WAAW,GAAG;QACrB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,uCAAuC,EAAE;SACjF;QACD,QAAQ,EAAE,CAAC,QAAQ,CAAC;KACrB,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,UAA8B,EAAE,KAAgB,EAAE,QAAuB;QACrF,iEAAiE;QACjE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,2BAA2B,KAAK,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC;IAC3G,CAAC;CACF;AApBD,wEAoBC", "sourcesContent": ["import { ITool, ToolInput, ToolResult } from './tool.ts.backup';\nimport { AgentContext } from '../types/agent';\nimport { llmService } from '../llm/llmService';\nimport { LLMConfig } from '../config';\nimport { z } from 'zod';\n\nexport class DocumentationGenTool implements ITool {\n  readonly id = 'docGen';\n  readonly name = 'Documentation Generation';\n  readonly description = 'Generate documentation for code or APIs.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    code: z.string().describe('Code to document.'),\n    type: z.enum(['function', 'class', 'module', 'api']).describe('Type of documentation.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      code: { type: 'string', description: 'Code to document.' },\n      type: { type: 'string', enum: ['function', 'class', 'module', 'api'], description: 'Type of documentation.' }\n    },\n    required: ['code', 'type']\n  };\n  private llmConfig: LLMConfig = {\n    provider: 'openai',\n    modelId: 'gpt-3.5-turbo',\n    options: { temperature: 0.25 }\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const code = input.code as string;\n    const type = input.type as string;\n    const provider = await llmService.getProviderForConfig(this.llmConfig);\n    if (!provider) return { success: false, error: 'No LLM provider configured.', toolId: this.id, actionName };\n    const prompt = `Generate ${type} documentation for the following code.\\n\\n${code}`;\n    const result = await provider.generate({ prompt, systemPrompt: 'You are an expert technical writer.', modelId: this.llmConfig.modelId, options: this.llmConfig.options, mode: 'generate' });\n    if (result.error) return { success: false, error: result.error, toolId: this.id, actionName };\n    return { success: true, output: result.content, toolId: this.id, actionName };\n  }\n}\n\nexport class DocumentationSearchTool implements ITool {\n  readonly id = 'docSearch';\n  readonly name = 'Documentation Search (Advanced)';\n  readonly description = 'Search documentation using web or local sources.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    query: z.string().describe('Documentation search query.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      query: { type: 'string', description: 'Documentation search query.' }\n    },\n    required: ['query']\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    // Placeholder: In real implementation, this would use web search or offline docs\n    return { success: true, output: `Searched documentation for: ${input.query}`, toolId: this.id, actionName };\n  }\n}\n\nexport class DocumentationSummaryTool implements ITool {\n  readonly id = 'docSummary';\n  readonly name = 'Documentation Summary';\n  readonly description = 'Summarize documentation or technical articles.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    text: z.string().describe('Documentation or article to summarize.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      text: { type: 'string', description: 'Documentation or article to summarize.' }\n    },\n    required: ['text']\n  };\n  private llmConfig: LLMConfig = {\n    provider: 'openai',\n    modelId: 'gpt-3.5-turbo',\n    options: { temperature: 0.2 }\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    const text = input.text as string;\n    const provider = await llmService.getProviderForConfig(this.llmConfig);\n    if (!provider) return { success: false, error: 'No LLM provider configured.', toolId: this.id, actionName };\n    const prompt = `Summarize the following documentation or article for a developer audience.\\n\\n${text}`;\n    const result = await provider.generate({ prompt, systemPrompt: 'You are an expert technical summarizer.', modelId: this.llmConfig.modelId, options: this.llmConfig.options, mode: 'generate' });\n    if (result.error) return { success: false, error: result.error, toolId: this.id, actionName };\n    return { success: true, output: result.content, toolId: this.id, actionName };\n  }\n}\n\nexport class DocumentationVisualizationTool implements ITool {\n  readonly id = 'docViz';\n  readonly name = 'Documentation Visualization';\n  readonly description = 'Visualize documentation structure or API relationships.';\n  readonly type = 'single-action'; // Required by ITool\n  readonly actions: Record<string, any> = {}; // Required by ITool\n  readonly singleActionSchema = z.object({\n    source: z.string().describe('Source code or API spec to visualize.')\n  });\n  readonly inputSchema = {\n    type: 'object',\n    properties: {\n      source: { type: 'string', description: 'Source code or API spec to visualize.' }\n    },\n    required: ['source']\n  };\n  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {\n    // Placeholder: actual visualization would require UI integration\n    return { success: true, output: `Visualization data for: ${input.source}`, toolId: this.id, actionName };\n  }\n}\n"]}