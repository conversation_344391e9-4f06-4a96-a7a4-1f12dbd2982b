/* Provider Settings Panel Styles */

:root {
    --container-padding: 20px;
    --input-padding-vertical: 6px;
    --input-padding-horizontal: 8px;
    --input-margin-vertical: 4px;
    --input-margin-horizontal: 0;
}

body {
    padding: 0;
    margin: 0;
    color: var(--vscode-foreground);
    font-size: var(--vscode-font-size);
    font-weight: var(--vscode-font-weight);
    font-family: var(--vscode-font-family);
    background-color: var(--vscode-editor-background);
}

.config-container {
    display: flex;
    flex-direction: column;
    padding: var(--container-padding);
    max-width: 800px;
    margin: 0 auto;
}

.config-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--vscode-panel-border);
}

.logo {
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

h1 {
    font-size: 1.5em;
    font-weight: 600;
    margin: 0;
    color: var(--vscode-editor-foreground);
}

h2 {
    font-size: 1.2em;
    font-weight: 600;
    margin: 0 0 10px 0;
    color: var(--vscode-editor-foreground);
}

.provider-info {
    margin-bottom: 20px;
    padding: 10px;
    background-color: var(--vscode-editor-inactiveSelectionBackground);
    border-radius: 4px;
}

.provider-info p {
    margin: 5px 0;
}

.provider-info a {
    color: var(--vscode-textLink-foreground);
    text-decoration: none;
}

.provider-info a:hover {
    text-decoration: underline;
}

.config-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-section {
    padding: 15px;
    background-color: var(--vscode-editor-inactiveSelectionBackground);
    border-radius: 4px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group:last-child {
    margin-bottom: 0;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.form-control {
    width: 100%;
    padding: var(--input-padding-vertical) var(--input-padding-horizontal);
    border: 1px solid var(--vscode-input-border);
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border-radius: 2px;
    font-family: var(--vscode-font-family);
    font-size: var(--vscode-font-size);
}

.form-control:focus {
    outline: 1px solid var(--vscode-focusBorder);
}

.description {
    font-size: 0.9em;
    color: var(--vscode-descriptionForeground);
    margin-top: 4px;
}

.checkbox-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid var(--vscode-input-border);
    background-color: var(--vscode-input-background);
    border-radius: 2px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.btn {
    padding: 6px 14px;
    border: none;
    border-radius: 2px;
    font-family: var(--vscode-font-family);
    font-size: var(--vscode-font-size);
    cursor: pointer;
}

.btn:focus {
    outline: 1px solid var(--vscode-focusBorder);
}

.btn.primary {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.btn.primary:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.btn.secondary {
    background-color: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
}

.btn.secondary:hover {
    background-color: var(--vscode-button-secondaryHoverBackground);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#connection-status, #default-status {
    margin-left: 10px;
    font-size: 0.9em;
}

.success {
    color: var(--vscode-testing-iconPassed);
}

.error {
    color: var(--vscode-testing-iconFailed);
}

.no-items {
    font-style: italic;
    color: var(--vscode-descriptionForeground);
    padding: 5px;
}

/* Models list styles */
.models-list-container {
    margin-top: 15px;
}

.models-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--vscode-input-border);
    background-color: var(--vscode-input-background);
    border-radius: 2px;
    padding: 5px;
    margin-top: 5px;
}

.model-item {
    display: flex;
    padding: 8px;
    border-bottom: 1px solid var(--vscode-panel-border);
    cursor: pointer;
}

.model-item:last-child {
    border-bottom: none;
}

.model-item:hover {
    background-color: var(--vscode-list-hoverBackground);
}

.model-item.selected {
    background-color: var(--vscode-list-activeSelectionBackground);
    color: var(--vscode-list-activeSelectionForeground);
}

.model-info {
    flex: 1;
}

.model-name {
    font-weight: 500;
    margin-bottom: 3px;
}

.model-description {
    font-size: 0.9em;
    color: var(--vscode-descriptionForeground);
}

.loading-models {
    padding: 10px;
    text-align: center;
    font-style: italic;
    color: var(--vscode-descriptionForeground);
}

.model-context-window {
    font-size: 0.85em;
    color: var(--vscode-descriptionForeground);
    margin-top: 3px;
}

#refresh-status {
    margin-left: 10px;
    font-size: 0.9em;
}

/* Tabs styles */
.tabs-container {
    margin-bottom: 20px;
}

.tabs-header {
    display: flex;
    overflow-x: auto;
    border-bottom: 1px solid var(--vscode-panel-border);
}

.tab {
    padding: 10px 15px;
    cursor: pointer;
    border: 1px solid transparent;
    border-bottom: none;
    margin-right: 5px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background-color: var(--vscode-tab-inactiveBackground);
    color: var(--vscode-tab-inactiveForeground);
}

.tab:hover {
    background-color: var(--vscode-tab-hoverBackground);
}

.tab.active {
    background-color: var(--vscode-tab-activeBackground);
    color: var(--vscode-tab-activeForeground);
    border-color: var(--vscode-panel-border);
    border-bottom: 1px solid var(--vscode-tab-activeBackground);
    position: relative;
    bottom: -1px;
}

.tab-content {
    display: none;
    padding: 15px 0;
}

.tab-content.active {
    display: block;
}

/* Provider category styles */
.provider-category {
    margin-bottom: 20px;
}

.provider-category-title {
    font-size: 1.1em;
    font-weight: 600;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--vscode-panel-border);
}

.provider-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.provider-card {
    padding: 15px;
    background-color: var(--vscode-editor-inactiveSelectionBackground);
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.provider-card:hover {
    background-color: var(--vscode-list-hoverBackground);
}

.provider-card.selected {
    background-color: var(--vscode-list-activeSelectionBackground);
    color: var(--vscode-list-activeSelectionForeground);
}

.provider-name {
    font-weight: 500;
    margin-bottom: 5px;
}

.provider-description {
    font-size: 0.9em;
    color: var(--vscode-descriptionForeground);
    margin-bottom: 10px;
}

/* GGUF model management styles */
.gguf-models-container {
    margin-top: 20px;
}

.gguf-model-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.gguf-model-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--vscode-input-border);
    background-color: var(--vscode-input-background);
    border-radius: 2px;
}

.gguf-model-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid var(--vscode-panel-border);
}

.gguf-model-item:last-child {
    border-bottom: none;
}

.gguf-model-info {
    flex: 1;
}

.gguf-model-name {
    font-weight: 500;
}

.gguf-model-path {
    font-size: 0.85em;
    color: var(--vscode-descriptionForeground);
    margin-top: 3px;
}

.gguf-model-size {
    font-size: 0.85em;
    color: var(--vscode-descriptionForeground);
}

.gguf-model-actions-buttons {
    display: flex;
    gap: 5px;
}
