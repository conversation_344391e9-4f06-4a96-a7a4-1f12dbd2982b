// Import core Node.js modules
import * as os from 'os';
import * as path from 'path';
import { promises as fs } from 'fs';
import { EventEmitter } from 'events';

// Import VSCode API with a namespace to avoid conflicts
import * as vscode from 'vscode';

// Re-export commonly used VSCode types for internal use
type CancellationToken = vscode.CancellationToken;
type ExtensionContext = vscode.ExtensionContext;
type Progress<T> = vscode.Progress<T>;
type OutputChannel = vscode.OutputChannel;
type Event<T> = vscode.Event<T>;
type Uri = vscode.Uri;
type TextEditor = vscode.TextEditor;
type TextDocument = vscode.TextDocument;
type ViewColumn = vscode.ViewColumn;
type Range = vscode.Range;
type TextDocumentShowOptions = vscode.TextDocumentShowOptions;
type TextEditorRevealType = vscode.TextEditorRevealType;

// Import core terminal functionality
import {
    InteractiveSession,
    ITerminalProcess,
    TerminalActions,
    TerminalConfig,
    CommandResult,
    TerminalState,
    TerminalStats,
    TerminalActionProvider,
    IAIContext,
    IAIToolPattern,
    IToolTerminalContext,
    ITerminalToolEvents,
    QuickFixPattern
} from './pseudoTerminal';
import { getConfig } from '../config';
import type { IMemoryOperations, MemoryEntry, MemorySearchOptions, MemorySource, MemoryType } from '../memory/types';

// Import comprehensive memory system
import { MemoryManager } from '../memory/memoryManager';
import { CodessaMemoryProvider } from '../memory/codessa/codessaMemory';
import { CodessaGraphMemory } from '../memory/codessa/codessaGraphMemory';
import { FileChunkingService } from '../memory/codessa/fileChunking';

// Import workflow types and implementations
import { Workflow, WorkflowContext, WorkflowStepResult, WorkflowStep, WorkflowDefinition } from '../agents/workflows/workflowEngine';
import { workflowManager } from '../agents/workflows/workflowManager';
import { workflowRegistry } from '../agents/workflows/workflowRegistry';

// Import Zod for schema validation
import { z, ZodError } from 'zod';

/**
 * Enhanced interfaces for comprehensive tool integration
 */

// Enhanced command result interface
export interface CommandResult {
    exitCode: number;
    output: string;
    command: string;
    duration: number;
    success: boolean;
    completedAt: Date;

    // Enhanced result information
    codeActions?: vscode.CodeAction[];
    diagnostics?: vscode.Diagnostic[];
    quickFixes?: CodeActionResult[];
    aiAnalysis?: {
        confidence: number;
        suggestions: string[];
        category: string;
    };
}

// Code action result with metadata
export interface CodeActionResult {
    action: vscode.CodeAction;
    priority: number;
    category: string;
    confidence: number;
    metadata: {
        source: string;
        trigger: string;
        context: Record<string, unknown>;
    };
}

// Tool operation memory interface
export interface IToolOperationMemory {
    id: string;
    toolName: string;
    operation: string;
    input: Record<string, unknown>;
    output?: unknown;
    metadata: {
        source: MemorySource;
        type: MemoryType;
        tags: string[];
        importance: number;
        workspaceContext?: string;
        affectedFiles?: string[];
        success?: boolean;
        [key: string]: unknown;
    };
    state: {
        status: 'pending' | 'running' | 'completed' | 'failed';
        startTime: number;
        endTime?: number;
    };
    duration?: number;
}

/**
 * Enhanced base class for all AI-enhanced tools with comprehensive memory and workflow integration.
 * This class provides a unified interface for terminal operations, code actions, memory management,
 * workflow execution, and AI-powered features.
 */
export abstract class AITerminalTool extends TerminalActionProvider {
    private readonly _terminalSession: InteractiveSession;
    private readonly _aiContext: IAIContext;
    private readonly _toolContextMap: Map<string, IToolTerminalContext> = new Map();
    private readonly _onProcessOutput = new vscode.EventEmitter<string>();
    private readonly _onAIAnalysis = new vscode.EventEmitter<{
        action: vscode.CodeAction;
        context: unknown;
        aiResponse?: unknown;
    }>();

    // Enhanced memory and workflow integration
    private readonly _memoryManager: MemoryManager;
    private readonly _workflowManager: typeof workflowManager;
    private readonly _workflowRegistry: typeof workflowRegistry;
    private readonly _operationMemory: Map<string, IToolOperationMemory> = new Map();
    private readonly _activeWorkflows: Map<string, Workflow> = new Map();

    // Event emitters for memory and workflow operations
    private readonly _onMemoryOperation = new vscode.EventEmitter<{
        operation: string;
        memory: MemoryEntry;
        success: boolean;
    }>();
    private readonly _onWorkflowExecution = new vscode.EventEmitter<{
        workflowId: string;
        step: string;
        result: WorkflowStepResult;
    }>();

    public readonly onAIAnalysis: vscode.Event<{
        action: vscode.CodeAction;
        context: unknown;
        aiResponse?: unknown;
    }> = this._onAIAnalysis.event;
    public readonly onProcessOutput = this._onProcessOutput.event;
    public readonly onMemoryOperation = this._onMemoryOperation.event;
    public readonly onWorkflowExecution = this._onWorkflowExecution.event;

    constructor(
        services: {
            terminalSession: InteractiveSession;
            aiContext: IAIContext;
            toolContext: IToolTerminalContext;
            memoryManager?: MemoryManager;
            workflowManager?: typeof workflowManager;
        }
    ) {
        super();
        this._terminalSession = services.terminalSession;
        this._aiContext = services.aiContext;
        this._toolContextMap.set(services.toolContext.session.id, services.toolContext);

        // Initialize memory and workflow managers
        this._memoryManager = services.memoryManager || MemoryManager.getInstance();
        this._workflowManager = services.workflowManager || workflowManager;
        this._workflowRegistry = workflowRegistry;

        // Initialize patterns and integrations
        this.initializeCommonPatterns();
        this.initializeMemoryIntegration();
        this.initializeWorkflowIntegration();

        // Set up event handlers
        const events = this.getTerminalEvents();
        events.onPatternMatch(e => {
            if (e.pattern.action === 'quick-fix') {
                this._handleQuickFixPattern(e.pattern as QuickFixPattern, e.matches);
            }
        });
    }

    public getToolContext(sessionId: string): IToolTerminalContext | undefined {
        return this._toolContextMap.get(sessionId);
    }

    protected getAIContext(): IAIContext {
        return this._aiContext;
    }

    protected abstract executeAIOperation(context: IAIContext, input: unknown): Promise<unknown>;

    protected getTerminalSession(): InteractiveSession {
        return this._terminalSession;
    }

    /**
     * Get the memory manager instance
     */
    protected getMemoryManager(): MemoryManager {
        return this._memoryManager;
    }

    /**
     * Get the workflow manager instance
     */
    protected getWorkflowManager(): typeof workflowManager {
        return this._workflowManager;
    }

    /**
     * Get the workflow registry instance
     */
    protected getWorkflowRegistry(): typeof workflowRegistry {
        return this._workflowRegistry;
    }

    /**
     * Initialize memory integration for the tool
     */
    private initializeMemoryIntegration(): void {
        // Set up memory event handlers
        this.onMemoryOperation(async (event) => {
            // Store operation in tool memory
            await this.storeOperationMemory({
                operation: event.operation,
                input: { memoryId: event.memory.id },
                output: event.memory,
                success: event.success,
                metadata: {
                    source: 'memory-operation' as MemorySource,
                    type: 'procedural' as MemoryType,
                    tags: ['memory', event.operation],
                    importance: event.success ? 0.8 : 0.3
                }
            });
        });
    }

    /**
     * Initialize workflow integration for the tool
     */
    private initializeWorkflowIntegration(): void {
        // Set up workflow event handlers
        this.onWorkflowExecution(async (event) => {
            // Store workflow execution in tool memory
            await this.storeOperationMemory({
                operation: 'workflow-execution',
                input: { workflowId: event.workflowId, step: event.step },
                output: event.result,
                success: event.result.success,
                metadata: {
                    source: 'workflow' as MemorySource,
                    type: 'procedural' as MemoryType,
                    tags: ['workflow', event.workflowId, event.step],
                    importance: event.result.success ? 0.9 : 0.4
                }
            });
        });
    }

    /**
     * Store operation memory with enhanced metadata
     */
    protected async storeOperationMemory(operation: {
        operation: string;
        input: Record<string, unknown>;
        output?: unknown;
        success: boolean;
        metadata?: Partial<{
            source: MemorySource;
            type: MemoryType;
            tags: string[];
            importance: number;
            workspaceContext: string;
            affectedFiles: string[];
        }>;
    }): Promise<IToolOperationMemory> {
        const operationMemory = this.createOperationMemory(
            this.constructor.name,
            operation.operation,
            operation.input,
            {
                ...operation.metadata,
                success: operation.success
            }
        );

        operationMemory.output = operation.output;
        operationMemory.state.status = operation.success ? 'completed' : 'failed';
        operationMemory.state.endTime = Date.now();
        operationMemory.duration = operationMemory.state.endTime - operationMemory.state.startTime;

        // Store in local operation memory
        this._operationMemory.set(operationMemory.id, operationMemory);

        // Store in global memory system
        try {
            await this._memoryManager.addMemory({
                content: JSON.stringify({
                    toolName: operationMemory.toolName,
                    operation: operationMemory.operation,
                    input: operationMemory.input,
                    output: operationMemory.output,
                    duration: operationMemory.duration,
                    success: operation.success
                }),
                metadata: operationMemory.metadata
            });
        } catch (error) {
            Logger.instance.error('Failed to store operation in memory:', error);
        }

        return operationMemory;
    }

    /**
     * Create operation memory entry
     */
    protected createOperationMemory(
        toolName: string,
        operation: string,
        input: Record<string, unknown>,
        metadata: Partial<{
            source: MemorySource;
            type: MemoryType;
            tags: string[];
            importance: number;
            workspaceContext: string;
            affectedFiles: string[];
            success: boolean;
        }> = {}
    ): IToolOperationMemory {
        return {
            id: `${toolName}-${operation}-${Date.now()}`,
            toolName,
            operation,
            input,
            metadata: {
                source: metadata.source || 'tool' as MemorySource,
                type: metadata.type || 'procedural' as MemoryType,
                tags: metadata.tags || [toolName.toLowerCase(), operation],
                importance: metadata.importance || 0.5,
                workspaceContext: metadata.workspaceContext,
                affectedFiles: metadata.affectedFiles,
                success: metadata.success,
                timestamp: Date.now()
            },
            state: {
                status: 'pending',
                startTime: Date.now()
            }
        };
    }

    /**
     * Comprehensive memory operations for tools
     */

    /**
     * Store content in memory with enhanced metadata
     */
    protected async storeMemory(
        content: string,
        metadata: Partial<{
            source: MemorySource;
            type: MemoryType;
            tags: string[];
            importance: number;
            workspaceContext: string;
            affectedFiles: string[];
        }> = {}
    ): Promise<MemoryEntry> {
        const memoryEntry = await this._memoryManager.addMemory({
            content,
            metadata: {
                source: metadata.source || 'tool' as MemorySource,
                type: metadata.type || 'procedural' as MemoryType,
                tags: metadata.tags || [this.constructor.name.toLowerCase()],
                importance: metadata.importance || 0.5,
                workspaceContext: metadata.workspaceContext,
                affectedFiles: metadata.affectedFiles,
                toolName: this.constructor.name,
                timestamp: Date.now()
            }
        });

        this._onMemoryOperation.fire({
            operation: 'store',
            memory: memoryEntry,
            success: true
        });

        return memoryEntry;
    }

    /**
     * Retrieve memories based on search criteria
     */
    protected async searchMemories(
        query: string,
        options: Partial<MemorySearchOptions> = {}
    ): Promise<MemoryEntry[]> {
        try {
            const memories = await this._memoryManager.searchSimilarMemories(query, {
                limit: options.limit || 10,
                threshold: options.threshold || 0.7,
                source: options.source,
                type: options.type,
                tags: options.tags ? [...options.tags, this.constructor.name.toLowerCase()] : [this.constructor.name.toLowerCase()],
                ...options
            });

            this._onMemoryOperation.fire({
                operation: 'search',
                memory: { id: 'search-result', content: query, metadata: { source: 'tool' as MemorySource, type: 'query' as MemoryType } } as MemoryEntry,
                success: true
            });

            return memories;
        } catch (error) {
            Logger.instance.error('Failed to search memories:', error);
            this._onMemoryOperation.fire({
                operation: 'search',
                memory: { id: 'search-error', content: query, metadata: { source: 'tool' as MemorySource, type: 'query' as MemoryType } } as MemoryEntry,
                success: false
            });
            return [];
        }
    }

    /**
     * Comprehensive workflow operations for tools
     */

    /**
     * Execute a workflow with the given inputs
     */
    protected async executeWorkflow(
        workflowId: string,
        inputs: Record<string, any> = {},
        context?: Partial<WorkflowContext>
    ): Promise<WorkflowStepResult> {
        try {
            // Get workflow from registry
            const workflowDefinition = this._workflowRegistry.getWorkflow(workflowId);
            if (!workflowDefinition) {
                throw new Error(`Workflow not found: ${workflowId}`);
            }

            // Create workflow instance
            const workflow = new Workflow(workflowDefinition);

            // Store active workflow
            this._activeWorkflows.set(workflowId, workflow);

            // Set up progress tracking
            workflow.onProgress((stepId, progress) => {
                this._onWorkflowExecution.fire({
                    workflowId,
                    step: stepId,
                    result: {
                        success: true,
                        output: { progress },
                        metadata: { stepId, progress }
                    }
                });
            });

            // Execute workflow
            const result = await workflow.execute(inputs, context);

            // Store workflow execution in memory
            await this.storeOperationMemory({
                operation: 'workflow-execution',
                input: { workflowId, inputs },
                output: result,
                success: result.success,
                metadata: {
                    source: 'workflow' as MemorySource,
                    type: 'procedural' as MemoryType,
                    tags: ['workflow', workflowId],
                    importance: result.success ? 0.9 : 0.4,
                    workspaceContext: context?.workflow?.getDefinition().name
                }
            });

            // Clean up active workflow
            this._activeWorkflows.delete(workflowId);

            return result;
        } catch (error) {
            Logger.instance.error(`Failed to execute workflow ${workflowId}:`, error);

            // Clean up active workflow
            this._activeWorkflows.delete(workflowId);

            const errorResult: WorkflowStepResult = {
                success: false,
                error: error instanceof Error ? error.message : String(error),
                metadata: { workflowId, error: true }
            };

            this._onWorkflowExecution.fire({
                workflowId,
                step: 'error',
                result: errorResult
            });

            return errorResult;
        }
    }

    /**
     * Create and register a new workflow
     */
    protected createWorkflow(
        definition: WorkflowDefinition
    ): void {
        this._workflowRegistry.registerWorkflow(definition);
        Logger.instance.info(`Workflow registered: ${definition.id}`);
    }

    /**
     * Get available workflows by tag
     */
    protected getWorkflowsByTag(tag: string): WorkflowDefinition[] {
        return this._workflowRegistry.getWorkflowsByTag(tag);
    }

    /**
     * Cancel an active workflow
     */
    protected async cancelWorkflow(workflowId: string): Promise<boolean> {
        const workflow = this._activeWorkflows.get(workflowId);
        if (workflow) {
            try {
                await workflow.cancel();
                this._activeWorkflows.delete(workflowId);

                this._onWorkflowExecution.fire({
                    workflowId,
                    step: 'cancelled',
                    result: {
                        success: false,
                        error: 'Workflow cancelled',
                        metadata: { cancelled: true }
                    }
                });

                return true;
            } catch (error) {
                Logger.instance.error(`Failed to cancel workflow ${workflowId}:`, error);
                return false;
            }
        }
        return false;
    }

    /**
     * Get the terminal events for the current tool context
     */
    protected getTerminalEvents(): ITerminalToolEvents {
        const toolContext = Array.from(this._toolContextMap.values())[0];
        const eventEmitter = new vscode.EventEmitter<{
            pattern: IAIToolPattern;
            matches: RegExpMatchArray;
        }>();

        if (toolContext) {
            // Bind terminal events to the tool context
            toolContext.provider.onPatternMatch(e => {
                eventEmitter.fire(e);
            });
        }

        return {
            ...this.getTerminalSession().events,
            onAIAction: this.onAIAnalysis,
            onPatternMatch: eventEmitter.event
        };
    }

    /**
     * Handle quick fix patterns in terminal output
     */
    protected _handleQuickFixPattern(pattern: QuickFixPattern, matches: RegExpMatchArray): Promise<void> {
        if (!pattern || !matches) {
            return Promise.resolve();
        }

        const editor = vscode.window.activeTextEditor;
        if (!editor) return Promise.resolve();

        const action = pattern.fix(matches, editor.document, new vscode.Range(0, 0, 0, 0));
        if (action) {
            this._onAIAnalysis.fire({
                action,
                context: {
                    pattern,
                    matches,
                    timestamp: Date.now()
                }
            });
        }

        return Promise.resolve();
    }

    /**
     * Initialize common patterns that all tools might need
     */
    protected initializeCommonPatterns(): void {
        // Common error patterns
        this.registerPattern({
            pattern: /error|exception|failed/i,
            action: 'analyze-error',
            priority: 100,
            category: 'error',
            fix: (match: RegExpMatchArray, document: vscode.TextDocument) => {
                const action = new vscode.CodeAction(
                    'Analyze Error',
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.analyzeError',
                    title: 'Analyze Error',
                    arguments: [match[0], document.uri.toString()]
                };
                return action;
            }
        });

        // Common warning patterns
        this.registerPattern({
            pattern: /warning|warn/i,
            action: 'analyze-warning',
            priority: 90,
            category: 'warning',
            fix: (match: RegExpMatchArray, document: vscode.TextDocument) => {
                const action = new vscode.CodeAction(
                    'Analyze Warning',
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.analyzeWarning',
                    title: 'Analyze Warning',
                    arguments: [match[0], document.uri.toString()]
                };
                return action;
            }
        });

        // Common success patterns
        this.registerPattern({
            pattern: /success|completed|done/i,
            action: 'process-success',
            priority: 80,
            category: 'success',
            fix: (match: RegExpMatchArray, document: vscode.TextDocument) => {
                const action = new vscode.CodeAction(
                    'Process Success',
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.processSuccess',
                    title: 'Process Success',
                    arguments: [match[0], document.uri.toString()]
                };
                return action;
            }
        });
    }

    /**
     * Execute a command with AI-enhanced error handling and suggestions
     */
    protected async executeWithAI(
        command: string,
        options?: {
            timeout?: number;
            expectPrompt?: boolean;
            background?: boolean;
            workingDirectory?: string;
        }
    ): Promise<CommandResult> {
        try {
            const result = await this._terminalSession.executeCommand(command, options);
            if (typeof result === 'string') {
                await this.processTerminalOutput(result);
                return {
                    exitCode: 0,
                    output: result,
                    command,
                    duration: 0,
                    success: true,
                    completedAt: new Date()
                };
            }
            return result;
        } catch (error) {
            // Create error diagnostics and trigger AI analysis
            const diagnostic = new vscode.Diagnostic(
                new vscode.Range(0, 0, 0, 0),
                `Command failed: ${error instanceof Error ? error.message : String(error)}`,
                vscode.DiagnosticSeverity.Error
            );
            diagnostic.source = 'terminal';
            
            const diagnostics = [diagnostic];
            this.addDiagnostics(vscode.Uri.parse('terminal://output'), diagnostics);

            // Process error with AI
            await this.handleAIProcessing(
                error instanceof Error ? error.message : String(error),
                []
            );
            throw error;
        }
    }

    /**
     * Add diagnostics to the terminal
     */
    protected addDiagnostics(uri: vscode.Uri, diagnostics: vscode.Diagnostic[]): void {
        const collection = vscode.languages.createDiagnosticCollection('terminal');
        collection.set(uri, diagnostics);
    }

    /**
     * Register a new AI pattern for terminal output analysis
     */
    protected registerAIPattern(pattern: IAIToolPattern): void {
        this.registerPattern(pattern);
    }

    /**
     * Comprehensive terminal capabilities integration with code actions
     */

    /**
     * Execute command with enhanced code action analysis
     */
    protected async executeTerminalCommandWithCodeActions(
        command: string,
        options?: {
            timeout?: number;
            expectPrompt?: boolean;
            background?: boolean;
            workingDirectory?: string;
            enableCodeActions?: boolean;
            storeInMemory?: boolean;
        }
    ): Promise<CommandResult> {
        const startTime = Date.now();
        const config = {
            timeout: options?.timeout ?? 30000,
            expectPrompt: options?.expectPrompt ?? true,
            background: options?.background ?? false,
            workingDirectory: options?.workingDirectory,
            enableCodeActions: options?.enableCodeActions ?? true,
            storeInMemory: options?.storeInMemory ?? true
        };

        try {
            // Execute command using terminal session
            const result = await this._terminalSession.executeCommandAdvanced(command, {
                timeout: config.timeout,
                expectPrompt: config.expectPrompt,
                background: config.background,
                workingDirectory: config.workingDirectory
            });

            // Enhanced result with code actions
            const enhancedResult: CommandResult = {
                ...result,
                codeActions: [],
                diagnostics: [],
                quickFixes: [],
                aiAnalysis: undefined
            };

            // Analyze output for code actions if enabled
            if (config.enableCodeActions) {
                enhancedResult.codeActions = this.analyzeTerminalOutputForCodeActions(result.output);
                enhancedResult.diagnostics = this.generateDiagnosticsFromOutput(result.output);
                enhancedResult.quickFixes = this.createQuickFixesFromActions(enhancedResult.codeActions);
            }

            // AI analysis for complex outputs
            if (this.shouldProcessWithAI(result.output)) {
                enhancedResult.aiAnalysis = await this.performAIAnalysis(result.output);
            }

            // Store in memory if enabled
            if (config.storeInMemory) {
                await this.storeOperationMemory({
                    operation: 'terminal-command',
                    input: { command, options: config },
                    output: enhancedResult,
                    success: result.success,
                    metadata: {
                        source: 'terminal' as MemorySource,
                        type: 'procedural' as MemoryType,
                        tags: ['terminal', 'command', result.success ? 'success' : 'error'],
                        importance: result.success ? 0.6 : 0.8,
                        affectedFiles: this.extractFilePathsFromOutput(result.output)
                    }
                });
            }

            return enhancedResult;
        } catch (error) {
            const errorResult: CommandResult = {
                exitCode: -1,
                output: error instanceof Error ? error.message : String(error),
                command,
                duration: Date.now() - startTime,
                success: false,
                completedAt: new Date(),
                codeActions: [],
                diagnostics: [{
                    range: new vscode.Range(0, 0, 0, 0),
                    message: `Command failed: ${error instanceof Error ? error.message : String(error)}`,
                    severity: vscode.DiagnosticSeverity.Error,
                    source: 'terminal'
                }],
                quickFixes: [],
                aiAnalysis: {
                    confidence: 0.9,
                    suggestions: ['Check command syntax', 'Verify permissions', 'Check file paths'],
                    category: 'error'
                }
            };

            // Store error in memory
            if (config.storeInMemory) {
                await this.storeOperationMemory({
                    operation: 'terminal-command-error',
                    input: { command, options: config },
                    output: errorResult,
                    success: false,
                    metadata: {
                        source: 'terminal' as MemorySource,
                        type: 'procedural' as MemoryType,
                        tags: ['terminal', 'command', 'error'],
                        importance: 0.9
                    }
                });
            }

            return errorResult;
        }
    }

    /**
     * Analyze terminal output for code actions using integrated providers
     */
    protected analyzeTerminalOutputForCodeActions(output: string): vscode.CodeAction[] {
        const actions: vscode.CodeAction[] = [];

        // Error patterns with code actions
        if (/command not found|not recognized/i.test(output)) {
            const match = output.match(/command not found:\s*(.+?)$/m) || output.match(/'(.+?)' is not recognized/);
            if (match) {
                const action = new vscode.CodeAction(
                    `Install missing command: ${match[1]}`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.terminal.installCommand',
                    title: `Install ${match[1]}`,
                    arguments: [match[1]]
                };
                actions.push(action);
            }
        }

        // Permission errors
        if (/permission denied|access denied|EACCES/i.test(output)) {
            const action = new vscode.CodeAction(
                'Fix permission issues',
                vscode.CodeActionKind.QuickFix
            );
            action.command = {
                command: 'codessa.terminal.fixPermissions',
                title: 'Fix permissions',
                arguments: [output]
            };
            actions.push(action);
        }

        // File not found errors
        if (/no such file or directory|cannot find|file not found/i.test(output)) {
            const fileMatch = output.match(/(?:no such file or directory|cannot find|file not found):\s*(.+?)$/m);
            if (fileMatch) {
                const action = new vscode.CodeAction(
                    `Create missing file: ${fileMatch[1]}`,
                    vscode.CodeActionKind.QuickFix
                );
                action.command = {
                    command: 'codessa.terminal.createFile',
                    title: `Create ${fileMatch[1]}`,
                    arguments: [fileMatch[1]]
                };
                actions.push(action);
            }
        }

        return actions;
    }

    /**
     * Generate diagnostics from terminal output
     */
    protected generateDiagnosticsFromOutput(output: string): vscode.Diagnostic[] {
        const diagnostics: vscode.Diagnostic[] = [];
        const lines = output.split('\n');

        lines.forEach((line, index) => {
            // Error patterns
            if (/error:|ERROR:|failed/i.test(line)) {
                diagnostics.push(new vscode.Diagnostic(
                    new vscode.Range(index, 0, index, line.length),
                    line.trim(),
                    vscode.DiagnosticSeverity.Error
                ));
            }
            // Warning patterns
            else if (/warning:|WARN:|deprecated/i.test(line)) {
                diagnostics.push(new vscode.Diagnostic(
                    new vscode.Range(index, 0, index, line.length),
                    line.trim(),
                    vscode.DiagnosticSeverity.Warning
                ));
            }
        });

        return diagnostics;
    }

    /**
     * Create quick fixes from code actions
     */
    protected createQuickFixesFromActions(actions: vscode.CodeAction[]): CodeActionResult[] {
        return actions.map(action => ({
            action,
            priority: this.getActionPriority(action),
            category: this.getActionCategory(action),
            confidence: this.getActionConfidence(action),
            metadata: {
                source: 'terminal',
                trigger: 'auto',
                context: {
                    timestamp: Date.now(),
                    toolName: this.constructor.name
                }
            }
        }));
    }

    /**
     * Utility methods for AI analysis and file path extraction
     */
    protected shouldProcessWithAI(output: string): boolean {
        return output.includes('error') ||
               output.includes('warning') ||
               output.includes('failed') ||
               output.includes('exception');
    }

    protected async performAIAnalysis(output: string): Promise<{
        confidence: number;
        suggestions: string[];
        category: string;
    }> {
        // Basic AI analysis - can be enhanced with actual AI service
        const suggestions: string[] = [];
        let category = 'general';
        let confidence = 0.5;

        if (output.includes('error')) {
            category = 'error';
            confidence = 0.8;
            suggestions.push('Check error message for specific details');
            suggestions.push('Verify input parameters and file paths');
        }

        if (output.includes('permission')) {
            suggestions.push('Check file/directory permissions');
            suggestions.push('Run with elevated privileges if needed');
            confidence = 0.9;
        }

        return { confidence, suggestions, category };
    }

    protected extractFilePathsFromOutput(output: string): string[] {
        const paths: string[] = [];
        const pathRegex = /(?:[a-zA-Z]:\\|\/)[^\s]+/g;
        const matches = output.match(pathRegex);

        if (matches) {
            paths.push(...matches.filter(path =>
                path.includes('.') || path.endsWith('/') || path.endsWith('\\')
            ));
        }

        return paths;
    }

    protected getActionPriority(action: vscode.CodeAction): number {
        if (action.kind?.value.includes('quickfix')) return 100;
        if (action.kind?.value.includes('refactor')) return 80;
        return 50;
    }

    protected getActionCategory(action: vscode.CodeAction): string {
        if (action.kind?.value.includes('quickfix')) return 'quickfix';
        if (action.kind?.value.includes('refactor')) return 'refactor';
        return 'general';
    }

    protected getActionConfidence(action: vscode.CodeAction): number {
        const title = action.title.toLowerCase();
        if (title.includes('install') || title.includes('create')) return 0.9;
        if (title.includes('fix') || title.includes('analyze')) return 0.8;
        return 0.7;
    }

    public dispose(): void {
        super.dispose();
        this._terminalSession.dispose();
    }
}

// Import node-fetch for HTTP requests
import fetch from 'node-fetch';
import { randomUUID as cryptoRandomUUID } from 'crypto';

// Use Node.js crypto.randomUUID() for secure UUID generation
const randomUUID = (): string => {
    return cryptoRandomUUID();
};


// #region PART 1: Core Framework Contracts & Interfaces

// ===================================================================================
//                        CORE INTERFACES & DATA STRUCTURES
// ===================================================================================

/**
 * Represents multimodal data that can be processed by the AI system.
 * This includes various types of media such as images, audio, or other binary data.
 * Used for clipboard operations and file-based multimodal content processing.
 *
 * @interface MultimodalData
 * @property {'image/jpeg' | 'image/png' | 'image/webp'} mimeType - The MIME type of the data (e.g., 'image/jpeg', 'image/png')
 * @property {string} base64Data - The base64-encoded content of the data
 * @property {'clipboard' | 'file'} source - The source of the multimodal data
 */
export interface MultimodalData {
    mimeType: 'image/jpeg' | 'image/png' | 'image/webp';
    base64Data: string;
    source: 'clipboard' | 'file';
}

/**
 * Represents a single content part of a prompt for a language model.
 * This can be either text or image content that will be sent to the language model.
 * 
 * @property {'text' | 'image'} type - The type of the content part
 * @property {string} [text] - The text content (for type 'text')
 * @property {object} [source] - The image source (for type 'image')
 * @property {'base64'} source.type - The type of image source (currently only base64 is supported)
 * @property {string} source.media_type - The MIME type of the image
 * @property {string} source.data - The base64-encoded image data
 */
export type LanguageModelPromptPart =
    | { type: 'text'; text: string; }
    | { type: 'image'; source: { type: 'base64'; media_type: MultimodalData['mimeType']; data: string; }; };

/**
 * Represents a complete prompt that can be sent to a language model.
 * A prompt consists of an array of content parts, which can include both text and images.
 * This allows for multimodal inputs to the language model.
 * 
 * @type {Array<LanguageModelPromptPart>}
 */
export type LanguageModelPrompt = LanguageModelPromptPart[];

/**
 * Represents a hierarchical directory structure with files and subdirectories.
 * This is used to model the file system structure in a way that can be easily
 * traversed and manipulated by tools and services.
 * 
 * @property {string} path - The full path to the directory
 * @property {string} name - The name of the directory
 * @property {Array<DirectoryTree | FileEntry>} children - Array of child entries (files or subdirectories)
 * @property {'directory'} type - Type discriminator, always 'directory' for directories
 */
export interface DirectoryTree {
    path: string;
    name: string;
    children: (DirectoryTree | FileEntry)[];
    type: 'directory';
}

/**
 * Represents a file entry in a directory structure.
 * 
 * @property {string} path - The full path to the file
 * @property {string} name - The name of the file
 * @property {'file'} type - Type discriminator, always 'file' for files
 */
type FileEntry = {
    path: string;
    name: string;
    type: 'file';
};

/**
 * Represents the memory schema for a tool's operations.
 * This interface defines the structure for storing and retrieving
 * tool-specific memory entries including operation history and context.
 */
export interface IToolMemorySchema {
    /** Unique identifier for this memory record */
    id: string;

    /** The name of the tool that generated this memory */
    toolName: string;

    /** The specific operation or action performed by the tool */
    operation: string;

    /** Timestamp when this memory was created */
    timestamp: number;

    /** The input parameters used for the operation */
    input: Record<string, unknown>;

    /** The result or output of the operation */
    output?: unknown;

    /** Any errors that occurred during the operation */
    error?: {
        message: string;
        code?: string;
        stack?: string;
    };

    /** Additional contextual metadata about the operation */
    metadata: {
        /** The source that triggered this tool operation */
        source: MemorySource;
        /** The type of memory entry */
        type: MemoryType;
        /** Optional tags for categorizing the memory */
        tags?: string[];
        /** The workspace context where the operation occurred */
        workspaceContext?: string;
        /** The file(s) affected by this operation, if any */
        affectedFiles?: string[];
        /** Whether this operation was successful */
        success: boolean;
        /** How important this memory is (0-1) */
        importance?: number;
        /** Additional tool-specific metadata */
        [key: string]: unknown;
    };
}

/**
 * Represents a single tool operation's memory record.
 * This interface extends IToolMemorySchema with additional
 * execution and tracking details specific to a tool operation.
 */
export interface IToolOperationMemory extends IToolMemorySchema {
    /** Duration of the operation in milliseconds */
    duration: number;

    /** The execution context identifier */
    executionId: string;

    /** Current state of the operation */
    state: {
        /** The status of this operation */
        status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
        /** When the operation started */
        startTime: number;
        /** When the operation ended (if completed) */
        endTime?: number;
        /** Progress information (0-100) */
        progress?: number;
    };

    /** Dependencies between operations */
    dependencies?: {
        /** IDs of operations that must complete before this one */
        requiredOperations?: string[];
        /** IDs of operations that depend on this one */
        dependentOperations?: string[];
    };

    /** Resource usage metrics */
    metrics?: {
        /** CPU usage during operation (0-100) */
        cpuUsage?: number;
        /** Memory usage in bytes */
        memoryUsage?: number;
        /** Number of API calls made */
        apiCalls?: number;
        /** Number of file operations */
        fileOperations?: number;
    };
}

/**
 * Standardized logging interface for consistent logging across the application.
 * This interface is injected into all services and tools to provide a consistent
 * way to log messages with different severity levels.
 * 
 * @method debug - Log a debug-level message (for detailed diagnostic information)
 * @method info - Log an informational message (for general operational messages)
 * @method warn - Log a warning message (for potentially harmful situations)
 * @method error - Log an error message (for error conditions that need attention)
 */
export interface ILogger {
    /**
     * Log a debug-level message with optional metadata.
     * @param {string} message - The message to log
     * @param {object} [metadata] - Optional additional data to include with the log
     */
    debug(message: string, metadata?: object): void;

    /**
     * Log an informational message with optional metadata.
     * @param {string} message - The message to log
     * @param {object} [metadata] - Optional additional data to include with the log
     */
    info(message: string, metadata?: object): void;

    /**
     * Log a warning message with optional metadata.
     * @param {string} message - The message to log
     * @param {object} [metadata] - Optional additional data to include with the log
     */
    warn(message: string, metadata?: object): void;

    /**
     * Log an error message or Error object with optional metadata.
     * @param {string | Error} message - The error message or Error object to log
     * @param {object} [metadata] - Optional additional data to include with the log
     */
    error(message: string | Error, metadata?: object): void;
}

/**
 * Provides tools with a high-level understanding of the workspace structure.
 * This interface abstracts workspace operations to provide a consistent way to
 * interact with the file system and workspace contents across different environments.
 */
export interface IWorkspaceKnowledge {
    /**
     * Retrieves a hierarchical representation of the workspace directory structure.
     * @param {Object} [options] - Configuration options for directory tree generation
     * @param {string[]} [options.ignorePatterns] - Array of glob patterns to exclude from the tree
     * @returns {Promise<DirectoryTree | null>} A promise that resolves to the directory tree or null if no workspace is open
     * @throws {Error} If there's an error accessing the file system
     * 
     * @example
     * ```typescript
     * const tree = await workspaceKnowledge.getDirectoryTree({
     *   ignorePatterns: ['**\/node_modules/**', '**\/.git/**']
     * });
     * ```
     */
    getDirectoryTree(options?: { ignorePatterns?: string[] }): Promise<DirectoryTree | null>;

    /**
     * Finds files in the workspace matching the given glob pattern.
     * @param {string} globPattern - The glob pattern to match files against
     * @returns {Promise<Uri[]>} A promise that resolves to an array of file URIs
     * @throws {Error} If the pattern is invalid or if there's an error searching
     * 
     * @example
     * ```typescript
     * const tsFiles = await workspaceKnowledge.findFilesByPattern('**\\*.ts');
     * ```
     */
    findFilesByPattern(globPattern: string): Promise<Uri[]>;

    /**
     * Reads the content of a file in the workspace.
     * @param {string} relativePath - The path to the file relative to the workspace root
     * @returns {Promise<string>} A promise that resolves to the file content as a string
     * @throws {Error} If the file doesn't exist or cannot be read
     * 
     * @example
     * ```typescript
     * const content = await workspaceKnowledge.getFileContent('src/utils/helpers.ts');
     * ```
     */
    getFileContent(relativePath: string): Promise<string>;
}

/**
 * Options for file append operations
 */
export interface FileAppendOptions {
    /**
     * Whether to add a newline before appending content
     * @default true
     */
    newLine?: boolean;

    /**
     * Separator to add between existing content and new content
     * @default '\n' (newline)
     */
    separator?: string;
}

/**
 * Options for file content search operations
 */
export interface FileSearchOptions {
    /**
     * Whether the search should be case-sensitive
     * @default false
     */
    caseSensitive?: boolean;

    /**
     * Whether to use regular expressions for pattern matching
     * @default false
     */
    useRegex?: boolean;

    /**
     * Maximum number of matches to return (0 for unlimited)
     * @default 0
     */
    maxMatches?: number;
}

/**
 * Represents a match found during a file content search
 */
export interface ContentMatch {
    lineNumber: number;
    lineContent: string;
    match: string | string[];
    /** Character positions of the match within the line (start, end) */
    position: [number, number];
}

/**
 * Supported diff formats for file comparison and patching
 */
export enum DiffFormat {
    /** Unified diff format (default) */
    Unified = 'unified',
    /** Context diff format */
    Context = 'context',
    /** Git-style diff */
    Git = 'git',
    /** JSON diff format for structured data */
    Json = 'json',
    /** Line-by-line diff */
    Line = 'line',
    /** Word-level diff */
    Word = 'word',
    /** Character-level diff */
    Char = 'char',
    /** Side-by-side diff */
    SideBySide = 'side-by-side',
    /** Inline diff */
    Inline = 'inline'
}

// VSCode types are already imported at the top of the file

/**
 * Options for diff generation and application
 */
export interface DiffOptions {
    // Diff Formatting Options
    /**
     * The format of the diff to generate/apply
     * @default DiffFormat.Unified
     */
    format?: DiffFormat;

    /**
     * Number of context lines to include around changes
     * @default 3
     */
    contextLines?: number;

    // Diff Generation Options
    /**
     * Whether to ignore whitespace changes
     * @default false
     */
    ignoreWhitespace?: boolean;

    /**
     * Whether to ignore case differences
     * @default false
     */
    ignoreCase?: boolean;

    /**
     * Whether to ignore line endings (CRLF vs LF)
     * @default true
     */
    ignoreLineEndings?: boolean;

    /**
     * Whether to ignore changes in indentation
     * @default false
     */
    ignoreIndentation?: boolean;

    // Advanced Diff Options
    /**
     * Whether to detect and handle moved/renamed content
     * @default true
     */
    detectRenames?: boolean;

    /**
     * Whether to detect and handle copied content
     * @default false
     */
    detectCopies?: boolean;

    /**
     * Whether to detect and handle moved/renamed files
     * @default true
     */
    detectRenamedFiles?: boolean;

    // Binary File Handling
    /**
     * Whether to detect and handle binary files
     * @default true
     */
    detectBinary?: boolean;

    /**
     * Whether to generate/apply diffs for binary files
     * @default false
     */
    binaryDiff?: boolean;

    // Output Formatting
    /**
     * Whether to normalize line endings before diffing
     * @default true
     */
    normalizeLineEndings?: boolean;

    /**
     * Whether to include line numbers in the diff output
     * @default true
     */
    lineNumbers?: boolean;

    /**
     * Whether to include the file header in the diff output
     * @default true
     */
    fileHeader?: boolean;

    /**
     * Whether to include the hunk headers in the diff output
     * @default true
     */
    hunkHeaders?: boolean;

    // VSCode Editor Integration
    /**
     * Whether to show the diff in VSCode's diff editor
     * @default true when running in VSCode
     */
    showInEditor?: boolean;

    /**
     * The title to show in the diff editor
     */
    title?: string;

    /**
     * The view column to open the diff editor in
     * @default ViewColumn.Beside
     */
    viewColumn?: typeof vscode.ViewColumn[keyof typeof vscode.ViewColumn];

    /**
     * Whether to preserve focus on the original editor
     * @default true
     */
    preserveFocus?: boolean;

    /**
     * Selection to highlight in the diff editor
     */
    selection?: vscode.Range;

    /**
     * Reveal type for the diff editor
     * @default vscode.TextEditorRevealType.InCenterIfOutsideViewport
     */
    revealType?: vscode.TextEditorRevealType;

    /**
     * Additional options for the diff editor
     */
    editorOptions?: vscode.TextDocumentShowOptions;

    /**
     * Language ID for syntax highlighting in the diff
     * @default 'plaintext'
     */
    languageId?: string;
}

/**
 * Options for file editing operations
 */
/**
 * Options for appending content to a file
 */
export interface FileAppendOptions {
    /**
     * Separator to use between existing content and new content
     * @default '\n'
     */
    separator?: string;
}

/**
 * Represents a match found in a file
 */
/** Represents a match found in a search operation */
export interface FileContentMatch {
    /** 1-based line number of the match */
    line: number;
    /** 1-based column number of the match */
    column: number;
    /** The actual text that matched */
    match: string;
    /** Any capture groups from regex matches */
    groups: string[];
    /** The file path relative to workspace root */
    filePath: string;
    /** The file's URI */
    uri: Uri;
    /** Preview of the surrounding content */
    preview: string;
    /** Additional search context */
    context?: {
        /** Number of lines before the match */
        linesBefore?: string[];
        /** Number of lines after the match */
        linesAfter?: string[];
        /** The matched line */
        matchedLine: string;
        /** Start position within the matched line */
        matchStart: number;
        /** End position within the matched line */
        matchEnd: number;
    };
}

/**
 * Options for searching within a file
 */
export interface FileSearchOptions {
    /**
     * Whether to use regular expression for searching
     * @default false
     */
    useRegex?: boolean;

    /**
     * Whether the search is case-sensitive
     * @default false
     */
    caseSensitive?: boolean;
}

export interface FileEditOptions extends DiffOptions {
    /**
     * Whether to create the file if it doesn't exist
     * @default false
     */
    createIfNotExists?: boolean;

    /**
     * Whether to preserve the file's line endings
     * @default true
     */
    preserveLineEndings?: boolean;

    /**
     * Encoding to use when reading/writing the file
     * @default 'utf8'
     */
    encoding?: BufferEncoding;

    /**
     * Whether to create a backup of the file before making changes
     * @default false
     */
    backup?: boolean;

    /**
     * Whether to show a preview of the changes before applying them
     * @default true
     */
    preview?: boolean;

    /**
     * Suffix to use for backup files
     * @default '.bak'
     */
    backupSuffix?: string;

    /**
     * Whether to suppress success messages
     * @default false
     */
    silent?: boolean;
}

/**
 * A secure, sandboxed service for all file system I/O operations.
 * This interface provides a controlled way to interact with the file system,
 * ensuring that operations are performed safely within the workspace boundaries.
 */
export interface IFileSystemManager {
    // ========== Basic File Operations ==========

    /**
     * Reads the contents of a file as a UTF-8 encoded string.
     * @param {string} relativePath - The path to the file relative to the workspace root
     * @param {BufferEncoding} [encoding='utf8'] - The encoding to use when reading the file
     * @returns {Promise<string>} A promise that resolves with the file contents
     * @throws {Error} If the file doesn't exist, cannot be read, or is outside the workspace
     * 
     * @example
     * ```typescript
     * const content = await fsManager.readFile('src/config/settings.json');
     * ```
     */
    readFile(relativePath: string, encoding?: BufferEncoding): Promise<string>;

    /**
     * Writes content to a file, creating it if it doesn't exist or overwriting if it does.
     * Parent directories will be created if they don't exist.
     * 
     * @param {string} relativePath - The path to the file relative to the workspace root
     * @param {string | Buffer} content - The content to write to the file
     * @param {FileEditOptions} [options] - Additional options for the write operation
     * @returns {Promise<void>} A promise that resolves when the write operation is complete
     * @throws {Error} If the file cannot be written or is outside the workspace
     * 
     * @example
     * ```typescript
     * await fsManager.writeFile('src/generated/config.js', 'const config = {};');
     * ```
     */
    writeFile(relativePath: string, content: string | Buffer, options?: FileEditOptions): Promise<void>;

    /**
     * Appends content to the end of a file.
     * 
     * @param {string} relativePath - The path to the file relative to the workspace root
     * @param {string | Buffer} content - The content to append to the file
     * @param {FileAppendOptions & FileEditOptions} [options] - Options for the append operation
     * @returns {Promise<void>} A promise that resolves when the append operation is complete
     * @throws {Error} If the file cannot be written to or is outside the workspace
     * 
     * @example
     * ```typescript
     * // Basic append
     * await fsManager.appendToFile('log.txt', 'New log entry');
     * 
     * // Append with custom separator
     * await fsManager.appendToFile('config.json', '\n{"newSetting": true}', { separator: ',\n' });
     * ```
     */
    appendToFile(
        relativePath: string,
        content: string | Buffer,
        options?: FileAppendOptions & FileEditOptions
    ): Promise<void>;

    /**
     * Deletes a file from the workspace.
     * 
     * @param {string} relativePath - The path to the file relative to the workspace root
     * @returns {Promise<void>} A promise that resolves when the file is deleted
     * @throws {Error} If the file doesn't exist, cannot be deleted, or is outside the workspace
     * 
     * @example
     * ```typescript
     * await fsManager.deleteFile('temp/old-config.json');
     * ```
     */
    deleteFile(relativePath: string): Promise<void>;

    /**
     * Creates a directory and any necessary parent directories.
     * 
     * @param {string} relativePath - The path to the directory relative to the workspace root
     * @returns {Promise<void>} A promise that resolves when the directory is created
     * @throws {Error} If the directory cannot be created or is outside the workspace
     * 
     * @example
     * ```typescript
     * await fsManager.createDirectory('src/generated/types');
     * ```
     */
    createDirectory(relativePath: string): Promise<void>;

    // ========== Advanced File Operations ==========

    /**
     * Finds all occurrences of a search pattern in a file.
     * 
     * @param {string} relativePath - The path to the file to search in
     * @param {string | RegExp} searchPattern - The pattern to search for (string or RegExp)
     * @param {FileSearchOptions} [options] - Options for the search operation
     * @returns {Promise<ContentMatch[]>} A promise that resolves with an array of matches
     * @throws {Error} If the file cannot be read or is outside the workspace
     * 
     * @example
     * ```typescript
     * // Simple text search
     * const matches = await fsManager.findInFile('src/utils/helpers.ts', 'function calculate');
     * 
     * // Case-insensitive regex search
     * const regexMatches = await fsManager.findInFile(
     *   'src/config.json', 
     *   /api[_-]?key\s*[:=]\s*['"]([^'"]+)['"]/i,
     *   { useRegex: true }
     * );
     * ```
     */
    findInFile(
        relativePath: string,
        searchPattern: string | RegExp,
        options?: FileSearchOptions
    ): Promise<ContentMatch[]>;

    /**
     * Replaces content in a file based on a search pattern.
     * 
     * @param {string} relativePath - The path to the file to modify
     * @param {string | RegExp} searchPattern - The pattern to search for
     * @param {string | ((match: string, ...groups: string[]) => string)} replacement - The replacement string or function
     * @param {FileSearchOptions & FileEditOptions} [options] - Options for the replace operation
     * @returns {Promise<number>} A promise that resolves with the number of replacements made
     * @throws {Error} If the file cannot be read, written, or is outside the workspace
     * 
     * @example
     * ```typescript
     * // Simple text replacement
     * const count = await fsManager.replaceInFile(
     *   'config.json',
     *   'old-api.example.com',
     *   'new-api.example.com'
     * );
     * 
     * // Using a replacement function
     * const updated = await fsManager.replaceInFile(
     *   'package.json',
     *   /"version":\s*"([\d.]+)"/,
     *   (match, version) => `"version": "${require('semver').inc(version, 'patch')}"`,
     *   { useRegex: true }
     * );
     * ```
     */
    replaceInFile(
        relativePath: string,
        searchPattern: string | RegExp,
        replacement: string | ((match: string, ...groups: string[]) => string),
        options?: FileSearchOptions & FileEditOptions
    ): Promise<number>;

    /**
     * Inserts content at a specific line in a file.
     * 
     * @param {string} relativePath - The path to the file to modify
     * @param {number} lineNumber - The 1-based line number to insert at
     * @param {string} content - The content to insert
     * @param {FileEditOptions} [options] - Options for the insert operation
     * @returns {Promise<void>} A promise that resolves when the insert is complete
     * @throws {Error} If the file cannot be modified or is outside the workspace
     * 
     * @example
     * ```typescript
     * // Insert an import at the top of a file
     * await fsManager.insertAtLine('src/app.ts', 1, 'import * as path from \'path\';\n');
     * 
     * // Insert a route at a specific position
     * await fsManager.insertAtLine('src/routes.ts', 10, '  { path: \'/new-route\', component: NewComponent },\n');
     * ```
     */
    insertAtLine(
        relativePath: string,
        lineNumber: number,
        content: string,
        options?: FileEditOptions
    ): Promise<void>;

    /**
     * Updates or replaces lines in a file that match a predicate function.
     * 
     * @param {string} relativePath - The path to the file to modify
     * @param {(line: string, lineNumber: number) => string | null} predicate - Function that returns:
     *   - A new string to replace the line with
     *   - null to leave the line unchanged
     *   - undefined to remove the line
     * @param {FileEditOptions} [options] - Options for the update operation
     * @returns {Promise<number>} A promise that resolves with the number of lines modified
     * @throws {Error} If the file cannot be modified or is outside the workspace
     * 
     * @example
     * ```typescript
     * // Update version numbers in a package.json
     * const updated = await fsManager.updateFileLines('package.json', (line) => {
     *   if (line.includes('"version":')) {
     *     return line.replace(/"version":\s*"[\d.]+/g, '"version": "1.2.3"');
     *   }
     *   return line; // Leave other lines unchanged
     * });
     * 
     * // Remove all console.log statements
     * const cleaned = await fsManager.updateFileLines('src/utils/logger.ts', (line) => {
     *   return line.trim().startsWith('console.log(') ? undefined : line;
     * });
     * ```
     */
    updateFileLines(
        relativePath: string,
        predicate: (line: string, lineNumber: number) => string | null | undefined,
        options?: FileEditOptions
    ): Promise<number>;

    /**
     * Gets a diff between the current file content and a new version.
     * 
     * @param {string} relativePath - The path to the file
     * @param {string | Buffer} newContent - The new content to compare against
     * @param {DiffOptions} [options] - Options for the diff generation
     * @returns {Promise<string>} A promise that resolves with a diff string in the specified format
     * @throws {Error} If the file cannot be read or is outside the workspace
     * 
     * @example
     * ```typescript
     * // Get unified diff
     * const diff = await fsManager.getFileDiff('src/utils/helpers.ts', newContent);
     * 
     * // Get JSON diff
     * const jsonDiff = await fsManager.getFileDiff('config.json', newConfig, {
     *   format: DiffFormat.Json,
     *   ignoreWhitespace: true
     * });
     * 
     * // Show diff in VSCode editor
     * await fsManager.getFileDiff('file.txt', newContent, {
     *   showInEditor: true,
     *   title: 'Preview Changes',
     *   viewColumn: ViewColumn.Beside
     * });
     * ```
     */
    getFileDiff(
        relativePath: string,
        newContent: string | Buffer,
        options?: DiffOptions
    ): Promise<string>;

    /**
     * Gets a diff between two versions of a file.
     * 
     * @param {string | Buffer} oldContent - The old content to compare from
     * @param {string | Buffer} newContent - The new content to compare to
     * @param {DiffOptions} [options] - Options for the diff generation
     * @returns {Promise<string>} A promise that resolves with a diff string in the specified format
     * 
     * @example
     * ```typescript
     * // Get diff as string
     * const diff = await fsManager.diffContent(oldContent, newContent, {
     *   format: DiffFormat.SideBySide,
     *   contextLines: 5
     * });
     * 
     * // Show diff in VSCode editor
     * await fsManager.diffContent(oldContent, newContent, {
     *   showInEditor: true,
     *   title: 'Compare Versions',
     *   viewColumn: ViewColumn.Beside
     * });
     * ```
     */
    diffContent(
        oldContent: string | Buffer,
        newContent: string | Buffer,
        options?: DiffOptions
    ): Promise<string>;

    /**
     * Applies a diff to a file.
     * 
     * @param {string} relativePath - The path to the file to patch
     * @param {string} diff - The diff to apply (format is auto-detected)
     * @param {FileEditOptions} [options] - Options for the patch operation
     * @returns {Promise<{success: boolean, conflicts: string[]}>} A promise that resolves with the result of the patch operation
     * @throws {Error} If the patch cannot be applied or the file is outside the workspace
     * 
     * @example
     * ```typescript
     * // Apply diff directly
     * const result = await fsManager.applyDiff('src/utils/helpers.ts', diffString, {
     *   backup: true,
     *   ignoreWhitespace: true
     * });
     * 
     * // Show preview before applying
     * const preview = await fsManager.getFileDiff('file.txt', newContent, {
     *   showInEditor: true,
     *   title: 'Review Changes Before Applying'
     * });
     * 
     * // After user confirms, apply the changes
     * const applyResult = await fsManager.applyDiff('file.txt', diffString, {
     *   backup: true
     * });
     * 
     * if (!applyResult.success) {
     *   console.warn('Conflicts found:', applyResult.conflicts);
     * }
     * ```
     */
    applyDiff(
        relativePath: string,
        diff: string,
        options?: FileEditOptions
    ): Promise<{ success: boolean; conflicts: string[] }>;

    /**
     * Creates a patch file from changes between two versions of a file.
     * 
     * @param {string} relativePath - The path to the file
     * @param {string | Buffer} oldContent - The old content
     * @param {string | Buffer} newContent - The new content
     * @param {string} outputPath - The path to save the patch file
     * @param {DiffOptions} [options] - Options for the patch generation
     * @returns {Promise<void>} A promise that resolves when the patch file is created
     * 
     * @example
     * ```typescript
     * await fsManager.createPatchFile(
     *   'src/utils/helpers.ts',
     *   oldContent,
     *   newContent,
     *   'patches/helpers-fix.patch',
     *   { format: DiffFormat.Git }
     * );
     * ```
     */
    createPatchFile(
        relativePath: string,
        oldContent: string | Buffer,
        newContent: string | Buffer,
        outputPath: string,
        options?: DiffOptions
    ): Promise<void>;

    /**
     * Applies a patch file to a file.
     * 
     * @param {string} relativePath - The path to the file to patch
     * @param {string} patchPath - The path to the patch file
     * @param {FileEditOptions} [options] - Options for the patch application
     * @returns {Promise<{success: boolean, conflicts: string[]}>} A promise that resolves with the result of the patch operation
     * 
     * @example
     * ```typescript
     * const result = await fsManager.applyPatchFile(
     *   'src/utils/helpers.ts',
     *   'patches/helpers-fix.patch',
     *   { backup: true }
     * );
     * ```
     */
    applyPatchFile(
        relativePath: string,
        patchPath: string,
        options?: FileEditOptions
    ): Promise<{ success: boolean; conflicts: string[] }>;
}

/**
 * @deprecated Use InteractiveSession from './pseudoTerminal' instead
 */
export interface IInteractiveSession extends vscode.Disposable {
    /**
     * A unique identifier for this terminal session.
     */
    readonly id: string;

    /**
     * An event that fires when data is received from the terminal.
     */
    readonly onData: Event<string>;

    /**
     * An event that fires when the terminal process exits.
     * The event parameter is the exit code of the process.
     */
    readonly onExit: Event<number>;

    /**
     * Writes data to the terminal's input stream.
     * 
     * @param {string} data - The data to write to the terminal
     * @returns {Promise<void>} A promise that resolves when the data has been written
     * @throws {Error} If the terminal is not available or the write operation fails
     * 
     * @example
     * ```typescript
     * await terminal.write('ls -la\n');
     * ```
     */
    write(data: string): Promise<void>;

    /**
     * Resizes the terminal to the specified dimensions.
     * 
     * @param {number} cols - The number of columns (width) in characters
     * @param {number} rows - The number of rows (height) in characters
     * @returns {Promise<void>} A promise that resolves when the resize is complete
     * @throws {Error} If the terminal doesn't support resizing or the operation fails
     * 
     * @example
     * ```typescript
     * await terminal.resize(120, 40);
     * ```
     */
    resize(cols: number, rows: number): Promise<void>;
}

/**
 * The single, unified service for all terminal operations within the extension.
 * This service provides a high-level API for executing commands, streaming output,
 * and managing interactive terminal sessions in a cross-platform manner.
 * 
 * @deprecated Use InteractiveSession and PseudoTerminal from './pseudoTerminal' directly for new code
 */
export interface ITerminalService {
    /**
     * Executes a command in a terminal and returns the result.
     * 
     * @param {string} command - The command to execute
     * @param {string[]} args - Array of command-line arguments
     * @param {Object} options - Execution options
     * @param {string} [options.cwd] - The current working directory for the command
     * @param {CancellationToken} options.cancellationToken - Token to cancel the operation
     * @returns {Promise<CommandResult>} A promise that resolves with the command result
     * @throws {TerminalError} If the command execution fails
     * 
     * @example
     * ```typescript
     * const result = await terminalService.execute('git', ['status'], {
     *   cwd: '/path/to/repo',
     *   cancellationToken: cancellationToken
     * });
     * ```
     */
    execute(
        command: string,
        args: string[],
        options: {
            cwd?: string;
            cancellationToken: CancellationToken
        }
    ): Promise<CommandResult>;

    /**
     * Streams the output of a command in real-time.
     * 
     * @param {string} command - The command to execute
     * @param {string[]} args - Array of command-line arguments
     * @param {Object} options - Stream options
     * @param {string} [options.cwd] - The current working directory for the command
     * @param {(data: string) => void} options.onData - Callback for receiving output data
     * @param {CancellationToken} options.cancellationToken - Token to cancel the operation
     * @returns {Promise<{ exitCode: number }>} A promise that resolves with the exit code when complete
     * 
     * @example
     * ```typescript
     * await terminalService.stream('npm', ['install'], {
     *   cwd: projectPath,
     *   onData: data => console.log(data),
     *   cancellationToken
     * });
     * ```
     */
    stream(
        command: string,
        args: string[],
        options: {
            cwd?: string;
            onData: (data: string) => void;
            cancellationToken: CancellationToken
        }
    ): Promise<{ exitCode: number }>;

    /**
     * Creates a new interactive terminal session.
     * 
     * @param {Object} [options] - Session options
     * @param {string} [options.cwd] - The initial working directory for the terminal
     * @returns {Promise<IInteractiveSession>} A promise that resolves with the new terminal session
     * 
     * @example
     * ```typescript
     * const session = await terminalService.createSession({ cwd: '/path/to/start' });
     * session.onData(data => console.log('Terminal output:', data));
     * await session.write('echo Hello World\n');
     * ```
     */
    createSession(options?: { cwd?: string }): Promise<IInteractiveSession>;
}

/**
 * A secure service for interacting with the system clipboard in a cross-platform manner.
 * This interface provides methods for reading and writing both text and image data
 * to/from the system clipboard, with proper error handling and type safety.
 */
export interface IClipboardService {
    /**
     * Reads plain text from the system clipboard.
     * 
     * @returns {Promise<string>} A promise that resolves with the text content
     * @throws {Error} If reading from the clipboard fails or if the clipboard doesn't contain text
     * 
     * @example
     * ```typescript
     * try {
     *   const text = await clipboardService.readText();
     *   console.log('Clipboard text:', text);
     * } catch (error) {
     *   console.error('Failed to read from clipboard:', error);
     * }
     * ```
     */
    readText(): Promise<string>;

    /**
     * Reads an image from the clipboard if available.
     * 
     * @returns {Promise<MultimodalData | null>} A promise that resolves with the image data
     *         or null if the clipboard doesn't contain an image
     * @throws {Error} If reading from the clipboard fails or if the image format is unsupported
     * 
     * @example
     * ```typescript
     * try {
     *   const imageData = await clipboardService.readImage();
     *   if (imageData) {
     *     console.log(`Image data (${imageData.mimeType}):`, imageData.base64Data.substring(0, 50) + '...');
     *   } else {
     *     console.log('No image in clipboard');
     *   }
     * } catch (error) {
     *   console.error('Failed to read image from clipboard:', error);
     * }
     * ```
     */
    readImage(): Promise<MultimodalData | null>;

    /**
     * Writes text to the system clipboard.
     * 
     * @param {string} text - The text to write to the clipboard
     * @returns {Promise<void>} A promise that resolves when the operation is complete
     * @throws {Error} If writing to the clipboard fails
     * 
     * @example
     * ```typescript
     * try {
     *   await clipboardService.writeText('Text to copy');
     *   console.log('Text copied to clipboard');
     * } catch (error) {
     *   console.error('Failed to write to clipboard:', error);
     * }
     * ```
     */
    writeText(text: string): Promise<void>;
}

/**
 * The core interface for interacting with Large Language Models (LLMs).
 * This defines a standard contract for generating text responses from language models,
 * supporting both streaming and non-streaming use cases.
 */
export interface ILanguageModel {
    /**
     * Generates a response from the language model based on the provided prompt.
     * 
     * @param {LanguageModelPrompt} prompt - The input prompt for the language model
     * @param {Object} options - Generation options
     * @param {CancellationToken} options.cancellationToken - Token to cancel the generation
     * @param {boolean} options.jsonOutput - Whether to force JSON output format
     * @returns {Promise<string>} A promise that resolves with the generated text
     * 
     * @example
     * ```typescript
     * const prompt = [
     *   { role: 'system', content: 'You are a helpful assistant.' },
     *   { role: 'user', content: 'Tell me about TypeScript' }
     * ];
     * 
     * const response = await languageModel.generateResponse(prompt, {
     *   cancellationToken,
     *   jsonOutput: false
     * });
     * ```
     * 
     * @throws {Error} If the generation fails or is cancelled
     */
    generateResponse(
        prompt: LanguageModelPrompt,
        options: {
            cancellationToken: CancellationToken;
            jsonOutput: boolean
        }
    ): Promise<string>;
}

/**
 * A service that manages and provides access to the currently active language model.
 * This acts as a facade that can be used to switch between different LLM providers
 * or configurations at runtime, providing a single point of access to the active model.
 */
export interface ILanguageModelProvider {
    /**
     * Initializes the language model provider with required services.
     * 
     * @param {IServiceContainer} services - The service container providing access to other services
     * @returns {void}
     * 
     * @example
     * ```typescript
     * // During extension activation
     * languageModelProvider.initialize(serviceContainer);
     * ```
     */
    initialize(services: IServiceContainer): void;

    /**
     * Retrieves the currently active language model instance.
     * 
     * @returns {ILanguageModel} The active language model instance
     * @throws {Error} If no language model is available or initialized
     * 
     * @example
     * ```typescript
     * const model = languageModelProvider.getActiveModel();
     * const response = await model.generateResponse(prompt, { options });
     * '''
     */
    getActiveModel(): ILanguageModel;
}

/**
 * Manages checking and requesting user consent for sensitive operations.
 * This service handles permission requests in a user-friendly way, ensuring
 * that users understand and approve potentially sensitive actions.
 */
export interface IPermissionManager {
    /**
     * Initializes the permission manager with the extension context.
     * 
     * @param {ExtensionContext} context - The extension context
     * @returns {void}
     * 
     * @example
     * ```typescript
     * // During extension activation
     * permissionManager.initialize(context);
     * ```
     */
    initialize(context: ExtensionContext): void;

    /**
     * Requests one or more permissions from the user with a clear rationale.
     * 
     * @param {readonly ToolPermission[]} permissions - Array of permissions to request
     * @param {string} rationale - Explanation of why these permissions are needed
     * @returns {Promise<boolean>} A promise that resolves to true if all permissions are granted
     * 
     * @example
     * ```typescript
     * const granted = await permissionManager.requestPermissions(
     *   [ToolPermission.FileSystemRead, ToolPermission.ClipboardWrite],
     *   'We need these permissions to save the generated code to your project and copy it to the clipboard.'
     * );
     * 
     * if (!granted) {
     *   throw new Error('Operation cancelled: Required permissions not granted');
     * }
     * ```
     */
    requestPermissions(permissions: readonly ToolPermission[], rationale: string): Promise<boolean>;
}

/**
 * A centralized registry for discovering, managing, and accessing tools within the extension.
 * This registry maintains a collection of available tools and provides methods to look them up
 * by name or retrieve all registered tools.
 */
export interface IToolRegistry {
    /**
     * Initializes the tool registry with the provided service container.
     * This method should be called during extension activation to set up the registry.
     * 
     * @param {IServiceContainer} services - The service container providing access to other services
     * @returns {Promise<void>} A promise that resolves when initialization is complete
     * 
     * @example
     * ```typescript
     * // During extension activation
     * await toolRegistry.initialize(serviceContainer);
     * ```
     */
    initialize(services: IServiceContainer): Promise<void>;

    /**
     * Retrieves a tool by its name.
     * 
     * @param {string} toolName - The name of the tool to retrieve
     * @returns {ITool | undefined} The tool instance if found, otherwise undefined
     * 
     * @example
     * ```typescript
     * const tool = toolRegistry.getTool('fileSystem');
     * if (tool) {
     *   // Use the tool
     * }
     * ```
     */
    getTool(toolName: string): ITool | undefined;

    /**
     * Retrieves all registered tools.
     * 
     * @returns {readonly ITool[]} An array of all registered tools
     * 
     * @example
     * ```typescript
     * const allTools = toolRegistry.getAllTools();
     * for (const tool of allTools) {
     *   console.log(`Found tool: ${tool.name}`);
     * }
     * ```
     */
    getAllTools(): readonly ITool[];
}

/**
 * Provides user interface interaction capabilities for tools within the VS Code environment.
 * This interface abstracts common UI operations, allowing tools to present information,
 * request input, and show progress without directly depending on VS Code's UI APIs.
 */
export interface IToolUIProvider {
    /**
     * Shows a progress indicator in the VS Code UI while executing the provided work.
     * 
     * @template T - The type of the result returned by the work function
     * @param {string} title - The title to display in the progress notification
     * @param {(progress: Progress<{ message?: string }>, token: CancellationToken) => Promise<T>} work - The async function to execute with progress tracking
     * @returns {Promise<T>} A promise that resolves with the result of the work function
     * 
     * @example
     * ```typescript
     * // Show progress while performing a long-running operation
     * const result = await uiProvider.showProgress('Processing files...', async (progress, token) => {
     *   progress.report({ message: 'Starting processing...' });
     *   // Perform work...
     *   progress.report({ message: 'Almost done...' });
     *   return await someAsyncOperation();
     * });
     * ```
     */
    showProgress<T>(
        title: string,
        work: (progress: Progress<{ message?: string }>, token: CancellationToken) => Promise<T>
    ): Promise<T>;

    /**
     * Shows an informational message to the user.
     * 
     * @param {string} message - The message to display
     * 
     * @example
     * ```typescript
     * uiProvider.showInformationMessage('Operation completed successfully');
     * ```
     */
    showInformationMessage(message: string): void;

    /**
     * Shows a warning message to the user.
     * 
     * @param {string} message - The warning message to display
     * 
     * @example
     * ```typescript
     * uiProvider.showWarningMessage('This operation may take longer than expected');
     * ```
     */
    showWarningMessage(message: string): void;

    /**
     * Shows an error message to the user.
     * 
     * @param {string | Error} message - The error message or Error object to display
     * 
     * @example
     * ```typescript
     * try {
     *   // Some operation that might fail
     * } catch (error) {
     *   uiProvider.showErrorMessage(error);
     * }
     * ```
     */
    showErrorMessage(message: string | Error): void;
}

/** Metadata for logging operations */
export interface LogMetadata {
    [key: string]: unknown;
}

/**
 * A centralized container for all singleton services used throughout the extension.
 * This container facilitates clean dependency injection, making services easily accessible
 * while maintaining loose coupling between components.
 * 
 * @example
 * ```typescript
 * // Get a service from the container
 * const fs = services.get<IFileSystemManager>('fileSystem');
 * 
 * // Check if a service is available
 * if (services.has('languageModel')) {
 *   const model = services.get<ILanguageModel>('languageModel');
 *   // Use the model...
 * }
 * ```
 */
export interface IServiceContainer {
    readonly logger: ILogger;
    readonly ui: IToolUIProvider;
    readonly permissionManager: IPermissionManager;
    readonly toolRegistry: IToolRegistry;
    readonly memoryManager: IMemoryOperations;
    readonly workspace: IWorkspaceKnowledge;
    readonly fileSystem: IFileSystemManager;
    readonly terminalService: ITerminalService;
    readonly clipboard: IClipboardService;
    readonly llmProvider: ILanguageModelProvider;
}

/**
 * Permission constants defining the specific capabilities that tools may request access to.
 *
 * These permissions control access to sensitive system resources and operations.
 * Tools must declare their required permissions, and users are prompted to grant them
 * before the tool can perform restricted operations.
 *
 * @constant
 * @type {readonly string[]}
 *
 * @example
 * ```typescript
 * // A tool requesting file system write access
 * public getPermissions(actionName: string): readonly ToolPermission[] {
 *   if (actionName === 'writeFile') {
 *     return ['workspace:write'];
 *   }
 *   return [];
 * }
 * ```
 */
export const TOOL_PERMISSIONS = [
    'workspace:read',           // Read access to workspace files and directories
    'workspace:write',          // Write access to workspace files and directories
    'shell:execute',            // Execute shell commands (governs execute() and stream())
    'terminal:interactive_session', // Create interactive terminal sessions (governs createSession())
    'network:unrestricted',     // Make unrestricted network requests
    'clipboard:read',           // Read from system clipboard
] as const;

/**
 * A specific tool permission type derived from the TOOL_PERMISSIONS constant.
 * This ensures type safety when working with permissions throughout the framework.
 *
 * @typedef {typeof TOOL_PERMISSIONS[number]} ToolPermission
 */
export type ToolPermission = typeof TOOL_PERMISSIONS[number];

/**
 * The detailed result of an individual tool action execution.
 *
 * This interface captures all aspects of a tool execution, including success/failure status,
 * output data, error information, and metadata about the execution for auditing and debugging.
 *
 * @interface ToolResult
 *
 * @property {string} executionId - Unique identifier for this specific tool execution
 * @property {string} toolName - Name of the tool that was executed
 * @property {string} actionName - Name of the action that was performed
 * @property {'success' | 'failure' | 'cancelled'} status - Final status of the execution
 * @property {unknown} [output] - Output data returned by the tool action (only present on success)
 * @property {object} [error] - Error information (only present on failure/cancellation)
 * @property {string} error.message - Human-readable error message
 * @property {string} [error.code] - Machine-readable error code for programmatic handling
 * @property {string} [error.stack] - Error stack trace for debugging
 * @property {Record<string, unknown>} [error.details] - Additional error context and metadata
 * @property {string[]} [memoriesCreated] - IDs of memories created during execution for learning
 *
 * @example
 * ```typescript
 * // Successful execution result
 * {
 *   executionId: "exec-123",
 *   toolName: "fileSystem",
 *   actionName: "readFile",
 *   status: "success",
 *   output: { content: "file contents..." },
 *   memoriesCreated: ["mem-456"]
 * }
 *
 * // Failed execution result
 * {
 *   executionId: "exec-789",
 *   toolName: "terminal",
 *   actionName: "execute",
 *   status: "failure",
 *   error: {
 *     message: "Command failed with exit code 1",
 *     code: "E_COMMAND_FAILED",
 *     details: { exitCode: 1, command: "invalid-command" }
 *   }
 * }
 * ```
 */
/**
 * Updates a tool operation memory with execution results
 * @param memory The memory record to update
 * @param result The execution result
 * @param metrics Optional performance metrics
 * @returns The updated memory record
 */
/**
 * Enhanced terminal session that integrates with tool memory management
 */
export class ToolTerminalSession extends InteractiveSession {
    private readonly _terminalState: TerminalState;
    private readonly _terminalStats: TerminalStats;
    private _commandHistory: string[];
    private _dimensions: { columns: number; rows: number };
    private _isConnected = false;
    private _isReady = false;
    private _isBusy = false;
    private _startTime: number;
    private _cwd: string;

    getTerminalState(): TerminalState {
        return this._terminalState;
    }

    constructor(config: TerminalConfig) {
        super(config);
        this._cwd = config.cwd || process.cwd();
        this._commandHistory = [];
        this._dimensions = { columns: 120, rows: 30 };
        this._startTime = Date.now();
        
        this._terminalState = {
            isConnected: false,
            isReady: false,
            currentDirectory: this._cwd,
            shellType: config.shell || 'powershell',
            dimensions: this._dimensions,
            lastCommand: '',
            commandHistory: [],
            isBusy: false
        };

        this._terminalStats = {
            totalCommands: 0,
            totalOutputBytes: 0,
            averageResponseTime: 0,
            uptimeSeconds: 0,
            errorCount: 0,
            resizeCount: 0
        };
    }

    protected getState(): TerminalState {
        this._terminalState.isConnected = this._isConnected;
        this._terminalState.isReady = this._isReady;
        this._terminalState.currentDirectory = this._cwd;
        this._terminalState.lastCommand = this._commandHistory[this._commandHistory.length - 1] || '';
        this._terminalState.commandHistory = [...this._commandHistory];
        this._terminalState.isBusy = this._isBusy;
        return { ...this._terminalState };
    }

    protected getStats(): TerminalStats {
        this._terminalStats.uptimeSeconds = Math.floor((Date.now() - this._startTime) / 1000);
        return { ...this._terminalStats };
    }

    createTerminalProcess(): ITerminalProcess {
        const process: ITerminalProcess = {
            id: this.id,
            config: this.config,
            state: this.getState(),
            stats: this.getStats(),
            events: this.events,
            initialize: async () => {
                this._isConnected = true;
                this._isReady = true;
            },
            enableLogging: () => Promise.resolve(),
            disableLogging: () => Promise.resolve(),
            write: (data: string) => this.write(data),
            writeLn: (data: string) => this.writeLn(data),
            executeCommand: (cmd: string, opts?: { 
                timeout?: number;
                expectPrompt?: boolean;
                background?: boolean;
                workingDirectory?: string;
            }) => this.executeCommand(cmd, opts),
            executeCommandAdvanced: async (cmd: string, opts?: {
                timeout?: number;
                expectPrompt?: boolean;
                background?: boolean;
                workingDirectory?: string;
            }) => {
                const startTime = Date.now();
                let output = '';
                let success = true;
                let error: string | undefined;
                
                try {
                    output = await this.executeCommand(cmd, opts);
                } catch (e) {
                    success = false;
                    error = e instanceof Error ? e.message : String(e);
                }
                
                return {
                    command: cmd,
                    output,
                    exitCode: success ? 0 : 1,
                    duration: Date.now() - startTime,
                    success,
                    error,
                    background: opts?.background || false,
                    completedAt: new Date()
                };
            },
            resize: (cols: number, rows: number) => {
                this._dimensions = { columns: cols, rows };
                this._terminalStats.resizeCount++;
                return Promise.resolve();
            },
            clear: () => this.clear(),
            reset: () => this.reset(),
            sendSignal: (signal: string) => this.sendSignal(signal),
            getWorkingDirectory: () => Promise.resolve(this._cwd),
            setWorkingDirectory: (path: string) => {
                this._cwd = path;
                return Promise.resolve();
            },
            getCommandHistory: () => [...this._commandHistory],
            clearCommandHistory: () => {
                this._commandHistory = [];
            },
            dispose: () => this.dispose()
        };
        return process;
    }

    createActionRegistry(): TerminalActions {
        return new TerminalActions(this);
    }

    override async executeCommand(
        command: string,
        options?: { 
            timeout?: number;
            expectPrompt?: boolean;
            background?: boolean;
            workingDirectory?: string;
        }
    ): Promise<string> {
        this._isBusy = true;
        this._commandHistory.push(command);
        const startTime = Date.now();

        try {
            if (options?.workingDirectory) {
                this._cwd = options.workingDirectory;
            }

            const output = await super.executeCommand(command, options);
            const duration = Date.now() - startTime;
            
            this._terminalStats.totalCommands++;
            this._terminalStats.totalOutputBytes += output.length;
            this._terminalStats.averageResponseTime = this._terminalStats.averageResponseTime === 0 
                ? duration 
                : (this._terminalStats.averageResponseTime + duration) / 2;
            this._isBusy = false;

            return output;
        } catch (error) {
            const duration = Date.now() - startTime;
            this._terminalStats.errorCount++;
            this._terminalStats.totalCommands++;
            this._terminalStats.averageResponseTime = this._terminalStats.averageResponseTime === 0
                ? duration
                : (this._terminalStats.averageResponseTime + duration) / 2;
            this._isBusy = false;

            throw error;
        }
    }
}

/**
 * Base interface for tools that need terminal interaction capabilities
 */
export interface ITerminalTool {
    /** The terminal session */
    readonly session: ToolTerminalSession;
    /** Terminal state */
    readonly terminalState: TerminalState;
    /** Terminal process interface */
    readonly process: ITerminalProcess;
    /** Terminal configuration */
    readonly config: TerminalConfig;
    /** Available terminal actions */
    readonly actions: TerminalActions;
}

/**
 * Factory for creating tool terminal sessions
 */
export class ToolTerminalFactory {
    private static _instance: ToolTerminalFactory;
    private _config: TerminalConfig;

    private constructor() {
        const platform = os.platform();
        this._config = {
            shell: platform === 'win32' ? 'powershell.exe' : '/bin/bash',
            cwd: process.cwd(),
            env: process.env
        };
    }

    public static get instance(): ToolTerminalFactory {
        if (!ToolTerminalFactory._instance) {
            ToolTerminalFactory._instance = new ToolTerminalFactory();
        }
        return ToolTerminalFactory._instance;
    }

    /**
     * Creates a new terminal session for a tool
     */
    public createTerminalSession(toolId: string): ToolTerminalSession {
        const config: TerminalConfig = {
            ...this._config,
            name: `Tool-${toolId}`
        };
        return new ToolTerminalSession(config);
    }
}

/**
 * Mixin class to add terminal capabilities to a tool
 * @template TBase - Type of the base class
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function withTerminalCapabilities<TBase extends new (...args: any[]) => { id: string }>(Base: TBase) {
    class TerminalCapabilityClass extends Base implements ITerminalTool {
        private _session: ToolTerminalSession;
        private _process: ITerminalProcess;
        private _actions: TerminalActions;

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        constructor(...args: any[]) {
            super(...args);
            this._session = ToolTerminalFactory.instance.createTerminalSession(this.id);
            this._process = this._session.createTerminalProcess();
            this._actions = this._session.createActionRegistry();
        }

        get session(): ToolTerminalSession {
            return this._session;
        }

        get terminalState(): TerminalState {
            return this._session.getTerminalState();
        }

        get process(): ITerminalProcess {
            return this._process;
        }

        get config(): TerminalConfig {
            return this._session.config;
        }

        get actions(): TerminalActions {
            return this._actions;
        }

        protected async dispose(): Promise<void> {
            await this._session.dispose();
        }
    }

    return TerminalCapabilityClass;
}

export function updateToolOperationMemory(
    memory: IToolOperationMemory,
    result: {
        success: boolean;
        output?: unknown;
        error?: { message: string; code?: string; stack?: string; };
    },
    metrics?: {
        cpuUsage?: number;
        memoryUsage?: number;
        apiCalls?: number;
        fileOperations?: number;
    }
): IToolOperationMemory {
    const endTime = Date.now();
    
    return {
        ...memory,
        duration: endTime - memory.state.startTime,
        output: result.output,
        error: result.error,
        state: {
            status: result.success ? 'completed' : 'failed',
            startTime: memory.state.startTime,
            endTime,
            progress: 100
        },
        metadata: {
            ...memory.metadata,
            success: result.success
        },
        metrics: metrics ? {
            ...memory.metrics,
            ...metrics
        } : memory.metrics
    };
}

/**
 * Creates a new tool operation memory record
 * @param toolName Name of the tool
 * @param operation Name of the operation/action
 * @param input Input parameters used
 * @param context Optional workspace context
 * @returns A new tool operation memory record
 */
export function createToolOperationMemory(
    toolName: string,
    operation: string,
    input: Record<string, unknown>,
    context?: {
        workspaceContext?: string;
        affectedFiles?: string[];
        importance?: number;
        tags?: string[];
    }
): IToolOperationMemory {
    return {
        id: randomUUID(),
        toolName,
        operation,
        timestamp: Date.now(),
        input,
        duration: 0,
        executionId: randomUUID(),
        state: {
            status: 'pending',
            startTime: Date.now()
        },
        metadata: {
            source: 'tool' as MemorySource,
            type: 'procedural' as MemoryType,
            success: false,
            workspaceContext: context?.workspaceContext,
            affectedFiles: context?.affectedFiles,
            importance: context?.importance,
            tags: context?.tags
        }
    };
}

export interface ToolResult {
    readonly executionId: string;
    readonly toolName: string;
    readonly actionName: string;
    readonly status: 'success' | 'failure' | 'cancelled';
    readonly output?: unknown;
    readonly error?: { readonly message: string; readonly code?: string; readonly stack?: string; readonly details?: Record<string, unknown> };
    readonly memoriesCreated?: string[];
    /** Memory record for this tool operation */
    readonly memory?: IToolOperationMemory;
    /** Whether this operation's memory should be preserved long-term */
    readonly preserveMemory?: boolean;
    /** Importance score for memory retention (0-1) */
    readonly memoryImportance?: number;
}

/**
 * The master interface for defining AI Agent Tools.
 *
 * This interface defines the contract that all tools must implement to be usable
 * within the framework. It provides the essential methods for tool discovery,
 * execution, permission management, and documentation.
 *
 * @interface ITool
 *
 * @property {string} name - Unique identifier for the tool
 * @property {string} description - Human-readable description of the tool's purpose
 * @property {string} version - Semantic version string (e.g., "1.0.0")
 * @property {string} category - Category for tool organization and discovery
 * @property {Readonly<Record<string, ToolActionDefinition>>} actions - Registry of available actions
 * @method invoke - Execute a specific action with input parameters and options
 * @method getPermissions - Return required permissions for a specific action
 * @method getDocumentation - Return comprehensive documentation in markdown format
 *
 * @example
 * ```typescript
 * class MyCustomTool implements ITool {
 *   public readonly name = 'myTool';
 *   public readonly description = 'A custom tool for specific operations';
 *   public readonly version = '1.0.0';
 *   public readonly category = 'Custom';
 *
 *   public readonly actions = {
 *     performAction: {
 *       description: 'Perform a specific action',
 *       inputSchema: z.object({ input: z.string() }),
 *       outputSchema: z.object({ result: z.string() })
 *     }
 *   } as const;
 *
 *   public async invoke(actionName: string, input: unknown, options: ToolInvokeOptions): Promise<ToolResult> {
 *     // Implementation here
 *   }
 *
 *   public getPermissions(actionName: string): readonly ToolPermission[] {
 *     return ['workspace:read'];
 *   }
 *
 *   public getDocumentation(): string {
 *     return `# My Custom Tool\n\nProvides custom functionality...`;
 *   }
 * }
 * ```
 */
export interface ITool {
    readonly name: string;
    readonly description: string;
    readonly version: string;
    readonly category: string;
    readonly actions: Readonly<Record<string, ToolActionDefinition>>;
    invoke(actionName: string, input: unknown, options: ToolInvokeOptions): Promise<ToolResult>;
    getPermissions(actionName: string): readonly ToolPermission[];
    getDocumentation(): string;
}

/**
 * Defines a single, named action within a tool.
 *
 * Each tool action has a clear definition including its purpose, input/output schemas,
 * and validation rules. This enables the framework to validate inputs, provide
 * documentation, and ensure type safety.
 *
 * @interface ToolActionDefinition
 *
 * @property {string} description - Human-readable description of what this action does
 * @property {z.ZodType<unknown>} inputSchema - Zod schema for validating input parameters
 * @property {z.ZodType<unknown>} outputSchema - Zod schema for validating output data
 *
 * @example
 * ```typescript
 * const readFileAction: ToolActionDefinition = {
 *   description: 'Read content from a file at the specified path',
 *   inputSchema: z.object({
 *     path: z.string().min(1, 'Path cannot be empty'),
 *     encoding: z.enum(['utf-8', 'ascii']).optional().default('utf-8')
 *   }),
 *   outputSchema: z.object({
 *     content: z.string(),
 *     size: z.number(),
 *     lastModified: z.date()
 *   })
 * };
 * ```
 */
export interface ToolActionDefinition {
    readonly description: string;
    readonly inputSchema: z.ZodType<unknown>;
    readonly outputSchema: z.ZodType<unknown>;
}

/**
 * The set of options passed to a tool's `invoke` method.
 *
 * These options provide the execution context, services, and control parameters
 * that tools need to function properly within the framework.
 *
 * @interface ToolInvokeOptions
 *
 * @property {WorkflowContext} workflowContext - The current workflow execution context
 * @property {IServiceContainer} services - Container providing access to all framework services
 * @property {CancellationToken} cancellationToken - Token for cancelling the operation
 *
 * @example
 * ```typescript
 * const options: ToolInvokeOptions = {
 *   workflowContext: currentWorkflowContext,
 *   services: serviceContainer,
 *   cancellationToken: cancellationTokenSource.token
 * };
 *
 * const result = await tool.invoke('readFile', input, options);
 * ```
 */
export interface ToolInvokeOptions {
    readonly workflowContext: WorkflowContext;
    readonly services: IServiceContainer;
    readonly cancellationToken: CancellationToken;
}

/**
 * Custom Error classes for the tool framework
 */

/**
 * Error thrown when an operation is cancelled by the user or due to a cancellation token.
 * This error indicates that the operation was intentionally stopped before completion.
 *
 * @class OperationCancelledError
 * @extends Error
 */
class OperationCancelledError extends Error {
    /**
     * Creates a new OperationCancelledError instance.
     * @param {string} [message='Operation cancelled.'] - The error message to display
     */
    constructor(m = 'Operation cancelled.') {
        super(m);
        this.name = 'OperationCancelledError';
    }
}

/**
 * Error thrown when a user denies the required permissions for a tool operation.
 * This prevents unauthorized access to sensitive resources or operations.
 *
 * @class PermissionDeniedError
 * @extends Error
 */
class PermissionDeniedError extends Error {
    /**
     * Creates a new PermissionDeniedError instance.
     * @param {string} message - The error message explaining which permissions were denied
     */
    constructor(m: string) {
        super(m);
        this.name = 'PermissionDeniedError';
    }
}

/**
 * Error thrown when a tool action is not found or improperly defined in the tool's actions registry.
 * This indicates a configuration or implementation error in the tool definition.
 *
 * @class ToolDefinitionError
 * @extends Error
 */
class ToolDefinitionError extends Error {
    /**
     * Creates a new ToolDefinitionError instance.
     * @param {string} message - The error message explaining the tool definition issue
     */
    constructor(m: string) {
        super(m);
        this.name = 'ToolDefinitionError';
    }
}

/**
 * Error thrown when terminal operations fail due to system-level issues or configuration problems.
 * This includes failures in command execution, session creation, or terminal process management.
 *
 * @class TerminalError
 * @extends Error
 * @deprecated Use the error classes from './pseudoTerminal' instead
 */
class TerminalError extends Error {
    /**
     * Creates a new TerminalError instance.
     * @param {string} message - The error message explaining the terminal operation failure
     */
    constructor(m: string) {
        super(m);
        this.name = 'TerminalError';
    }
}

/**
 * Error thrown when workflow operations fail due to execution issues, invalid configurations,
 * or problems with step execution and orchestration.
 *
 * @class WorkflowError
 * @extends Error
 */
class WorkflowError extends Error {
    /**
     * Creates a new WorkflowError instance.
     * @param {string} message - The error message explaining the workflow execution failure
     */
    constructor(m: string) {
        super(m);
        this.name = 'WorkflowError';
    }
}

/**
 * Error thrown when attempting to use a feature or method that has not been implemented yet.
 * This serves as a placeholder for planned functionality that is not yet available.
 *
 * @class NotImplementedError
 * @extends Error
 */
class NotImplementedError extends Error {
    /**
     * Creates a new NotImplementedError instance.
     * @param {string} message - The error message explaining what functionality is not yet implemented
     */
    constructor(m: string) {
        super(m);
        this.name = 'NotImplementedError';
    }
}
// #endregion

/**
 * Abstract base class for all AI Agent Tools.
 *
 * This class provides the foundation for implementing tools within the framework.
 * It handles common functionality such as permission management, input validation,
 * error handling, and execution orchestration. All concrete tool implementations
 * must extend this class and provide their specific business logic.
 *
 * @abstract
 * @class BaseTool
 * @implements {ITool}
 *
 * @example
 * ```typescript
 * export class FileSystemTool extends BaseTool {
 *   public readonly name = 'fileSystem';
 *   public readonly description = 'File system operations';
 *   public readonly category = 'System';
 *
 *   public readonly actions = {
 *     readFile: {
 *       description: 'Read content from a file',
 *       inputSchema: z.object({ path: z.string() }),
 *       outputSchema: z.object({ content: z.string() })
 *     }
 *   } as const;
 *
 *   protected async _execute(actionName: string, input: unknown, options: ToolInvokeOptions) {
 *     switch (actionName) {
 *       case 'readFile':
 *         const { path } = input as { path: string };
 *         const content = await fs.readFile(path, 'utf-8');
 *         return { output: { content } };
 *       default:
 *         throw new Error(`Unknown action: ${actionName}`);
 *     }
 *   }
 *
 *   public getDocumentation() {
 *     return `# File System Tool
 *
 * Provides basic file system operations for reading, writing, and managing files.
 *
 * ## Actions
 * - **readFile**: Read content from a file path
 * `;
 *   }
 * }
 * ```
 */
export abstract class BaseTool implements ITool {
    /**
     * The unique name identifier for this tool.
     * Used for tool discovery and invocation.
     */
    public abstract readonly name: string;

    /**
     * Human-readable description of what this tool does.
     * Should be concise but informative.
     */
    public abstract readonly description: string;

    /**
     * Version string for this tool implementation.
     * Follows semantic versioning (e.g., "1.0.0").
     */
    public readonly version = '1.0.0';

    /**
     * Category this tool belongs to for organization and discovery.
     * Examples: 'System', 'Development', 'AI', 'Utilities'
     */
    public abstract readonly category: string;

    /**
     * Registry of all actions supported by this tool.
     * Each action defines its input/output schemas and description.
     */
    public abstract readonly actions: Readonly<Record<string, ToolActionDefinition>>;

    /**
     * Executes the specific business logic for a tool action.
     * This method must be implemented by concrete tool classes.
     *
     * @protected
     * @abstract
     * @param {string} actionName - The name of the action to execute
     * @param {unknown} validatedInput - The validated input data for the action
     * @param {ToolInvokeOptions} options - Execution options including services and context
     * @returns {Promise<{ output: unknown, memoriesCreated?: string[] }>} The execution result
     */
    protected abstract _execute(actionName: string, validatedInput: unknown, options: ToolInvokeOptions): Promise<{ output: unknown, memoriesCreated?: string[] }>;

    /**
     * Main entry point for tool invocation.
     * Handles permission checking, input validation, execution, and error handling.
     *
     * @param {string} actionName - The name of the action to invoke
     * @param {unknown} input - The input data for the action (will be validated)
     * @param {ToolInvokeOptions} options - Execution options and context
     * @returns {Promise<ToolResult>} The result of the tool execution
     *
     * @throws {ToolDefinitionError} If the requested action doesn't exist
     * @throws {PermissionDeniedError} If required permissions are not granted
     * @throws {OperationCancelledError} If the operation is cancelled
     */
    public async invoke(actionName: string, input: unknown, options: ToolInvokeOptions): Promise<ToolResult> {
        const executionId = randomUUID();
        const { services, cancellationToken } = options;
        const actionDef = this.actions[actionName];

        try {
            // Validate action exists
            if (!actionDef) {
                throw new ToolDefinitionError(`Action '${actionName}' not found in tool '${this.name}'.`);
            }

            // Check and request required permissions
            const requiredPermissions = this.getPermissions(actionName);
            if (requiredPermissions.length > 0) {
                const rationale = `Tool '${this.name}' is requesting permissions to perform action '${actionName}'.`;
                const hasPermission = await services.permissionManager.requestPermissions(requiredPermissions, rationale);
                if (!hasPermission) {
                    throw new PermissionDeniedError(`User denied required permissions: ${requiredPermissions.join(', ')}`);
                }
            }

            // Check for cancellation before execution
            if (cancellationToken.isCancellationRequested) {
                throw new OperationCancelledError();
            }

            // Validate input against schema
            const validatedInput = actionDef.inputSchema.parse(input);

            // Execute the tool's business logic
            const { output, memoriesCreated } = await this._execute(actionName, validatedInput, options);

            // Validate output against schema
            const validatedOutput = actionDef.outputSchema.parse(output);

            // Return successful result
            return {
                executionId,
                toolName: this.name,
                actionName,
                status: 'success',
                output: validatedOutput,
                memoriesCreated
            };

        } catch (error) {
            // Handle different types of errors with appropriate structured error information
            let structuredError: { message: string, code?: string, stack?: string, details?: Record<string, unknown> };

            if (error instanceof ZodError) {
                // Schema validation errors
                structuredError = {
                    message: 'Data validation failed',
                    code: 'E_VALIDATION',
                    details: error.flatten()
                };
            } else if (error instanceof PermissionDeniedError) {
                // Permission-related errors
                structuredError = {
                    message: error.message,
                    code: 'E_PERMISSION_DENIED'
                };
            } else if (error instanceof OperationCancelledError) {
                // Cancellation returns cancelled status instead of failure
                return {
                    executionId,
                    toolName: this.name,
                    actionName,
                    status: 'cancelled',
                    error: { message: error.message, code: 'E_CANCELLED' }
                };
            } else if (error instanceof Error) {
                // General execution errors
                structuredError = {
                    message: error.message,
                    stack: error.stack,
                    code: 'E_EXECUTION_ERROR'
                };
            } else {
                // Unknown error types
                structuredError = {
                    message: 'An unknown error occurred',
                    code: 'E_UNKNOWN'
                };
            }

            // Log the error for debugging and monitoring
            services.logger.error(structuredError.message, {
                tool: this.name,
                action: actionName,
                error: structuredError
            });

            // Return failure result
            return {
                executionId,
                toolName: this.name,
                actionName,
                status: 'failure',
                error: structuredError
            };
        }
    }

    /**
     * Returns the permissions required for a specific action.
     * Override this method in concrete tool implementations to specify required permissions.
     *
     * @param {string} actionName - The name of the action to check permissions for
     * @returns {readonly ToolPermission[]} Array of required permissions (empty by default)
     *
     * @example
     * ```typescript
     * public getPermissions(actionName: string): readonly ToolPermission[] {
     *   switch (actionName) {
     *     case 'writeFile':
     *       return ['workspace:write'];
     *     case 'runCommand':
     *       return ['shell:execute'];
     *     default:
     *       return [];
     *   }
     * }
     * ```
     */
    public getPermissions(actionName: string): readonly ToolPermission[] {
        // Log the action name for debugging and audit purposes
        console.log(`Getting permissions for action: ${actionName}`);
        return [];
    }

    /**
     * Returns comprehensive documentation for this tool.
     * This should include usage examples, action descriptions, and any important notes.
     *
     * @abstract
     * @returns {string} Markdown-formatted documentation string
     */
    public abstract getDocumentation(): string;
}
// #endregion

// #region PART 3: Advanced Workflow Integration for Tools

/**
 * A workflow step that executes a specific tool action.
 *
 * This class integrates tools into the advanced workflow system by implementing
 * the WorkflowStep interface. It handles input hydration using workflow context
 * and executes the specified tool action with proper error handling.
 *
 * @class ToolWorkflowStep
 * @implements {WorkflowStep}
 *
 * @example
 * ```typescript
 * const step = new ToolWorkflowStep(
 *   'read-file-step',
 *   'fileSystem',
 *   'readFile',
 *   { path: '{{inputs.filePath}}' },
 *   services,
 *   cancellationToken
 * );
 * ```
 */
export class ToolWorkflowStep implements WorkflowStep {
    /** Unique identifier for this workflow step */
    public readonly id: string;

    /** Human-readable name for this step */
    public readonly name: string;

    /** Detailed description of what this step does */
    public readonly description: string;

    /** The execution function that performs the tool action */
    public readonly execute: (context: WorkflowContext) => Promise<WorkflowStepResult>;

    /** Array of next step IDs to execute after this step */
    public readonly nextSteps: string[];

    /** Whether this step has conditional logic */
    public readonly isConditional?: boolean;

    /** The name of the tool to execute */
    private readonly toolName: string;

    /** The name of the action to execute on the tool */
    private readonly actionName: string;

    /** The input parameters for the tool action (may contain template strings) */
    private readonly input: Record<string, unknown>;

    /** Service container providing access to all services */
    private readonly services: IServiceContainer;

    /** Cancellation token for cancelling the operation */
    private readonly token: CancellationToken;

    /**
     * Creates a new ToolWorkflowStep instance.
     *
     * @param {string} id - Unique identifier for this step
     * @param {string} toolName - Name of the tool to execute
     * @param {string} actionName - Name of the action to execute
     * @param {Record<string, unknown>} input - Input parameters (may contain template strings like {{variableName}})
     * @param {IServiceContainer} services - Service container with access to all services
     * @param {CancellationToken} token - Cancellation token for the operation
     */
    constructor(
        id: string,
        toolName: string,
        actionName: string,
        input: Record<string, unknown>,
        services: IServiceContainer,
        token: CancellationToken
    ) {
        this.id = id;
        this.name = `Tool: ${toolName}.${actionName}`;
        this.description = `Execute ${toolName}.${actionName}`;
        this.nextSteps = [];
        this.toolName = toolName;
        this.actionName = actionName;
        this.input = input;
        this.services = services;
        this.token = token;

        // Define the execution function
        this.execute = async (context: WorkflowContext): Promise<WorkflowStepResult> => {
            // Retrieve the tool from the registry
            const tool = this.services.toolRegistry.getTool(this.toolName);
            if (!tool) {
                const errorMsg = `Tool '${this.toolName}' not found.`;
                return { success: false, error: errorMsg };
            }

            try {
                // Hydrate input parameters using workflow context
                const hydratedInput = this.hydrateInput(this.input, context);

                // Execute the tool action
                const result = await tool.invoke(this.actionName, hydratedInput, {
                    workflowContext: context,
                    services: this.services,
                    cancellationToken: this.token
                });

                // Return the result
                return {
                    success: result.status === 'success',
                    output: result.output,
                    error: result.error ? result.error.message : undefined
                };
            } catch (error: unknown) {
                // Handle any execution errors
                const errorMsg = `Error executing tool '${this.toolName}.${this.actionName}': ${error instanceof Error ? error.message : String(error)}`;
                return { success: false, error: errorMsg };
            }
        };
    }

    /**
     * Hydrates input parameters by replacing template strings with actual values from the workflow context.
     *
     * Template strings use the format {{source.property}} where:
     * - inputs: References workflow input values
     * - outputs: References output values from previous steps
     * - variables: References workflow variables
     *
     * @private
     * @param {Record<string, unknown>} input - The input parameters with potential template strings
     * @param {WorkflowContext} context - The workflow context containing values for hydration
     * @returns {Record<string, unknown>} The hydrated input parameters
     * @throws {WorkflowError} If a template string cannot be resolved
     *
     * @example
     * ```typescript
     * // Input: { path: '{{inputs.filePath}}', content: '{{outputs.readStep.content}}' }
     * // Context: { inputs: { filePath: '/test.txt' }, outputs: { readStep: { content: 'Hello' } } }
     * // Result: { path: '/test.txt', content: 'Hello' }
     * ```
     */
    private hydrateInput(input: Record<string, unknown>, context: WorkflowContext): Record<string, unknown> {
        const hydrated: Record<string, unknown> = {};

        // Process each input parameter
        for (const [key, value] of Object.entries(input)) {
            if (typeof value === 'string' && value.startsWith('{{') && value.endsWith('}}')) {
                // This is a template string that needs to be resolved
                const path = value.slice(2, -2).trim().split('.');
                const [source, ...propertyPath] = path;
                let resolvedValue: unknown;

                // Resolve based on the source type
                if (source === 'inputs') {
                    resolvedValue = context.inputs;
                } else if (source === 'outputs') {
                    resolvedValue = context.outputs;
                } else if (source === 'variables') {
                    resolvedValue = context.variables;
                } else {
                    // Try to find in context properties
                    resolvedValue = (context as unknown as Record<string, unknown>)[source];
                }

                // Ensure the source exists
                if (resolvedValue === undefined) {
                    throw new WorkflowError(`Hydration failed: Source '${source}' not found in context.`);
                }

                // Navigate through the property path
                for (const prop of propertyPath) {
                    resolvedValue = (resolvedValue as Record<string, unknown>)?.[prop];
                }

                // Ensure the final path resolves to a value
                if (resolvedValue === undefined) {
                    throw new WorkflowError(`Hydration failed: Path '${value}' could not be resolved.`);
                }

                hydrated[key] = resolvedValue;
            } else {
                // Not a template string, use the value as-is
                hydrated[key] = value;
            }
        }

        return hydrated;
    }
}

/**
 * Factory class for creating workflows that integrate tools into the advanced workflow system.
 *
 * This factory provides methods to create both complex multi-step workflows from execution plans
 * and simple single-tool workflows. It serves as the primary interface for converting tool
 * execution specifications into executable workflow instances.
 *
 * @class ToolWorkflowFactory
 *
 * @example
 * ```typescript
 * const factory = new ToolWorkflowFactory(services);
 *
 * // Create a simple single-tool workflow
 * const simpleWorkflow = factory.createSimpleToolWorkflow(
 *   'fileSystem',
 *   'readFile',
 *   { path: '/example.txt' }
 * );
 *
 * // Create a complex workflow from a plan
 * const complexWorkflow = factory.createWorkflowFromPlan({
 *   id: 'complex-workflow',
 *   name: 'Process Files',
 *   steps: [
 *     {
 *       id: 'read',
 *       tool_name: 'fileSystem',
 *       action_name: 'readFile',
 *       input: { path: '{{inputs.sourcePath}}' }
 *     },
 *     {
 *       id: 'process',
 *       tool_name: 'textProcessor',
 *       action_name: 'transform',
 *       input: { content: '{{outputs.read.content}}' }
 *     }
 *   ]
 * });
 * ```
 */
export class ToolWorkflowFactory {
    /** Service container providing access to all framework services */
    private readonly services: IServiceContainer;

    /**
     * Creates a new ToolWorkflowFactory instance.
     *
     * @param {IServiceContainer} services - The service container with all required services
     */
    constructor(services: IServiceContainer) {
        this.services = services;
    }

    /**
     * Creates a complex workflow from a detailed execution plan.
     *
     * This method takes a structured plan containing multiple steps and converts it into
     * an executable workflow. Each step in the plan is transformed into a ToolWorkflowStep
     * that can be executed as part of the workflow.
     *
     * @param {Object} plan - The execution plan for the workflow
     * @param {string} plan.id - Unique identifier for the workflow
     * @param {string} plan.name - Human-readable name for the workflow
     * @param {Array} plan.steps - Array of step definitions
     * @param {string} plan.steps[].id - Unique identifier for the step
     * @param {string} plan.steps[].tool_name - Name of the tool to execute
     * @param {string} plan.steps[].action_name - Name of the action to execute
     * @param {Record<string, unknown>} plan.steps[].input - Input parameters (may contain template strings)
     * @param {Array} [plan.inputs] - Optional workflow input definitions
     * @param {string} plan.inputs[].id - Input identifier
     * @param {string} plan.inputs[].name - Input display name
     * @param {string} plan.inputs[].description - Input description
     * @param {string} plan.inputs[].type - Input data type
     * @param {boolean} plan.inputs[].required - Whether the input is required
     * @param {unknown} [plan.inputs[].default] - Default value for the input
     * @returns {Workflow} An executable workflow instance
     *
     * @example
     * ```typescript
     * const workflow = factory.createWorkflowFromPlan({
     *   id: 'file-processing-workflow',
     *   name: 'Process Source Files',
     *   steps: [
     *     {
     *       id: 'read-source',
     *       tool_name: 'fileSystem',
     *       action_name: 'readFile',
     *       input: { path: '{{inputs.sourcePath}}' }
     *     },
     *     {
     *       id: 'validate-content',
     *       tool_name: 'contentValidator',
     *       action_name: 'validate',
     *       input: { content: '{{outputs.read-source.content}}' }
     *     }
     *   ],
     *   inputs: [
     *     {
     *       id: 'sourcePath',
     *       name: 'Source File Path',
     *       description: 'Path to the source file to process',
     *       type: 'string',
     *       required: true
     *     }
     *   ]
     * });
     * ```
     */
    public createWorkflowFromPlan(plan: {
        id: string;
        name: string;
        steps: Array<{
            id: string;
            tool_name: string;
            action_name: string;
            input: Record<string, unknown>;
        }>;
        inputs?: Array<{
            id: string;
            name: string;
            description: string;
            type: 'string' | 'number' | 'boolean' | 'object' | 'array';
            required: boolean;
            default?: unknown;
        }>;
    }): Workflow {
        // Convert plan steps into executable ToolWorkflowStep instances
        const steps: WorkflowStep[] = plan.steps.map(step =>
            new ToolWorkflowStep(
                step.id,
                step.tool_name,
                step.action_name,
                step.input,
                this.services,
                new vscode.CancellationTokenSource().token
            )
        );

        // Create the workflow definition
        const workflowDefinition: WorkflowDefinition = {
            id: plan.id,
            name: plan.name,
            description: `Tool-based workflow: ${plan.name}`,
            version: '1.0.0',
            steps,
            inputs: plan.inputs || [],
            outputs: [],
            startStepId: plan.steps[0]?.id || ''
        };

        return new Workflow(workflowDefinition);
    }

    /**
     * Creates a simple workflow that executes a single tool action.
     *
     * This is a convenience method for creating straightforward workflows that only
     * need to execute one tool action. It's useful for simple operations or as
     * building blocks for more complex workflows.
     *
     * @param {string} toolName - Name of the tool to execute
     * @param {string} actionName - Name of the action to execute on the tool
     * @param {Record<string, unknown>} input - Input parameters for the action
     * @param {string} [workflowId] - Optional custom ID for the workflow (auto-generated if not provided)
     * @returns {Workflow} An executable workflow instance containing a single tool step
     *
     * @example
     * ```typescript
     * // Create a simple workflow to read a file
     * const readFileWorkflow = factory.createSimpleToolWorkflow(
     *   'fileSystem',
     *   'readFile',
     *   { path: '/path/to/file.txt' }
     * );
     *
     * // Create with custom workflow ID
     * const customWorkflow = factory.createSimpleToolWorkflow(
     *   'terminal',
     *   'execute',
     *   { command: 'ls', args: ['-la'] },
     *   'list-directory-workflow'
     * );
     * ```
     */
    public createSimpleToolWorkflow(
        toolName: string,
        actionName: string,
        input: Record<string, unknown>,
        workflowId: string = `tool-${toolName}-${Date.now()}`
    ): Workflow {
        const stepId = 'execute-tool';

        // Create a single tool workflow step
        const step = new ToolWorkflowStep(
            stepId,
            toolName,
            actionName,
            input,
            this.services,
            new vscode.CancellationTokenSource().token
        );

        // Create the workflow definition for the single step
        const workflowDefinition: WorkflowDefinition = {
            id: workflowId,
            name: `Execute ${toolName}.${actionName}`,
            description: `Simple workflow to execute ${toolName}.${actionName}`,
            version: '1.0.0',
            steps: [step],
            inputs: [],
            outputs: [],
            startStepId: stepId
        };

        return new Workflow(workflowDefinition);
    }
}

const WorkflowPlanSchema = z.object({
    thought: z.string().describe('The reasoning and strategy for the generated plan.'),
    steps: z.array(z.object({
        id: z.string().describe('A unique camelCase identifier for this step, e.g., \'readFileStep\'.'),
        tool_name: z.string().describe('The exact name of the tool to use.'),
        action_name: z.string().describe('The exact name of the action to execute.'),
        input: z.record(z.unknown()).describe('Input parameters. Use \'$stepId.output\' to reference outputs from previous steps.'),
    })).describe('The sequence of tool calls to achieve the goal.'),
});

/**
 * The central orchestrator for AI agent operations within the tool framework.
 *
 * This class serves as the primary coordinator for executing complex goals by leveraging
 * available tools and workflows. It handles goal analysis, workflow planning, execution,
 * and result processing. The orchestrator uses AI language models to create intelligent
 * execution plans and manages the entire lifecycle of AI agent operations.
 *
 * Key responsibilities:
 * - Goal analysis and context gathering using memory systems
 * - Dynamic workflow planning using language models
 * - Tool orchestration and execution coordination
 * - Progress tracking and result reporting
 * - Memory management for learning and context preservation
 *
 * @class Orchestrator
 *
 * @example
 * ```typescript
 * const orchestrator = new Orchestrator(services);
 *
 * // Execute a complex goal
 * const result = await orchestrator.run(
 *   'Analyze the codebase and generate documentation',
 *   {
 *     visualContext: [imageData],
 *     token: cancellationToken
 *   }
 * );
 *
 * // Execute a simple tool directly
 * const simpleResult = await orchestrator.runSimpleTool(
 *   'fileSystem',
 *   'readFile',
 *   { path: '/path/to/file.txt' },
 *   cancellationToken
 * );
 * ```
 */
export class Orchestrator {
    /** The language model used for planning and reasoning */
    private readonly llm: ILanguageModel;

    /** Factory for creating workflows from tool execution plans */
    private readonly workflowFactory: ToolWorkflowFactory;

    /** Event emitter for workflow lifecycle events */
    private readonly _emitter = new EventEmitter();

    /**
     * Creates a new Orchestrator instance.
     *
     * @param {IServiceContainer} services - The service container providing access to all framework services
     */
    constructor(private readonly services: IServiceContainer) {
        this.llm = services.llmProvider.getActiveModel();
        this.workflowFactory = new ToolWorkflowFactory(services);
    }

    /**
     * Registers an event listener for workflow lifecycle events.
     *
     * @param {'workflowStart' | 'stepComplete' | 'workflowEnd'} event - The event type to listen for
     * @param {(...args: unknown[]) => void} listener - The event handler function
     *
     * @example
     * ```typescript
     * orchestrator.on('workflowStart', ({ workflowId, goal }) => {
     *   console.log(`Started workflow ${workflowId} for goal: ${goal}`);
     * });
     *
     * orchestrator.on('workflowEnd', ({ workflowId, finalContext }) => {
     *   console.log(`Completed workflow ${workflowId}: ${finalContext.status}`);
     * });
     * ```
     */
    public on(event: 'workflowStart' | 'stepComplete' | 'workflowEnd', listener: (...args: unknown[]) => void): void {
        this._emitter.on(event, listener);
    }

    /**
     * Executes a complex goal by analyzing requirements, planning workflow, and orchestrating tool execution.
     *
     * This is the main entry point for AI agent operations. It performs several key steps:
     * 1. Analyzes the goal and retrieves relevant context from memory
     * 2. Uses AI to generate a structured workflow plan
     * 3. Creates and executes a workflow based on the plan
     * 4. Tracks progress and reports results
     * 5. Stores execution results in memory for future learning
     *
     * @param {string} goal - The goal to achieve (e.g., "analyze codebase and generate documentation")
     * @param {Object} options - Execution options
     * @param {MultimodalData[]} [options.visualContext] - Optional visual context (images) for analysis
     * @param {CancellationToken} options.token - Cancellation token for the operation
     * @returns {Promise<WorkflowStepResult>} The result of the workflow execution
     *
     * @throws {Error} If workflow planning or execution fails
     *
     * @example
     * ```typescript
     * const result = await orchestrator.run(
     *   'Create a summary of all TypeScript files in the project',
     *   {
     *     visualContext: [], // No images provided
     *     token: cancellationToken
     *   }
     * );
     *
     * if (result.success) {
     *   console.log('Execution successful:', result.output);
     * } else {
     *   console.error('Execution failed:', result.error);
     * }
     * ```
     */
    public async run(goal: string, options: { visualContext?: MultimodalData[], token: CancellationToken }): Promise<WorkflowStepResult> {
        const { visualContext, token } = options;
        const workflowId = randomUUID();

        return this.services.ui.showProgress(`AI Agent: ${goal}`, async (progress) => {
            this._emitter.emit('workflowStart', { workflowId, goal });
            progress.report({ message: 'Analyzing goal and memories...' });

            // Utilize memory manager for context retrieval
            const relevantMemories = await this.services.memoryManager.searchMemories({
                query: goal,
                limit: 10
            } as MemorySearchOptions);

            // Store search results in memory for future reference
            const memoryEntry: MemoryEntry = {
                id: randomUUID(),
                content: `Goal: ${goal} | Memories found: ${relevantMemories.length}`,
                timestamp: Date.now(),
                metadata: {
                    source: 'orchestrator' as MemorySource, // Type assertion for compatibility
                    type: 'goal_analysis' as MemoryType,
                    tags: ['goal_analysis', 'workflow_execution'],
                    goal,
                    memoryCount: relevantMemories.length,
                    workflowId
                }
            };

            // Use memoryManager instance with full MemoryManager functionality
            try {
                // Get memory manager from services
                const memoryManager = this.services.memoryManager as IMemoryOperations & { storeMemory?: (entry: MemoryEntry) => Promise<void> };
                await memoryManager.storeMemory?.(memoryEntry);
            } catch (memoryError) {
                // Silently handle memory storage errors
                console.warn('Failed to store memory:', memoryError);
            }

            // Get configuration for enhanced workflow features using getConfig directly
            const config = getConfig('aiAgent', {}) as { enableAdvancedFeatures?: boolean };
            const enableAdvancedFeatures = config.enableAdvancedFeatures ?? true;

            const plan = await this.createWorkflowPlan(goal, visualContext, token);
            this.services.logger.info('Generated workflow plan', { workflowId, plan });

            // Create workflow using the factory with enhanced features
            const workflow = this.workflowFactory.createWorkflowFromPlan({
                id: workflowId,
                name: `Workflow for: ${goal}`,
                steps: plan.steps
            });

            // Set up agent for the workflow if the workflow supports it
            if ('setAgent' in workflow && typeof workflow.setAgent === 'function') {
                const workflowWithAgent = workflow as { setAgent: (agent: unknown) => void };
                workflowWithAgent.setAgent({});
            }

            // Enable advanced features if configured
            if (enableAdvancedFeatures) {
                workflow.enableAdvancedFeatures({
                    goddessMode: true,
                    quantumAnalysis: true,
                    neuralSynthesis: false,
                    timeTravelDebugging: false,
                    adaptivePersonality: true
                });
            }

            progress.report({ message: 'Executing workflow...' });

            // Execute the workflow - simplified implementation
            const finalResult = await this.executeWorkflowSimple(workflow);

            // Register workflow with registry for future reference
            const registry = workflowRegistry as unknown as { registerWorkflow?: (definition: unknown) => void };
            const definition = workflow.getDefinition?.();
            registry.registerWorkflow?.(definition);

            this._emitter.emit('workflowEnd', {
                workflowId,
                finalContext: {
                    status: finalResult.success ? 'completed' : 'failed',
                    endTime: new Date()
                }
            });

            this.services.logger.info('Workflow finished with result:', { workflowId, finalResult });
            return finalResult;
        });
    }

    public async runSimpleTool(
        toolName: string,
        actionName: string,
        input: Record<string, unknown>,
        token: CancellationToken
    ): Promise<WorkflowStepResult> {
        const workflow = this.workflowFactory.createSimpleToolWorkflow(toolName, actionName, input);
        const context = workflow.getContext() as WorkflowContext & { cancellationToken?: CancellationToken };
        context.cancellationToken = token; // Use the provided cancellation token
        return await (workflow as unknown as { execute?: (context: WorkflowContext) => Promise<WorkflowStepResult> }).execute?.(context) || { success: false, error: 'Workflow execution not supported' };
    }

    private async createWorkflowPlan(goal: string, visualContext: MultimodalData[] | undefined, token: CancellationToken): Promise<z.infer<typeof WorkflowPlanSchema>> {
        const relevantMemories = await this.services.memoryManager.searchMemories({ query: goal, limit: 5 });
        const toolDocs = this.services.toolRegistry.getAllTools().map(t => t.getDocumentation()).join('\n---\n');
        const promptParts: LanguageModelPromptPart[] = [];
        const textualPrompt = `You are a world-class AI orchestrator. Your task is to create a JSON workflow plan to achieve the user's GOAL.\n\nGOAL: "${goal}"\n\nRELEVANT MEMORIES (for context):\n${relevantMemories.length > 0 ? (relevantMemories as unknown[]).map(m => `- ${(m as { content: string }).content}`).join('\n') : 'None'}\n\nAVAILABLE TOOLS:\n${toolDocs}\n\nINSTRUCTIONS:\n1. Think step-by-step to decompose the goal into a sequence of tool calls.\n2. Define the workflow as a JSON object strictly matching this schema: { "thought": "string", "steps": [{ "id": "string", "tool_name": "string", "action_name": "string", "input": "object" }] }\n3. To use an output from a previous step, use the syntax "$stepId.output".\n4. If images are provided, incorporate your analysis of them into the plan.\n5. Ensure the plan is logical and directly works towards solving the user's GOAL.\n\nRESPONSE (JSON object only):`;
        if (visualContext?.length) {
            promptParts.push({ type: 'text', text: 'Analyze image(s)...' });
            visualContext.forEach(image => { promptParts.push({ type: 'image', source: { type: 'base64', media_type: image.mimeType, data: image.base64Data } }); });
        }
        promptParts.push({ type: 'text', text: textualPrompt });
        const responseJson = await this.llm.generateResponse(promptParts, { cancellationToken: token, jsonOutput: true });
        return WorkflowPlanSchema.parse(JSON.parse(responseJson));
    }

    /**
     * Simplified workflow execution for when the full workflow engine is not available
     */
    private async executeWorkflowSimple(workflow: Workflow): Promise<WorkflowStepResult> {
        const definition = workflow.getDefinition();
        const context = workflow.getContext();

        // Use workflowManager for advanced workflow coordination if available
        interface WorkflowManager {
            coordinateWorkflowExecution?: (
                definition: unknown,
                context: WorkflowContext,
                options: {
                    enableParallelExecution: boolean;
                    enableErrorRecovery: boolean;
                    enableProgressTracking: boolean;
                }
            ) => Promise<WorkflowStepResult>;
        }

        const wm = workflowManager as WorkflowManager;
        if (wm.coordinateWorkflowExecution) {
            try {
                // Leverage workflowManager for enhanced execution
                const enhancedResult = await wm.coordinateWorkflowExecution(
                    definition,
                    context,
                    {
                        enableParallelExecution: false,
                        enableErrorRecovery: true,
                        enableProgressTracking: true
                    }
                );

                if (enhancedResult) {
                    return {
                        success: enhancedResult.success,
                        output: enhancedResult.output,
                        error: enhancedResult.error
                    };
                }
            } catch (error) {
                // Fall back to simple execution if advanced features fail
                this.services.logger.warn('Advanced workflow execution failed, falling back to simple execution', { error: error instanceof Error ? error.message : String(error) });
            }
        }

        // Execute steps sequentially
        for (let i = 0; i < definition.steps.length; i++) {
            const step = definition.steps[i];
            const startTime = Date.now();

            // Record step start in history
            context.history.push({
                stepId: step.id,
                startTime: new Date(startTime),
                result: undefined
            });

            try {
                // Execute the step
                const result = await step.execute(context);

                // Record step completion
                const historyEntry = context.history[context.history.length - 1];
                historyEntry.endTime = new Date();
                historyEntry.result = result;

                // Store output in context if successful
                if (result.success && result.output !== undefined) {
                    context.outputs[step.id] = result.output;
                }

                // If step failed, return failure result
                if (!result.success) {
                    return {
                        success: false,
                        error: result.error || `Step '${step.name}' failed`
                    };
                }

                // If step specifies next step, jump to it
                if (result.nextStepId) {
                    const nextIndex = definition.steps.findIndex(s => s.id === result.nextStepId);
                    if (nextIndex !== -1) {
                        i = nextIndex - 1; // Will be incremented by loop
                    }
                }
            } catch (error: unknown) {
                // Record error in history
                const historyEntry = context.history[context.history.length - 1];
                historyEntry.endTime = new Date();
                historyEntry.result = {
                    success: false,
                    error: error instanceof Error ? error.message : String(error)
                };

                return {
                    success: false,
                    error: error instanceof Error ? error.message : String(error)
                };
            }
        }

        // All steps completed successfully
        return {
            success: true,
            output: context.outputs
        };
    }
}

/**
 * VS Code implementation of the IPermissionManager interface.
 *
 * This service handles permission requests within the VS Code environment by:
 * - Storing granted permissions in VS Code's global state
 * - Presenting permission requests to users via modal dialogs
 * - Managing the lifecycle of permission grants
 *
 * The service persists permission grants across VS Code sessions using the
 * extension's global state storage.
 *
 * @class VSCodePermissionManager
 * @implements {IPermissionManager}
 *
 * @example
 * ```typescript
 * const permissionManager = new VSCodePermissionManager();
 * await permissionManager.initialize(context);
 *
 * const granted = await permissionManager.requestPermissions(
 *   ['workspace:write', 'shell:execute'],
 *   'This tool needs to modify files and run commands.'
 * );
 * ```
 */
export class VSCodePermissionManager implements IPermissionManager {
    /** VS Code extension context for accessing global state */
    private context: ExtensionContext | null = null;

    /** Key used to store granted permissions in VS Code global state */
    private readonly GRANTED_KEY = 'ai-agent.grantedPermissions';

    /**
     * Initializes the permission manager with the VS Code extension context.
     *
     * This method must be called before using the permission manager to request permissions.
     * The context provides access to VS Code's global state for persisting permission grants.
     *
     * @param {ExtensionContext} context - The VS Code extension context
     *
     * @example
     * ```typescript
     * // During extension activation
     * await permissionManager.initialize(context);
     * ```
     */
    public async initialize(context: ExtensionContext): Promise<void> {
        this.context = context; // Store context for potential future use
    }

    /**
     * Requests one or more permissions from the user with a clear rationale.
     *
     * This method:
     * 1. Checks if the requested permissions are already granted
     * 2. Filters out already-granted permissions
     * 3. Presents a modal dialog to the user explaining why permissions are needed
     * 4. Updates the persistent permission store if the user grants permissions
     *
     * @param {readonly ToolPermission[]} permissions - Array of permissions to request
     * @param {string} rationale - Clear explanation of why these permissions are needed
     * @returns {Promise<boolean>} Promise that resolves to true if all permissions are granted
     *
     * @throws {Error} If the permission manager has not been initialized
     *
     * @example
     * ```typescript
     * const granted = await permissionManager.requestPermissions(
     *   ['workspace:write', 'terminal:interactive_session'],
     *   'This tool needs to create files and run interactive terminal sessions.'
     * );
     *
     * if (!granted) {
     *   throw new Error('Required permissions were not granted by the user.');
     * }
     * ```
     */
    public async requestPermissions(permissions: readonly ToolPermission[], rationale: string): Promise<boolean> {
        // Return early if no permissions are requested
        if (!permissions.length) {
            return true;
        }

        // Ensure the manager has been initialized
        if (!this.context) {
            throw new Error('Permission manager not initialized.');
        }

        // Get currently granted permissions from persistent storage
        const granted = new Set(this.context.globalState.get<ToolPermission[]>(this.GRANTED_KEY, []));

        // Filter out permissions that are already granted
        const needed = permissions.filter(p => !granted.has(p));

        // Return early if all requested permissions are already granted
        if (!needed.length) {
            return true;
        }

        // Present permission request dialog to user
        const decision = await vscode.window.showWarningMessage(
            `${rationale} Requires: ${needed.join(', ')}. Grant?`,
            { modal: true },
            'Grant'
        );

        // Update persistent storage if user granted permissions
        if (decision === 'Grant') {
            needed.forEach(p => granted.add(p));
            await this.context.globalState.update(this.GRANTED_KEY, Array.from(granted));
            return true;
        }

        // User denied the permissions
        return false;
    }
}

/**
 * A simple, static implementation of the IToolRegistry interface.
 *
 * This registry maintains a collection of tools in memory and provides basic
 * methods for tool discovery and management. It's suitable for scenarios where
 * the set of available tools is known at initialization time and doesn't change
 * dynamically during runtime.
 *
 * Key features:
 * - In-memory storage of tool instances
 * - Fast lookup by tool name
 * - Immutable view of all registered tools
 * - Service container integration for tool initialization
 *
 * @class StaticToolRegistry
 * @implements {IToolRegistry}
 *
 * @example
 * ```typescript
 * const registry = new StaticToolRegistry();
 * await registry.initialize(serviceContainer);
 *
 * // Register tools (implementation would need to be added)
 * // registry.registerTool(new FileSystemTool());
 * // registry.registerTool(new TerminalTool());
 *
 * // Retrieve tools
 * const fileTool = registry.getTool('fileSystem');
 * const allTools = registry.getAllTools();
 * ```
 */
export class StaticToolRegistry implements IToolRegistry {
    /** Internal storage for registered tools */
    private tools = new Map<string, ITool>();

    /** Reference to the service container for tool initialization */
    private services: IServiceContainer | null = null;

    /**
     * Initializes the tool registry with the provided service container.
     *
     * This method stores a reference to the service container which can be used
     * by tools during their initialization or operation. The container provides
     * access to all framework services that tools might need.
     *
     * @param {IServiceContainer} services - The service container with all required services
     * @returns {Promise<void>} A promise that resolves when initialization is complete
     *
     * @example
     * ```typescript
     * // During extension activation
     * await toolRegistry.initialize(serviceContainer);
     * ```
     */
    public async initialize(services: IServiceContainer): Promise<void> {
        this.services = services; // Store services for potential future use
    }

    /**
     * Retrieves a tool by its name.
     *
     * This method performs a fast lookup in the internal tool registry and returns
     * the tool instance if found, or undefined if no tool with the specified name exists.
     *
     * @param {string} toolName - The name of the tool to retrieve
     * @returns {ITool | undefined} The tool instance if found, otherwise undefined
     *
     * @example
     * ```typescript
     * const tool = toolRegistry.getTool('fileSystem');
     * if (tool) {
     *   // Use the tool
     *   const result = await tool.invoke('readFile', { path: '/example.txt' }, options);
     * }
     * ```
     */
    public getTool(toolName: string): ITool | undefined {
        return this.tools.get(toolName);
    }

    /**
     * Retrieves all registered tools as an immutable array.
     *
     * This method returns a read-only view of all tools currently registered in the
     * registry. The returned array cannot be modified, ensuring the integrity of
     * the internal tool collection.
     *
     * @returns {readonly ITool[]} An immutable array of all registered tools
     *
     * @example
     * ```typescript
     * const allTools = toolRegistry.getAllTools();
     * console.log(`Found ${allTools.length} tools:`);
     * for (const tool of allTools) {
     *   console.log(`- ${tool.name}: ${tool.description}`);
     * }
     * ```
     */
    public getAllTools(): readonly ITool[] {
        return Array.from(this.tools.values());
    }
}

/**
 * Abstract base class for language model implementations.
 *
 * This class provides common functionality and structure for all language model
 * implementations within the framework. It handles logging infrastructure and
 * provides a consistent constructor pattern for all language model services.
 *
 * Key features:
 * - Integrated logging support for all language model operations
 * - Consistent service container integration
 * - Abstract interface for response generation
 *
 * @abstract
 * @class BaseLanguageModel
 * @implements {ILanguageModel}
 *
 * @example
 * ```typescript
 * export class CustomLanguageModel extends BaseLanguageModel {
 *   public async generateResponse(
 *     prompt: LanguageModelPrompt,
 *     options: { cancellationToken: CancellationToken; jsonOutput: boolean; }
 *   ): Promise<string> {
 *     // Custom implementation here
 *     this.logger.info('Generating response with custom model');
 *     return 'Custom model response';
 *   }
 * }
 * ```
 */
export abstract class BaseLanguageModel implements ILanguageModel {
    /** Logger instance for tracking language model operations and debugging */
    protected readonly logger: ILogger;

    /**
     * Creates a new BaseLanguageModel instance.
     *
     * @param {IServiceContainer} services - The service container providing access to framework services
     */
    constructor(services: IServiceContainer) {
        this.logger = services.logger;
    }

    /**
     * Generates a response from the language model based on the provided prompt.
     *
     * This abstract method must be implemented by concrete language model classes
     * to provide the actual model interaction logic.
     *
     * @abstract
     * @param {LanguageModelPrompt} prompt - The input prompt for the language model
     * @param {Object} options - Generation options
     * @param {CancellationToken} options.cancellationToken - Token to cancel the generation
     * @param {boolean} options.jsonOutput - Whether to force JSON output format
     * @returns {Promise<string>} A promise that resolves with the generated text response
     *
     * @example
     * ```typescript
     * const response = await languageModel.generateResponse(prompt, {
     *   cancellationToken,
     *   jsonOutput: true
     * });
     * ```
     */
    abstract generateResponse(
        prompt: LanguageModelPrompt,
        options: { cancellationToken: CancellationToken; jsonOutput: boolean; }
    ): Promise<string>;
}

/**
 * OpenAI implementation of the ILanguageModel interface.
 *
 * This class provides integration with OpenAI's GPT models through their REST API.
 * It supports both text-only and multimodal prompts (with image support), and handles
 * cancellation tokens, timeout management, and proper error handling.
 *
 * Key features:
 * - Full OpenAI Chat Completions API support
 * - Multimodal prompt handling (text + images)
 * - Configurable model selection and parameters
 * - Automatic timeout and cancellation handling
 * - JSON output format support for structured responses
 * - Comprehensive error handling and logging
 *
 * @class OpenAIModel
 * @extends {BaseLanguageModel}
 * @implements {ILanguageModel}
 *
 * @example
 * ```typescript
 * const openAIModel = new OpenAIModel(services);
 *
 * const response = await openAIModel.generateResponse(
 *   [
 *     { type: 'text', text: 'Describe this image:' },
 *     { type: 'image', source: { type: 'base64', media_type: 'image/jpeg', data: '...' } }
 *   ],
 *   {
 *     cancellationToken,
 *     jsonOutput: false
 *   }
 * );
 * ```
 */
export class OpenAIModel extends BaseLanguageModel {
    /**
     * Generates a response from OpenAI's language model based on the provided prompt.
     *
     * This method handles the complete flow of preparing the request, sending it to
     * OpenAI's API, processing the response, and handling any errors that occur.
     *
     * @param {LanguageModelPrompt} prompt - The input prompt containing text and/or images
     * @param {Object} options - Generation options
     * @param {CancellationToken} options.cancellationToken - Token to cancel the generation
     * @param {boolean} options.jsonOutput - Whether to force JSON output format
     * @returns {Promise<string>} A promise that resolves with the generated text response
     *
     * @throws {OperationCancelledError} If the operation is cancelled via cancellation token
     * @throws {Error} If the API request fails or configuration is invalid
     *
     * @example
     * ```typescript
     * const prompt = [
     *   { type: 'text', text: 'Explain the following code:' },
     *   { type: 'text', text: 'function add(a, b) { return a + b; }' }
     * ];
     *
     * const response = await openAIModel.generateResponse(prompt, {
     *   cancellationToken,
     *   jsonOutput: true
     * });
     * ```
     */
    public async generateResponse(
        prompt: LanguageModelPrompt,
        options: { cancellationToken: CancellationToken; jsonOutput: boolean; }
    ): Promise<string> {
        // Retrieve OpenAI configuration from VS Code settings
        const config = vscode.workspace.getConfiguration('aiAgent.openai');
        const apiKey = process.env.OPENAI_API_KEY || config.get<string>('apiKey');

        // Validate API key configuration
        if (!apiKey) {
            throw new Error('OpenAI API key not configured');
        }

        // Get API base URL with fallback to default
        const baseUrl = config.get<string>('baseUrl', 'https://api.openai.com/v1');

        // Set up request cancellation and timeout
        const controller = new AbortController();
        const timeout = setTimeout(() => controller.abort(), 30000);

        // Check for cancellation before starting
        if (options.cancellationToken.isCancellationRequested) {
            throw new OperationCancelledError();
        }

        try {
            // Convert multimodal prompt to OpenAI message format
            const messages = (Array.isArray(prompt) ? prompt : [prompt]).map(p => {
                if (typeof p === 'string') {
                    return { role: 'user' as const, content: p };
                } else if ('text' in p) {
                    return { role: 'user' as const, content: p.text };
                } else if ('image' in p) {
                    // Note: OpenAI supports images, but this implementation uses placeholder
                    return { role: 'user' as const, content: '[Image data]' };
                }
                return { role: 'user' as const, content: String(p) };
            });

            // Prepare request body with configuration
            const requestBody = {
                model: config.get<string>('model', 'gpt-4-turbo'),
                messages,
                temperature: 0.1,
                // Add JSON format specification if requested
                ...(options.jsonOutput && { response_format: { type: 'json_object' } })
            };

            // Make API request to OpenAI
            const response = await fetch(`${baseUrl}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });

            // Handle API errors
            if (!response.ok) {
                const errorBody = await response.text();
                throw new Error(`API Error ${response.status}: ${errorBody}`);
            }

            // Parse and validate response
            interface OpenAIResponse {
                choices: Array<{ message: { content: string } }>;
                [key: string]: unknown;
            }
            const data = await response.json() as OpenAIResponse;

            // Extract and return the response content
            return data.choices[0]?.message?.content || '';

        } catch (error) {
            // Log error for debugging
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            this.logger.error(errorMessage, { message: 'OpenAI request failed.' });

            // Re-throw the error for proper handling by calling code
            throw error;

        } finally {
            // Clean up timeout regardless of outcome
            clearTimeout(timeout);
        }
    }
}

/**
 * Ollama implementation of the ILanguageModel interface.
 *
 * This class provides integration with Ollama, a local LLM server that runs
 * various open-source language models. It's designed for scenarios where users
 * want to run models locally without relying on external API services.
 *
 * Key features:
 * - Local LLM execution via Ollama server
 * - Support for various open-source models
 * - Cancellation token support for operation control
 * - Comprehensive logging and error handling
 * - Placeholder implementation with proper scaffolding
 *
 * Note: This implementation is currently a placeholder and requires the actual
 * Ollama client integration to be completed.
 *
 * @class OllamaModel
 * @extends {BaseLanguageModel}
 * @implements {ILanguageModel}
 *
 * @example
 * ```typescript
 * const ollamaModel = new OllamaModel(services);
 *
 * try {
 *   const response = await ollamaModel.generateResponse(
 *     [{ type: 'text', text: 'Explain quantum computing' }],
 *     {
 *       cancellationToken,
 *       jsonOutput: false
 *     }
 *   );
 * } catch (error) {
 *   // Will throw NotImplementedError until implementation is completed
 *   console.error('Ollama integration not yet implemented');
 * }
 * ```
 */
export class OllamaModel extends BaseLanguageModel {
    /**
     * Generates a response using the Ollama local LLM server.
     *
     * This method is currently a placeholder implementation that validates
     * configuration and logs the request parameters but does not actually
     * communicate with an Ollama server. The implementation serves as a
     * foundation for the eventual Ollama client integration.
     *
     * @param {LanguageModelPrompt} prompt - The input prompt for the language model
     * @param {Object} options - Generation options
     * @param {CancellationToken} options.cancellationToken - Token to cancel the generation
     * @param {boolean} options.jsonOutput - Whether to force JSON output format
     * @returns {Promise<string>} A promise that resolves with the generated text response
     *
     * @throws {OperationCancelledError} If the operation is cancelled via cancellation token
     * @throws {Error} If the Ollama model is not configured
     * @throws {NotImplementedError} Always thrown until the actual implementation is completed
     *
     * @example
     * ```typescript
     * const prompt = [
     *   { type: 'text', text: 'What is the capital of France?' }
     * ];
     *
     * try {
     *   const response = await ollamaModel.generateResponse(prompt, {
     *     cancellationToken,
     *     jsonOutput: false
     *   });
     * } catch (error) {
     *   if (error instanceof NotImplementedError) {
     *     console.log('Ollama integration is not yet implemented');
     *   }
     * }
     * ```
     */
    public async generateResponse(
        prompt: LanguageModelPrompt,
        options: { cancellationToken: CancellationToken; jsonOutput: boolean; }
    ): Promise<string> {
        // Retrieve Ollama configuration from VS Code settings
        const config = vscode.workspace.getConfiguration('aiAgent.ollama');
        const model = config.get<string>('model');
        const baseUrl = config.get<string>('baseUrl', 'http://localhost:11434');

        // Check for cancellation before proceeding with validation
        if (options.cancellationToken.isCancellationRequested) {
            throw new OperationCancelledError();
        }

        // Validate model configuration
        if (!model) {
            throw new Error('Ollama model not configured.');
        }

        // Log the configuration details and prompt length for debugging
        this.logger.info('Using Ollama.', {
            baseUrl,
            model,
            promptLength: prompt.length,
            jsonOutput: options.jsonOutput
        });

        // Check for cancellation before proceeding with implementation
        if (options.cancellationToken.isCancellationRequested) {
            throw new OperationCancelledError();
        }

        // Log that implementation is pending
        this.logger.warn('Ollama client not implemented yet, but parameters are being utilized for logging and validation');

        // Throw NotImplementedError until the actual implementation is completed
        throw new NotImplementedError('Ollama client not implemented.');
    }
}

/**
 * LM Studio implementation of the ILanguageModel interface.
 *
 * This class provides integration with LM Studio, a user-friendly desktop application
 * for running LLMs locally. LM Studio provides an OpenAI-compatible API server that
 * can run various quantized models locally, offering privacy and control benefits.
 *
 * Key features:
 * - Local LLM execution via LM Studio's OpenAI-compatible server
 * - Support for various quantized models (GGUF format)
 * - OpenAI-compatible API for easy integration
 * - Cancellation token support for operation control
 * - Comprehensive logging and error handling
 * - Placeholder implementation with proper scaffolding
 *
 * Note: This implementation is currently a placeholder and requires the actual
 * LM Studio client integration to be completed. LM Studio typically runs on
 * localhost:1234 and provides an OpenAI-compatible API.
 *
 * @class LMStudioModel
 * @extends {BaseLanguageModel}
 * @implements {ILanguageModel}
 *
 * @example
 * ```typescript
 * const lmStudioModel = new LMStudioModel(services);
 *
 * try {
 *   const response = await lmStudioModel.generateResponse(
 *     [{ type: 'text', text: 'Explain machine learning' }],
 *     {
 *       cancellationToken,
 *       jsonOutput: false
 *     }
 *   );
 * } catch (error) {
 *   // Will throw NotImplementedError until implementation is completed
 *   console.error('LM Studio integration not yet implemented');
 * }
 * ```
 */
export class LMStudioModel extends BaseLanguageModel {
    /**
     * Generates a response using the LM Studio local LLM server.
     *
     * This method is currently a placeholder implementation that validates
     * configuration and logs the request parameters but does not actually
     * communicate with an LM Studio server. The implementation serves as a
     * foundation for the eventual LM Studio client integration.
     *
     * LM Studio typically provides an OpenAI-compatible API, so the eventual
     * implementation will be similar to the OpenAI implementation but with
     * different configuration defaults.
     *
     * @param {LanguageModelPrompt} prompt - The input prompt for the language model
     * @param {Object} options - Generation options
     * @param {CancellationToken} options.cancellationToken - Token to cancel the generation
     * @param {boolean} options.jsonOutput - Whether to force JSON output format
     * @returns {Promise<string>} A promise that resolves with the generated text response
     *
     * @throws {OperationCancelledError} If the operation is cancelled via cancellation token
     * @throws {NotImplementedError} Always thrown until the actual implementation is completed
     *
     * @example
     * ```typescript
     * const prompt = [
     *   { type: 'text', text: 'What are the benefits of local LLMs?' }
     * ];
     *
     * try {
     *   const response = await lmStudioModel.generateResponse(prompt, {
     *     cancellationToken,
     *     jsonOutput: false
     *   });
     * } catch (error) {
     *   if (error instanceof NotImplementedError) {
     *     console.log('LM Studio integration is not yet implemented');
     *   }
     * }
     * ```
     */
    public async generateResponse(
        prompt: LanguageModelPrompt,
        options: { cancellationToken: CancellationToken; jsonOutput: boolean; }
    ): Promise<string> {
        // Retrieve LM Studio configuration from VS Code settings
        const config = vscode.workspace.getConfiguration('aiAgent.lmstudio');
        const baseUrl = config.get<string>('baseUrl', 'http://localhost:1234/v1');

        // Log the configuration details and prompt length for debugging
        this.logger.info('Using LM Studio.', {
            baseUrl,
            promptLength: prompt.length,
            jsonOutput: options.jsonOutput
        });

        // Check for cancellation before proceeding
        if (options.cancellationToken.isCancellationRequested) {
            throw new OperationCancelledError();
        }

        // Log that implementation is pending
        this.logger.warn('LM Studio client not implemented yet, but parameters are being utilized for logging and validation');

        // Throw NotImplementedError until the actual implementation is completed
        throw new NotImplementedError('LM Studio client not implemented.');
    }
}

/**
 * VS Code implementation of the ILanguageModelProvider interface.
 *
 * This service manages the lifecycle of language model providers within the VS Code
 * environment. It acts as a factory and registry for different LLM implementations,
 * allowing users to switch between providers (OpenAI, Ollama, LM Studio) via
 * configuration settings.
 *
 * Key features:
 * - Dynamic provider switching based on VS Code configuration
 * - Lazy initialization of language model instances
 * - Comprehensive provider support (OpenAI, Ollama, LM Studio)
 * - Configuration-driven provider selection
 * - Proper error handling for unsupported providers
 * - Integration with VS Code's settings system
 *
 * The provider reads from the 'aiAgent.provider' configuration setting to determine
 * which language model implementation to instantiate. This allows users to switch
 * between different LLM backends without code changes.
 *
 * @class LanguageModelProvider
 * @implements {ILanguageModelProvider}
 *
 * @example
 * ```typescript
 * const provider = new LanguageModelProvider();
 * await provider.initialize(serviceContainer);
 *
 * const model = provider.getActiveModel();
 * const response = await model.generateResponse(prompt, options);
 *
 * // User can switch providers via VS Code settings:
 * // "aiAgent.provider": "ollama" | "openai" | "lmstudio"
 * ```
 */
export class LanguageModelProvider implements ILanguageModelProvider {
    /** The currently active language model instance */
    private activeModel!: ILanguageModel;

    /**
     * Initializes the language model provider with the required service container.
     *
     * This method reads the VS Code configuration to determine which language model
     * provider to instantiate. It supports multiple providers including OpenAI,
     * Ollama, and LM Studio, allowing users to choose their preferred LLM backend.
     *
     * The initialization process:
     * 1. Reads 'aiAgent.provider' configuration setting
     * 2. Instantiates the appropriate language model class
     * 3. Logs the selected provider for debugging and monitoring
     * 4. Stores the instance for later retrieval
     *
     * @param {IServiceContainer} services - The service container providing access to framework services
     * @returns {void}
     *
     * @throws {Error} If an unsupported provider is configured
     *
     * @example
     * ```typescript
     * // During extension activation
     * const provider = new LanguageModelProvider();
     * provider.initialize(serviceContainer);
     *
     * // Configuration in VS Code settings:
     * // {
     * //   "aiAgent.provider": "openai"
     * // }
     * ```
     */
    public initialize(services: IServiceContainer): void {
        // Read provider configuration from VS Code settings
        const providerType = vscode.workspace.getConfiguration('aiAgent').get<string>('provider', 'openai');

        // Instantiate the appropriate language model based on configuration
        switch (providerType.toLowerCase()) {
            case 'openai':
                this.activeModel = new OpenAIModel(services);
                break;
            case 'ollama':
                this.activeModel = new OllamaModel(services);
                break;
            case 'lmstudio':
                this.activeModel = new LMStudioModel(services);
                break;
            default:
                throw new Error(`Unsupported provider: '${providerType}'. Supported providers are: openai, ollama, lmstudio.`);
        }

        // Log the selected provider for debugging and monitoring
        services.logger.info(`Active AI provider: ${providerType}`);
    }

    /**
     * Retrieves the currently active language model instance.
     *
     * This method returns the language model instance that was created during
     * initialization. It ensures that the provider has been properly initialized
     * before allowing access to the model.
     *
     * @returns {ILanguageModel} The active language model instance
     *
     * @throws {Error} If the provider has not been initialized
     *
     * @example
     * ```typescript
     * const model = provider.getActiveModel();
     * const response = await model.generateResponse(prompt, {
     *   cancellationToken,
     *   jsonOutput: false
     * });
     * ```
     */
    public getActiveModel(): ILanguageModel {
        // Ensure the provider has been initialized before allowing access
        if (!this.activeModel) {
            throw new Error('LLM Provider not initialized. Call initialize() first.');
        }

        return this.activeModel;
    }
}

/**
 * VS Code implementation of the IToolUIProvider interface.
 *
 * This service provides user interface interaction capabilities within the VS Code
 * environment, serving as a bridge between tools and VS Code's UI components.
 * It abstracts common UI operations to allow tools to present information,
 * request input, and show progress without directly depending on VS Code APIs.
 *
 * Key features:
 * - Progress indicator support with cancellation capability
 * - Information, warning, and error message display
 * - Integration with VS Code's notification system
 * - Proper error message extraction from Error objects
 * - Non-blocking UI operations for tool execution
 *
 * The provider uses VS Code's built-in UI primitives to ensure consistent
 * user experience and proper integration with the editor environment.
 *
 * @class VSCodeUIProvider
 * @implements {IToolUIProvider}
 *
 * @example
 * ```typescript
 * const uiProvider = new VSCodeUIProvider();
 *
 * // Show progress for long-running operation
 * const result = await uiProvider.showProgress('Processing files...', async (progress, token) => {
 *   progress.report({ message: 'Starting...' });
 *   // Perform work...
 *   return await someAsyncOperation();
 * });
 *
 * // Show various message types
 * uiProvider.showInformationMessage('Operation completed successfully');
 * uiProvider.showWarningMessage('This may take a while');
 * uiProvider.showErrorMessage(new Error('Something went wrong'));
 * ```
 */
export class VSCodeUIProvider implements IToolUIProvider {
    /**
     * Shows a progress indicator in the VS Code UI while executing the provided work.
     *
     * This method displays a cancellable progress notification in VS Code's status
     * bar area and executes the provided work function with progress reporting
     * capabilities. The progress indicator automatically disappears when the
     * work completes or is cancelled.
     *
     * @template T - The type of the result returned by the work function
     * @param {string} title - The title to display in the progress notification
     * @param {(progress: vscode.Progress<{ message?: string }>, token: vscode.CancellationToken) => Thenable<T>} work - The async function to execute with progress tracking
     * @returns {Promise<T>} A promise that resolves with the result of the work function
     *
     * @example
     * ```typescript
     * const files = await uiProvider.showProgress('Scanning workspace...', async (progress, token) => {
     *   progress.report({ message: 'Finding files...' });
     *
     *   if (token.isCancellationRequested) {
     *     throw new OperationCancelledError();
     *   }
     *
     *   const result = await scanWorkspace();
     *   progress.report({ message: 'Complete!' });
     *   return result;
     * });
     * ```
     */
    public async showProgress<T>(
        title: string,
        work: (progress: vscode.Progress<{ message?: string }>, token: vscode.CancellationToken) => Thenable<T>
    ): Promise<T> {
        return vscode.window.withProgress(
            {
                location: vscode.ProgressLocation.Notification,
                title,
                cancellable: true
            },
            (progress, token) => work(progress, token)
        );
    }

    /**
     * Shows an informational message to the user.
     *
     * This method displays a non-intrusive information message to the user
     * through VS Code's notification system. The message appears temporarily
     * and doesn't require user interaction to dismiss.
     *
     * @param {string} message - The message to display
     *
     * @example
     * ```typescript
     * uiProvider.showInformationMessage('File saved successfully');
     * ```
     */
    public showInformationMessage(message: string): void {
        vscode.window.showInformationMessage(message);
    }

    /**
     * Shows a warning message to the user.
     *
     * This method displays a warning message that draws the user's attention
     * to potentially important information. The message is styled to indicate
     * a warning condition.
     *
     * @param {string} message - The warning message to display
     *
     * @example
     * ```typescript
     * uiProvider.showWarningMessage('Large file detected - processing may be slow');
     * ```
     */
    public showWarningMessage(message: string): void {
        vscode.window.showWarningMessage(message);
    }

    /**
     * Shows an error message to the user.
     *
     * This method displays an error message that indicates a problem or failure.
     * If an Error object is provided, it extracts and displays the error message.
     * The message is styled to indicate an error condition.
     *
     * @param {string | Error} message - The error message or Error object to display
     *
     * @example
     * ```typescript
     * try {
     *   await riskyOperation();
     * } catch (error) {
     *   uiProvider.showErrorMessage(error);
     * }
     *
     * // Or with a custom message
     * uiProvider.showErrorMessage('Failed to save file');
     * ```
     */
    public showErrorMessage(message: string | Error): void {
        vscode.window.showErrorMessage(message instanceof Error ? message.message : message);
    }
}

/**
 * VS Code implementation of the ILogger interface using output channels.
 *
 * This logger implementation integrates with VS Code's output channel system,
 * providing a dedicated output window for logging messages. It's designed for
 * development and debugging purposes, allowing users to view detailed logs
 * in a structured format within the VS Code interface.
 *
 * Key features:
 * - Integration with VS Code's output channel system
 * - Structured logging with metadata support
 * - Automatic error stack trace inclusion
 * - Consistent log formatting with level prefixes
 * - Persistent log visibility across sessions
 *
 * The logger creates a named output channel that can be viewed by users through
 * VS Code's "Output" panel, making it ideal for debugging and monitoring tool
 * execution and framework operations.
 *
 * @class VSCodeOutputChannelLogger
 * @implements {ILogger}
 *
 * @example
 * ```typescript
 * const logger = new VSCodeOutputChannelLogger('My Tool');
 *
 * logger.info('Tool initialized successfully');
 * logger.debug('Processing file', { fileName: 'example.txt', size: 1024 });
 * logger.error(new Error('File processing failed'), { fileName: 'example.txt' });
 *
 * // Users can view logs in VS Code's Output panel under "My Tool" channel
 * ```
 */
export class VSCodeOutputChannelLogger implements ILogger {
    /** The VS Code output channel used for logging */
    private channel: OutputChannel;

    /**
     * Creates a new VS Code output channel logger instance.
     *
     * This constructor creates a dedicated output channel in VS Code where all
     * log messages will be displayed. The channel name is used to identify the
     * log source in the Output panel.
     *
     * @param {string} name - The name of the output channel (displayed in VS Code's Output panel)
     *
     * @example
     * ```typescript
     * const logger = new VSCodeOutputChannelLogger('AI Agent Tools');
     * ```
     */
    constructor(name: string) {
        this.channel = vscode.window.createOutputChannel(name);
    }

    /**
     * Logs a debug-level message with optional metadata.
     *
     * Debug messages are intended for detailed diagnostic information during
     * development and troubleshooting. They provide insights into the internal
     * workings of tools and framework components.
     *
     * @param {string} message - The debug message to log
     * @param {object} [metadata] - Optional additional data to include with the log
     *
     * @example
     * ```typescript
     * logger.debug('Processing started', {
     *   fileName: 'config.json',
     *   operation: 'validation',
     *   timestamp: Date.now()
     * });
     * ```
     */
    public debug(message: string, metadata?: object): void {
        this.log('DEBUG', message, metadata);
    }

    /**
     * Logs an informational message with optional metadata.
     *
     * Info messages provide general operational information about tool execution,
     * successful operations, and important state changes.
     *
     * @param {string} message - The informational message to log
     * @param {object} [metadata] - Optional additional data to include with the log
     *
     * @example
     * ```typescript
     * logger.info('File system tool initialized successfully');
     * logger.info('Processing completed', { filesProcessed: 15, duration: 250 });
     * ```
     */
    public info(message: string, metadata?: object): void {
        this.log('INFO', message, metadata);
    }

    /**
     * Logs a warning message with optional metadata.
     *
     * Warning messages indicate potentially harmful situations or important
     * conditions that should be reviewed but don't prevent operation.
     *
     * @param {string} message - The warning message to log
     * @param {object} [metadata] - Optional additional data to include with the log
     *
     * @example
     * ```typescript
     * logger.warn('Large file detected - processing may be slow', {
     *   fileSize: '2.5GB',
     *   recommendedAction: 'Consider chunking'
     * });
     * ```
     */
    public warn(message: string, metadata?: object): void {
        this.log('WARN', message, metadata);
    }

    /**
     * Logs an error message or Error object with optional metadata.
     *
     * Error messages indicate error conditions that need attention. If an Error
     * object is provided, the logger automatically includes the stack trace
     * for debugging purposes.
     *
     * @param {string | Error} message - The error message or Error object to log
     * @param {object} [metadata] - Optional additional data to include with the log
     *
     * @example
     * ```typescript
     * try {
     *   await riskyOperation();
     * } catch (error) {
     *   logger.error(error, { operation: 'fileProcessing', fileName: 'data.txt' });
     * }
     *
     * // Or with a custom message
     * logger.error('Failed to connect to external service');
     * ```
     */
    public error(message: string | Error, metadata?: object): void {
        this.log('ERROR', message instanceof Error ? message.message : message,
            message instanceof Error ? { ...metadata, stack: message.stack } : metadata);
    }

    /**
     * Internal method that formats and writes log messages to the output channel.
     *
     * This private method handles the common formatting logic for all log levels,
     * ensuring consistent output format and proper metadata serialization.
     *
     * @private
     * @param {string} level - The log level (DEBUG, INFO, WARN, ERROR)
     * @param {string} message - The log message
     * @param {object} [metadata] - Optional metadata to include
     *
     * @example
     * ```typescript
     * // Internal usage
     * this.log('INFO', 'Operation completed', { duration: 150 });
     * // Output: [INFO] Operation completed | {"duration":150}
     * ```
     */
    private log(level: string, message: string, metadata?: object): void {
        this.channel.appendLine(`[${level}] ${message}${metadata ? ` | ${JSON.stringify(metadata)}` : ''}`);
    }
}

/**
 * VS Code implementation of the IFileSystemManager interface.
 *
 * This service provides secure, sandboxed file system operations within the VS Code
 * workspace environment. It ensures that all file operations are confined to the
 * workspace boundaries and provides automatic path resolution and validation.
 *
 * Key features:
 * - Workspace-bound file operations (cannot access files outside workspace)
 * - Automatic path resolution relative to workspace root
 * - Path traversal attack prevention
 * - Support for both string and Buffer content
 * - Automatic directory creation for file writes
 * - UTF-8 encoding for text file operations
 * - Comprehensive error handling and validation
 *
 * The service automatically resolves relative paths against the workspace root
 * and validates that all operations stay within the workspace boundaries.
 * This prevents path traversal attacks and ensures data integrity.
 *
 * @class VSCodeFileSystemManager
 * @implements {IFileSystemManager}
 *
 * @example
 * ```typescript
 * const fsManager = new VSCodeFileSystemManager();
 *
 * // Read a configuration file
 * const config = await fsManager.readFile('config/settings.json');
 *
 * // Write generated content
 * await fsManager.writeFile('dist/output.js', generatedCode);
 *
 * // Create nested directories automatically
 * await fsManager.createDirectory('src/components/utils');
 *
 * // Delete temporary files
 * await fsManager.deleteFile('temp/cache.json');
 * ```
 */
export class VSCodeFileSystemManager implements IFileSystemManager {
    /**
     * Creates a patch file showing differences between old and new content
     * @param relativePath The path to the file being patched (for context in the patch file)
     * @param oldContent The original content
     * @param newContent The new content
     * @param outputPath Where to save the patch file
     * @param options Diff generation options
     * @returns A promise that resolves to the path of the created patch file
     */
    async createPatchFile(
        relativePath: string,
        oldContent: string | Buffer,
        newContent: string | Buffer,
        outputPath: string,
        options?: DiffOptions
    ): Promise<void> {
        const diff = await this.diffContent(
            oldContent,
            newContent,
            { ...options, format: DiffFormat.Unified }
        );

        await this.writeFile(outputPath, diff);
    }

    /**
     * Applies a patch file to the workspace
     * @param relativePath The path to the file to patch (relative to workspace root)
     * @param patchPath The path to the patch file to apply
     * @param options Options for applying the patch
     * @returns The result of the patch application
     */
    async applyPatchFile(
        relativePath: string,
        patchPath: string,
        options?: FileEditOptions
    ): Promise<{ success: boolean; conflicts: string[] }> {
        const patchContent = await this.readFile(patchPath);
        return this.applyDiff(relativePath, patchContent, options);
    }

    /** The absolute path to the workspace root directory */
    private readonly workspaceRoot: string;

    /**
     * Creates a new VS Code file system manager instance.
     *
     * This constructor initializes the workspace root path and validates that
     * a workspace is currently open. All file operations will be relative to
     * this workspace root.
     *
     * @throws {Error} If no workspace folder is currently open in VS Code
     *
     * @example
     * ```typescript
     * // Will succeed if a workspace is open
     * const fsManager = new VSCodeFileSystemManager();
     *
     * // Will throw if no workspace is open
     * // const fsManager = new VSCodeFileSystemManager(); // Error!
     * ```
     */
    constructor() {
        const root = vscode.workspace.workspaceFolders?.[0];
        if (!root) {
            throw new Error('No workspace folder open.');
        }
        this.workspaceRoot = root.uri.fsPath;
    }

    /**
     * Resolves and validates a relative path against the workspace root.
     *
     * This private method performs critical security validation by ensuring
     * that the resolved absolute path is within the workspace boundaries.
     * It prevents path traversal attacks and ensures data integrity.
     *
     * @private
     * @param {string} relativePath - The relative path to resolve and validate
     * @returns {string} The validated absolute path
     * @throws {Error} If the resolved path would be outside the workspace
     *
     * @example
     * ```typescript
     * // Safe path resolution
     * const safePath = this._resolveAndValidatePath('src/utils/helpers.ts');
     * // Result: '/workspace/src/utils/helpers.ts'
     *
     * // Path traversal attempt (will throw)
     * // const blockedPath = this._resolveAndValidatePath('../../../etc/passwd');
     * // Error: Path traversal denied.
     * ```
     */
    private resolveAndValidatePath(relativePath: string): string {
        const absolutePath = path.resolve(this.workspaceRoot, relativePath);

        // Security check: ensure the path is within workspace boundaries
        if (!absolutePath.startsWith(this.workspaceRoot)) {
            throw new Error('Path traversal denied.');
        }

        return absolutePath;
    }

    /**
     * Reads the contents of a file as a UTF-8 encoded string.
     *
     * This method reads the complete contents of a file and returns it as a string.
     * The file path is resolved relative to the workspace root and validated
     * for security.
     *
     * @param {string} relativePath - The path to the file relative to the workspace root
     * @returns {Promise<string>} A promise that resolves with the file contents as a UTF-8 string
     * @throws {Error} If the file doesn't exist, cannot be read, or is outside the workspace
     *
     * @example
     * ```typescript
     * // Read a TypeScript configuration file
     * const tsConfig = await fsManager.readFile('tsconfig.json');
     * console.log('TypeScript config:', tsConfig);
     *
     * // Read a source code file
     * const sourceCode = await fsManager.readFile('src/main.ts');
     * ```
     */
    public async readFile(relativePath: string): Promise<string> {
        const absolutePath = this.resolveAndValidatePath(relativePath);
        const uri = vscode.Uri.file(absolutePath);

        try {
            // First try to get the document if it's already open in the editor
            const document = vscode.workspace.textDocuments.find(
                doc => doc.uri.fsPath === absolutePath
            );

            if (document) {
                return document.getText();
            }

            // If not open, open it as a text document
            const textDocument = await vscode.workspace.openTextDocument(uri);
            return textDocument.getText();
        } catch (error) {
            // Fall back to direct file system read if document can't be opened
            return fs.readFile(absolutePath, 'utf-8');
        }
    }

    /**
     * Writes content to a file, creating it if it doesn't exist or overwriting if it does.
     *
     * This method writes the provided content to a file. If the file doesn't exist,
     * it will be created. If the parent directories don't exist, they will be
     * created automatically. The content can be either a string or Buffer.
     *
     * @param {string} relativePath - The path to the file relative to the workspace root
     * @param {string | Buffer} content - The content to write to the file
     * @returns {Promise<void>} A promise that resolves when the write operation is complete
     * @throws {Error} If the file cannot be written or is outside the workspace
     *
     * @example
     * ```typescript
     * // Write a configuration file
     * await fsManager.writeFile('config/app.json', JSON.stringify(config, null, 2));
     *
     * // Write binary content
     * const buffer = Buffer.from('binary data');
     * await fsManager.writeFile('assets/image.png', buffer);
     *
     * // Create nested directories automatically
     * await fsManager.writeFile('deep/nested/path/file.txt', 'content');
     * ```
     */
    public async writeFile(relativePath: string, content: string | Buffer): Promise<void> {
        const absolutePath = this.resolveAndValidatePath(relativePath);
        const uri = vscode.Uri.file(absolutePath);

        // Create parent directories if they don't exist
        await fs.mkdir(path.dirname(absolutePath), { recursive: true });

        // Convert Buffer to string if needed
        const contentStr = Buffer.isBuffer(content) ? content.toString('utf-8') : content;

        // Check if the document is already open
        const document = vscode.workspace.textDocuments.find(
            doc => doc.uri.fsPath === absolutePath
        );

        if (document && !document.isClosed) {
            // If document is open, use workspace edit to update it
            const edit = new vscode.WorkspaceEdit();
            const fullRange = new vscode.Range(
                document.positionAt(0),
                document.positionAt(document.getText().length)
            );
            edit.replace(uri, fullRange, contentStr);
            await vscode.workspace.applyEdit(edit);

            // Save the document if it's dirty
            if (document.isDirty) {
                await document.save();
            }
        } else {
            // Fall back to direct file system write
            await fs.writeFile(absolutePath, content);

            // If the file is open in any editor, ensure it's reloaded
            const editor = vscode.window.visibleTextEditors.find(
                e => e.document.uri.fsPath === absolutePath
            );
            if (editor) {
                await vscode.commands.executeCommand('workbench.action.files.revert', editor.document.uri);
            }
        }
    }

    /**
     * Deletes a file from the workspace.
     *
     * This method removes a file from the file system. The file path is resolved
     * relative to the workspace root and validated for security before deletion.
     *
     * @param {string} relativePath - The path to the file relative to the workspace root
     * @returns {Promise<void>} A promise that resolves when the file is deleted
     * @throws {Error} If the file doesn't exist, cannot be deleted, or is outside the workspace
     *
     * @example
     * ```typescript
     * // Delete a temporary file
     * await fsManager.deleteFile('temp/cache.json');
     *
     * // Delete a generated file
     * await fsManager.deleteFile('dist/bundle.js');
     * ```
     */
    public async deleteFile(relativePath: string): Promise<void> {
        const absolutePath = this.resolveAndValidatePath(relativePath);
        const uri = vscode.Uri.file(absolutePath);

        // Check if the document is open in any editor
        const document = vscode.workspace.textDocuments.find(
            doc => doc.uri.fsPath === absolutePath
        );

        if (uri) {
            workflowManager.logger?.debug?.(`Processing file deletion for ${uri.fsPath}`);
        }

        // If document is open, close it first
        if (document) {
            // Find all editors with this document
            const editors = vscode.window.visibleTextEditors.filter(
                editor => editor.document.uri.fsPath === absolutePath
            );

            // Close all editors showing this document
            for (const editor of editors) {
                await vscode.window.showTextDocument(editor.document, { preview: false });
                await vscode.commands.executeCommand('workbench.action.closeActiveEditor');
            }
        }

        // Now delete the file
        await fs.unlink(absolutePath);

        // Notify VS Code that the file was deleted
        // VS Code automatically handles file system events, so we don't need to manually fire them
        // The file system watcher will pick up the change and notify any listeners
    }

    /**
     * Creates a directory and any necessary parent directories.
     *
     * This method creates a directory at the specified path. If parent directories
     * don't exist, they will be created automatically. The path is resolved
     * relative to the workspace root and validated for security.
     *
     * @param {string} relativePath - The path to the directory relative to the workspace root
     * @returns {Promise<void>} A promise that resolves when the directory is created
     * @throws {Error} If the directory cannot be created or is outside the workspace
     *
     * @example
     * ```typescript
     * // Create a simple directory
     * await fsManager.createDirectory('logs');
     *
     * // Create nested directories
     * await fsManager.createDirectory('src/components/buttons');
     *
     * // Create deep directory structure
     * await fsManager.createDirectory('app/modules/user/authentication');
     * ```
     */
    public async createDirectory(relativePath: string): Promise<void> {
        const absolutePath = this.resolveAndValidatePath(relativePath);
        await fs.mkdir(absolutePath, { recursive: true });
    }

    public async appendToFile(
        relativePath: string,
        content: string | Buffer,
        options: FileAppendOptions & FileEditOptions & { viewColumn?: ViewColumn } = {}
    ): Promise<void> {
        const absolutePath = this.resolveAndValidatePath(relativePath);
        const uri = vscode.Uri.file(absolutePath);
        const contentStr = Buffer.isBuffer(content) ? content.toString('utf-8') : content;

        // Create a backup if requested
        if (options.backup) {
            const backupPath = `${absolutePath}${options.backupSuffix || '.bak'}`;
            await fs.copyFile(absolutePath, backupPath);
        }

        // Check if the document is already open
        let document = vscode.workspace.textDocuments.find(
            doc => doc.uri.fsPath === absolutePath
        );

        let currentContent = '';

        if (document) {
            // Get content from open document
            currentContent = document.getText();
        } else {
            // Read from file system if not open
            try {
                currentContent = await fs.readFile(absolutePath, 'utf-8');
            } catch (error: unknown) {
                const nodeError = error as NodeJS.ErrnoException;
                if (nodeError.code !== 'ENOENT' || !options.createIfNotExists) {
                    throw error;
                }
            }

            // Open the document if we need to show it
            if (options.viewColumn) {
                const showOptions: TextDocumentShowOptions = {
                    viewColumn: options.viewColumn,
                    preserveFocus: options.preserveFocus,
                    preview: options.preview
                };

                document = await vscode.workspace.openTextDocument(uri);
                const editor = await vscode.window.showTextDocument(document, showOptions);

                // Reveal the end of the document where we'll append
                const lastLine = document.lineCount - 1;
                const endPos = new vscode.Position(lastLine, document.lineAt(lastLine).text.length);
                editor.revealRange(
                    new vscode.Range(endPos, endPos),
                    vscode.TextEditorRevealType.Default
                );
            }
        }

        // Handle separator for appending
        const separator = options.separator || '\n';
        const newContent = currentContent
            ? currentContent + separator + contentStr
            : contentStr;

        // Write the updated content using the existing writeFile method
        await this.writeFile(relativePath, newContent);

        // If we have an open document and it's visible, scroll to the end
        if (document) {
            const editor = vscode.window.visibleTextEditors.find(
                e => e.document.uri.fsPath === absolutePath
            );

            if (editor) {
                const lastLine = editor.document.lineCount - 1;
                const endPos = new vscode.Position(lastLine, editor.document.lineAt(lastLine).text.length);

                // Scroll to show the appended content
                editor.revealRange(
                    new vscode.Range(endPos, endPos),
                    vscode.TextEditorRevealType.InCenterIfOutsideViewport
                );

                // Move cursor to the end of the appended content
                editor.selection = new vscode.Selection(endPos, endPos);
            }
        }
    }

    public async findInFile(
        relativePath: string,
        searchPattern: string | RegExp,
        options: FileSearchOptions = {}
    ): Promise<ContentMatch[]> {
        const absolutePath = this.resolveAndValidatePath(relativePath);
        const content = await fs.readFile(absolutePath, 'utf-8');
        const lines = content.split('\n');
        const matches: ContentMatch[] = [];
        const regex = searchPattern instanceof RegExp
            ? searchPattern
            : new RegExp(
                options.useRegex ? searchPattern : searchPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
                options.caseSensitive ? 'g' : 'gi'
            );

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            let match;
            while ((match = regex.exec(line)) !== null) {
                matches.push({
                    lineNumber: i + 1,
                    lineContent: line,
                    match: match[0],
                    position: [match.index + 1, match.index + match[0].length]
                });

                // Prevent infinite loops for zero-length matches
                if (match.index === regex.lastIndex) {
                    regex.lastIndex++;
                }
            }
        }

        return matches;
    }

    public async replaceInFile(
        relativePath: string,
        searchPattern: string | RegExp,
        replacement: string | ((match: string, ...groups: string[]) => string),
        options: FileSearchOptions & FileEditOptions = {}
    ): Promise<number> {
        const absolutePath = this.resolveAndValidatePath(relativePath);
        const content = await fs.readFile(absolutePath, 'utf-8');

        // Create a backup if requested
        if (options.backup) {
            const backupPath = `${absolutePath}${options.backupSuffix || '.bak'}`;
            await fs.copyFile(absolutePath, backupPath);
        }

        const regex = searchPattern instanceof RegExp
            ? searchPattern
            : new RegExp(
                options.useRegex ? searchPattern : searchPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
                options.caseSensitive ? 'g' : 'gi'
            );

        let replaceCount = 0;
        const newContent = content.replace(regex, (...args) => {
            replaceCount++;
            return typeof replacement === 'function'
                ? replacement(...args)
                : replacement;
        });

        if (replaceCount > 0) {
            await this.writeFile(relativePath, newContent);
        }

        return replaceCount;
    }

    public async insertAtLine(
        relativePath: string,
        lineNumber: number,
        content: string,
        options: FileEditOptions = {}
    ): Promise<void> {
        const absolutePath = this.resolveAndValidatePath(relativePath);
        let fileContent = '';

        try {
            fileContent = await fs.readFile(absolutePath, 'utf-8');
        } catch (error: unknown) {
            const nodeError = error as NodeJS.ErrnoException;
            if (nodeError.code !== 'ENOENT' || !options.createIfNotExists) {
                throw error;
            }
        }

        const lines = fileContent.split('\n');
        const insertAt = Math.max(0, Math.min(lineNumber - 1, lines.length));
        lines.splice(insertAt, 0, content);

        await this.writeFile(relativePath, lines.join('\n'));
    }

    public async updateFileLines(
        relativePath: string,
        predicate: (line: string, lineNumber: number) => string | null | undefined,
        options: FileEditOptions = {}
    ): Promise<number> {
        if (options) {
            workflowManager.logger?.info?.(`Updating file lines with options: ${JSON.stringify(options)}`);
        }
        const absolutePath = this.resolveAndValidatePath(relativePath);
        const content = await fs.readFile(absolutePath, 'utf-8');
        const lines = content.split('\n');
        let modifiedCount = 0;
        const newLines: string[] = [];

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];
            const result = predicate(line, i + 1);

            if (result === undefined) {
                // Skip this line (remove it)
                modifiedCount++;
            } else if (result !== null) {
                // Replace the line
                newLines.push(result);
                if (result !== line) {
                    modifiedCount++;
                }
            } else {
                // Keep the line as is
                newLines.push(line);
            }
        }

        if (modifiedCount > 0) {
            await this.writeFile(relativePath, newLines.join('\n'));
        }

        return modifiedCount;
    }

    /**
     * Gets a diff between the current file content and a new version.
     * 
     * @param {string} relativePath - The path to the file
     * @param {string | Buffer} newContent - The new content to compare against
     * @param {DiffOptions} [options] - Options for the diff generation
     * @returns {Promise<string>} A promise that resolves with a diff string in the specified format
     * @throws {Error} If the file cannot be read or is outside the workspace
     */
    public async getFileDiff(
        relativePath: string,
        newContent: string | Buffer,
        options: DiffOptions = {}
    ): Promise<string> {
        const absolutePath = this.resolveAndValidatePath(relativePath);
        workflowManager.logger?.debug?.(`Generating diff for file at ${absolutePath}`);
        const oldContent = await this.readFile(relativePath);

        return this.diffContent(oldContent, newContent, options);
    }

    /**
     * Gets a diff between two versions of content.
     * 
     * @param {string | Buffer} oldContent - The old content to compare from
     * @param {string | Buffer} newContent - The new content to compare to
     * @param {DiffOptions} [options] - Options for the diff generation
     * @returns {Promise<string>} A promise that resolves with a diff string in the specified format
     */
    public async diffContent(
        oldContent: string | Buffer,
        newContent: string | Buffer,
        options: DiffOptions = {}
    ): Promise<string> {
        // Convert Buffer to string if needed
        const oldContentStr = Buffer.isBuffer(oldContent) ? oldContent.toString('utf-8') : oldContent;
        const newContentStr = Buffer.isBuffer(newContent) ? newContent.toString('utf-8') : newContent;

        // If showInEditor is true, show the diff in VSCode's diff editor
        if (options.showInEditor !== false && vscode.window) {
            try {
                const title = options.title || 'Compare Changes';
                const diffId = `codessa-diff-${Date.now()}`;

                // Create a unique identifier for this diff session
                const leftUri = vscode.Uri.parse(`codessa-diff:${diffId}/original`).with({
                    scheme: 'untitled',
                    path: `${title} (Original)`
                });

                const rightUri = vscode.Uri.parse(`codessa-diff:${diffId}/modified`).with({
                    scheme: 'untitled',
                    path: `${title} (Modified)`
                });

                // Create and show the diff editor
                await vscode.commands.executeCommand(
                    'vscode.diff',
                    leftUri,
                    rightUri,
                    title,
                    {
                        preview: true,
                        viewColumn: options.viewColumn || vscode.ViewColumn.Beside,
                        preserveFocus: options.preserveFocus,
                        selection: options.selection,
                        revealIfVisible: true
                    },
                    {
                        isReadOnly: true,
                        label: title,
                        description: 'Codessa Diff Viewer',
                        detail: 'Interactive diff viewer for code changes'
                    }
                );

                // Get the active text editor (should be the diff editor)
                const activeEditor = vscode.window.activeTextEditor;
                if (activeEditor) {
                    // Apply content to the left and right sides of the diff
                    const edit = new vscode.WorkspaceEdit();

                    // Clear any existing content
                    const fullRange = (doc: vscode.TextDocument) =>
                        new vscode.Range(
                            doc.positionAt(0),
                            doc.positionAt(doc.getText().length)
                        );

                    // Open the documents if they're not already open
                    const [leftDoc, rightDoc] = await Promise.all([
                        vscode.workspace.openTextDocument(leftUri),
                        vscode.workspace.openTextDocument(rightUri)
                    ]);

                    // Replace entire content in a single edit
                    edit.replace(leftUri, fullRange(leftDoc), oldContentStr);
                    edit.replace(rightUri, fullRange(rightDoc), newContentStr);

                    // Apply the edits
                    await vscode.workspace.applyEdit(edit);

                    // Save the documents to ensure changes are preserved
                    await Promise.all([
                        leftDoc.save(),
                        rightDoc.save()
                    ]);

                    // Set language mode if available
                    if (options.languageId) {
                        await vscode.languages.setTextDocumentLanguage(leftDoc, options.languageId);
                        await vscode.languages.setTextDocumentLanguage(rightDoc, options.languageId);
                    }
                }

                // Return empty string since we're showing the diff in the editor
                return '';
            } catch (error) {
                console.error('Error showing diff in editor:', error);
                // Fall through to text-based diff on error
            }
        }

        // Import the 'diff' module
        const diffModule = await import('diff');
        
        // Use create patch with standard options
        const diffResult = diffModule.default.createPatch(
            'original', // original filename
            'modified', // modified filename
            oldContentStr,
            newContentStr
        );

        // Return the diff result based on the requested format
        switch (options.format || DiffFormat.Unified) {
            case DiffFormat.Json:
                return JSON.stringify({
                    oldContent: oldContentStr,
                    newContent: newContentStr,
                    patch: diffResult
                }, null, 2);
            case DiffFormat.Unified:
            default:
                return diffResult;
        }
    }

    /**
     * Applies a diff to a file.
     * 
     * @param {string} relativePath - The path to the file to patch
     * @param {string} diff - The diff to apply (format is auto-detected)
     * @param {FileEditOptions} [options] - Options for the patch operation
     * @returns {Promise<{success: boolean, conflicts: string[]}>} A promise that resolves with the result of the patch operation
     * @throws {Error} If the patch cannot be applied or the file is outside the workspace
     */
    public async applyDiff(
        relativePath: string,
        diff: string,
        options: FileEditOptions & { preview?: boolean } = {}
    ): Promise<{ success: boolean; conflicts: string[] }> {
        const absolutePath = this.resolveAndValidatePath(relativePath);
        const conflicts: string[] = [];

        // Get current file content
        let currentContent: string;
        try {
            currentContent = await this.readFile(relativePath);
        } catch (error) {
            throw new Error(`Failed to read file ${relativePath}: ${error instanceof Error ? error.message : String(error)}`);
        }

        // Import diff module
        const diffUtils = await import('diff');

        // Apply the diff to get the new content
        let patched: string | false;
        try {
            // Apply patch with basic options
            patched = diffUtils.applyPatch(currentContent, diff);

            if (patched === false) {
                throw new Error('Failed to apply diff: patch not applicable');
            }
        } catch (error) {
            conflicts.push(`Failed to apply diff: ${error instanceof Error ? error.message : String(error)}`);
            return { success: false, conflicts };
        }

        // If preview is requested, show the diff before applying
        if (options.preview && vscode.window) {
            try {
                const originalUri = vscode.Uri.file(absolutePath);
                const modifiedUri = originalUri.with({ scheme: 'untitled', query: 'modified' });

                // Create a new untitled document for the modified content
                const document = await vscode.workspace.openTextDocument(
                    modifiedUri.with({ scheme: 'untitled' })
                );

                // Set the content and language mode
                const edit = new vscode.WorkspaceEdit();
                edit.insert(modifiedUri, new vscode.Position(0, 0), patched);
                await vscode.workspace.applyEdit(edit);

                // Get the language ID for syntax highlighting
                const languageId = await vscode.workspace.openTextDocument(originalUri).then(
                    doc => doc.languageId,
                    () => undefined
                );

                if (languageId) {
                    await vscode.languages.setTextDocumentLanguage(document, languageId);
                }

                // Show the diff
                await vscode.commands.executeCommand(
                    'vscode.diff',
                    originalUri,
                    modifiedUri,
                    `Preview changes: ${path.basename(relativePath)}`,
                    { preview: false }
                );

                // Ask for confirmation before applying
                const confirm = await vscode.window.showInformationMessage(
                    'Do you want to apply these changes?',
                    {
                        modal: true,
                        detail: `This will modify ${path.basename(relativePath)}.`
                    },
                    'Apply Changes',
                    'Cancel'
                );

                if (confirm !== 'Apply Changes') {
                    return { success: false, conflicts: ['User cancelled the operation'] };
                }
            } catch (error) {
                // If preview fails, log but continue with the operation
                console.warn('Failed to show diff preview:', error);
            }
        }

        // Apply the changes
        try {
            // Write the file with the patched content
            await this.writeFile(relativePath, patched);

            // Reveal the file in the explorer and show success message
            if (vscode.window) {
                const uri = vscode.Uri.file(absolutePath);
                await vscode.commands.executeCommand('revealInExplorer', uri);

                // Show a success message if not in silent mode
                if (!options.silent) {
                    vscode.window.showInformationMessage(
                        `Successfully updated ${path.basename(relativePath)}`,
                        'Open File'
                    ).then(choice => {
                        if (choice === 'Open File') {
                            vscode.window.showTextDocument(uri);
                        }
                    });
                }
            }

            return { success: true, conflicts: [] };
        } catch (error) {
            throw new Error(`Failed to write file ${relativePath}: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}

/**
 * VS Code implementation of the IWorkspaceKnowledge interface.
 *
 * This service provides tools with a high-level understanding of the
 * workspace structure, enabling intelligent file discovery and content analysis.
 *
 * Key features:
 * - Hierarchical directory tree generation with customizable ignore patterns
 * - File pattern matching using VS Code's powerful search capabilities
 * - Efficient file content retrieval with workspace-relative paths
 * - Built-in ignore patterns for common directories (.git, node_modules, dist, etc.)
 * - Recursive directory traversal with proper path handling
 * - Integration with VS Code workspace APIs for optimal performance
 *
 * @class VSCodeWorkspaceKnowledge
 * @implements {IWorkspaceKnowledge}
 *
 * @example
 * ```typescript
 * const workspaceKnowledge = new VSCodeWorkspaceKnowledge();
 *
 * // Get the complete workspace structure
 * const tree = await workspaceKnowledge.getDirectoryTree({
 *   ignorePatterns: ['**\/node_modules/**', '**\/.git/**']
 * });
 *
 * // Find all TypeScript files
 * const tsFiles = await workspaceKnowledge.findFilesByPattern('**\/*.ts');
 *
 * // Read specific file content
 * const configContent = await workspaceKnowledge.getFileContent('tsconfig.json');
 * ```
 */
export class VSCodeWorkspaceKnowledge implements IWorkspaceKnowledge {
    /** Default set of directory patterns to ignore during tree building */
    private readonly defaultIgnore = new Set(['.git', 'node_modules', 'dist', 'build', '.vscode', '__pycache__']);

    /** The root path of the workspace */
    private readonly workspaceRoot: string;

    constructor() {
        const root = vscode.workspace.workspaceFolders?.[0];
        if (!root) {
            throw new Error('No workspace folder open.');
        }
        this.workspaceRoot = root.uri.fsPath;
    }

    /**
     * Resolves and validates a path relative to the workspace root.
     * Ensures the resolved path is within the workspace boundaries to prevent path traversal attacks.
     * 
     * @private
     * @param {string} relativePath - The path to resolve and validate
     * @returns {string} The resolved absolute path
     * @throws {Error} If the path is outside the workspace boundaries
     */
    private _resolveAndValidatePath(relativePath: string): string {
        // Normalize the path to handle . and .. segments
        const normalizedPath = path.normalize(relativePath);

        // Resolve to absolute path
        const absolutePath = path.resolve(this.workspaceRoot, normalizedPath);

        // Verify the path is within the workspace
        if (!absolutePath.startsWith(this.workspaceRoot)) {
            throw new Error(`Path "${relativePath}" resolves to outside the workspace.`);
        }

        return absolutePath;
    }

    /**
     * Writes content to a file in the workspace.
     * @param relativePath The path to the file relative to the workspace root
     * @param content The content to write to the file
     * @returns A promise that resolves when the file is written
     * @throws {Error} If the file cannot be written or is outside the workspace
     */
    public async writeFile(relativePath: string, content: string | Buffer): Promise<void> {
        const absolutePath = this._resolveAndValidatePath(relativePath);

        // Ensure the directory exists
        const dir = path.dirname(absolutePath);
        await fs.mkdir(dir, { recursive: true });

        // Write the file
        await fs.writeFile(absolutePath, content);
    }

    /**
     * Reads the content of a file in the workspace.
     * @param relativePath The path to the file relative to the workspace root
     * @returns A promise that resolves with the file content as a string
     */
    public async readFile(relativePath: string): Promise<string> {
        const absolutePath = this._resolveAndValidatePath(relativePath);
        return fs.readFile(absolutePath, 'utf8');
    }

    /**
     * Retrieves a hierarchical representation of the workspace directory structure.
     *
     * This method builds a complete tree structure of the workspace, starting from
     * the root folder. It recursively traverses all directories and files, while
     * respecting ignore patterns to avoid including irrelevant or large directories.
     *
     * The tree structure provides:
     * - Relative paths from workspace root
     * - Proper type discrimination between files and directories
     * - Hierarchical organization reflecting the actual file system structure
     * - Efficient traversal capabilities for tools and services
     *
     * @param {Object} [options] - Configuration options for directory tree generation
     * @param {string[]} [options.ignorePatterns] - Additional glob patterns to exclude from the tree
     * @returns {Promise<DirectoryTree | null>} A promise that resolves to the directory tree or null if no workspace is open
     * @throws {Error} If there's an error accessing the file system during traversal
     *
     * @example
     * ```typescript
     * // Get workspace tree ignoring common directories
     * const tree = await workspaceKnowledge.getDirectoryTree({
     *   ignorePatterns: ['**\/node_modules/**', '**\/.git/**', '**\/dist/**']
     * });
     *
     * if (tree) {
     *   console.log('Workspace root:', tree.name);
     *   console.log('Total items:', tree.children.length);
     * }
     *
     * // Get complete tree including all files
     * const fullTree = await workspaceKnowledge.getDirectoryTree();
     * ```
     */
    public async getDirectoryTree(options?: { ignorePatterns?: string[] }): Promise<DirectoryTree | null> {
        const rootFolder = vscode.workspace.workspaceFolders?.[0];
        if (!rootFolder) {
            return null;
        }

        // Combine default ignore patterns with user-specified patterns
        const ignoreSet = new Set([...this.defaultIgnore, ...(options?.ignorePatterns ?? [])]);

        /**
         * Recursively builds the directory tree structure.
         *
         * This internal function performs the actual tree building by:
         * 1. Reading directory entries with file type information
         * 2. Filtering out ignored directories and files
         * 3. Recursively processing subdirectories
         * 4. Constructing proper tree nodes with relative paths
         *
         * @param {string} dirPath - The absolute path of the directory to process
         * @returns {Promise<DirectoryTree>} A promise that resolves to the directory tree node
         */
        const buildTree = async (dirPath: string): Promise<DirectoryTree> => {
            // Read directory entries with file type information
            const entries = await fs.readdir(dirPath, { withFileTypes: true });
            const children: (DirectoryTree | { path: string; name: string; type: 'file' })[] = [];

            // Process each entry in the directory
            for (const entry of entries) {
                // Skip ignored entries
                if (ignoreSet.has(entry.name)) {
                    continue;
                }

                const fullPath = path.join(dirPath, entry.name);
                const relPath = path.relative(rootFolder.uri.fsPath, fullPath);

                if (entry.isDirectory()) {
                    // Recursively build subtree for directories
                    children.push(await buildTree(fullPath));
                } else {
                    // Create file entry for regular files
                    children.push({
                        path: relPath,
                        name: entry.name,
                        type: 'file'
                    });
                }
            }

            // Return the directory tree node
            return {
                path: path.relative(rootFolder.uri.fsPath, dirPath),
                name: path.basename(dirPath),
                children,
                type: 'directory'
            };
        };

        // Start building the tree from the workspace root
        return buildTree(rootFolder.uri.fsPath);
    }

    /**
     * Finds files in the workspace matching the given glob pattern.
     *
     * This method leverages VS Code's powerful file search capabilities to find
     * files that match the specified glob pattern. It supports all standard glob
     * patterns and provides efficient searching across the entire workspace.
     *
     * Common pattern examples:
     * - `**\/*.ts` - All TypeScript files in any subdirectory
     * - `src/**` - All files in the src directory and subdirectories
     * - `**\/*.{js,ts}` - All JavaScript and TypeScript files
     * - `!**\/node_modules/**` - Exclude node_modules (when supported)
     *
     * @param {string} globPattern - The glob pattern to match files against
     * @returns {Promise<Uri[]>} A promise that resolves to an array of file URIs that match the pattern
     * @throws {Error} If the pattern is invalid or if there's an error searching
     *
     * @example
     * ```typescript
     * // Find all TypeScript files
     * const tsFiles = await workspaceKnowledge.findFilesByPattern('**\/*.ts');
     * console.log(`Found ${tsFiles.length} TypeScript files`);
     *
     * // Find configuration files
     * const configFiles = await workspaceKnowledge.findFilesByPattern('**\/*.{json,yml,yaml}');
     *
     * // Find files in specific directory
     * const srcFiles = await workspaceKnowledge.findFilesByPattern('src/**');
     * ```
     */
    public async findFilesByPattern(globPattern: string): Promise<Uri[]> {
        return vscode.workspace.findFiles(globPattern);
    }

    /**
     * Reads the content of a file in the workspace.
     *
     * This method provides convenient access to file contents using workspace-relative
     * paths. It internally uses the VSCodeFileSystemManager to handle path resolution,
     * validation, and secure file access.
     *
     * @param {string} relativePath - The path to the file relative to the workspace root
     * @returns {Promise<string>} A promise that resolves to the file content as a UTF-8 string
     * @throws {Error} If the file doesn't exist, cannot be read, or is outside the workspace
     *
     * @example
     * ```typescript
     * // Read package.json
     * const packageJson = await workspaceKnowledge.getFileContent('package.json');
     * const packageData = JSON.parse(packageJson);
     *
     * // Read source code files
     * const mainFile = await workspaceKnowledge.getFileContent('src/main.ts');
     *
     * // Read configuration files
     * const config = await workspaceKnowledge.getFileContent('.vscode/settings.json');
     * ```
     */
    public async getFileContent(relativePath: string): Promise<string> {
        return this.readFile(relativePath);
    }

    /**
     * Opens a document in the VS Code editor with precise control over its display and selection.
     * This method provides a comprehensive way to interact with the editor programmatically.
     * 
     * @param {string} relativePath - Path to the file relative to workspace root
     * @param {Object} [options] - Configuration options for the editor
     * @param {ViewColumn} [options.viewColumn=ViewColumn.Active] - The editor column to open in
     * @param {boolean} [options.preserveFocus=false] - Whether to keep focus on the current editor
     * @param {boolean} [options.preview=true] - Open in preview mode (single tab)
     * @param {Range} [options.selection] - Text selection range to apply
     * @param {TextEditorRevealType} [options.revealType=TextEditorRevealType.Default] - How to reveal the selection
     * @param {string} [options.content] - Optional content to write before opening
     * @returns {Promise<{ editor: TextEditor; document: TextDocument } | undefined>} The editor and document instances
     * @throws {Error} If the file cannot be accessed or edited
     * 
     * @example
     * ```typescript
     * // Open a file in a new editor group
     * await fsManager.openInEditor('src/main.ts', {
     *   viewColumn: ViewColumn.Beside,
     *   preview: false,
     *   selection: new Range(new Position(10, 0), new Position(15, 0)),
     *   revealType: TextEditorRevealType.InCenterIfOutsideViewport
     * });
     * 
     * // Open and edit a file with new content
     * await fsManager.openInEditor('config.json', {
     *   content: JSON.stringify(config, null, 2),
     *   viewColumn: ViewColumn.One
     * });
     * ```
     */
    /**
     * Opens a document in the VS Code editor with precise control over its display and selection.
     * This method provides a comprehensive way to interact with the editor programmatically.
     * 
     * @param {string} relativePath - Path to the file relative to workspace root
     * @param {Object} [options] - Configuration options for the editor
     * @param {ViewColumn} [options.viewColumn=ViewColumn.Active] - The editor column to open in
     * @param {boolean} [options.preserveFocus=false] - Whether to keep focus on the current editor
     * @param {boolean} [options.preview=true] - Open in preview mode (single tab)
     * @param {Range} [options.selection] - Text selection range to apply
     * @param {TextEditorRevealType} [options.revealType=TextEditorRevealType.Default] - How to reveal the selection
     * @param {string} [options.content] - Optional content to write before opening
     * @returns {Promise<{ editor: TextEditor; document: TextDocument } | undefined>} The editor and document instances
     * @throws {Error} If the file cannot be accessed or edited
     */
    public async openInEditor(
        relativePath: string,
        options: {
            viewColumn?: ViewColumn;
            preserveFocus?: boolean;
            preview?: boolean;
            selection?: Range;
            revealType?: TextEditorRevealType;
            content?: string;
        } = {}
    ): Promise<{ editor: TextEditor; document: TextDocument } | undefined> {
        // Resolve and validate the file path
        const absolutePath = this._resolveAndValidatePath(relativePath);
        const uri = vscode.Uri.file(absolutePath);

        try {
            // Update file content if new content is provided
            if (options.content !== undefined) {
                await this.writeFile(relativePath, options.content);
            }

            // Open the document first to get TextDocument instance
            const document: TextDocument = await vscode.workspace.openTextDocument(uri);

            // Configure TextDocumentShowOptions with all relevant properties
            const showOptions: TextDocumentShowOptions = {
                viewColumn: options.viewColumn ?? vscode.ViewColumn.Active,
                preserveFocus: options.preserveFocus ?? false,
                preview: options.preview ?? true
            };

            // If a selection is provided, configure it in the show options
            if (options.selection) {
                showOptions.selection = options.selection;
            }

            // Show the document in the editor and get the TextEditor instance
            const editor: TextEditor = await vscode.window.showTextDocument(document, showOptions);

            // If we have both a selection and a reveal type, apply them after the editor is ready
            if (options.selection && options.revealType) {
                // Use requestAnimationFrame to ensure the editor is fully initialized
                requestAnimationFrame(() => {
                    // Set the selection first
                    if (options.selection && options.revealType) {
                        const { start, end } = options.selection;
                        editor.selection = new vscode.Selection(start, end);

                        // Then reveal the selection with the specified reveal type
                        editor.revealRange(
                            options.selection,
                            options.revealType
                        );
                    }
                });
            } else if (options.selection) {
                // If only selection is provided, just set the selection
                const { start, end } = options.selection;
                editor.selection = new vscode.Selection(start, end);

                // Reveal with default behavior if no specific reveal type is provided
                editor.revealRange(
                    options.selection,
                    vscode.TextEditorRevealType.Default
                );
            } else if (options.revealType) {
                // If only reveal type is provided, reveal the current selection
                editor.revealRange(
                    editor.selection,
                    options.revealType
                );
            }

            return { editor, document };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            throw new Error(`Failed to open file in editor: ${errorMessage}`);
        }
    }
}

/**
 * A lightweight wrapper around the InteractiveSession from pseudoTerminal.ts
 * that provides a simplified interface for terminal operations.
 *
 * This class is provided for backward compatibility. New code should use
 * the InteractiveSession class directly from './pseudoTerminal'.
 *
 * @class TerminalService
 * @implements {ITerminalService}
 * @deprecated Use InteractiveSession from './pseudoTerminal' directly in new code
 */
export class TerminalService implements ITerminalService {
    private readonly logger: ILogger;
    private session: InteractiveSession | null = null;
    private readonly onDataEmitter = new vscode.EventEmitter<string>();
    private readonly onExitEmitter = new vscode.EventEmitter<number>();
    private readonly onErrorEmitter = new vscode.EventEmitter<Error>();
    private readonly onReadyEmitter = new vscode.EventEmitter<void>();
    private readonly onResizeEmitter = new vscode.EventEmitter<{ columns: number; rows: number }>();
    private readonly onCommandEmitter = new vscode.EventEmitter<string>();
    private readonly onStateChangeEmitter = new vscode.EventEmitter<TerminalState>();
    private readonly shell: string;

    constructor(logger: ILogger) {
        this.logger = logger;
        this.shell = process.platform === 'win32' ? 'powershell.exe' : (process.env.SHELL || 'bash');
    }

    private resolveCwd(cwd?: string): string {
        return cwd || process.cwd();
    }

    private async ensureSession(cwd?: string): Promise<InteractiveSession> {
        if (!this.session) {
            this.session = new InteractiveSession({
                cwd: cwd || process.cwd(),
                shell: process.platform === 'win32' ? 'powershell.exe' : (process.env.SHELL || 'bash'),
                dimensions: { columns: 120, rows: 30 },
                enableCommandHistory: true,
                maxCommandHistory: 1000,
                enableAutoComplete: true,
                workingDirectoryTracking: true,
                scrollback: 10000,
                fontSize: 14,
                fontFamily: 'Consolas, "Courier New", monospace',
                theme: 'system',
                hideFromUser: false,
                isTransient: false,
                enableBracketedPaste: true,
                enableUnicodeSupport: true,
                enableColorSupport: true
            });

            // Set up event forwarding
            this.session.onData((data: string) => this.onDataEmitter.fire(data));
            this.session.onExit((code: number) => {
                this.onExitEmitter.fire(code);
                this.session = null; // Clear the session on exit
            });
            this.session.onError((error: Error) => this.onErrorEmitter.fire(error));
            this.session.onReady(() => this.onReadyEmitter.fire());
            this.session.onResize(dimensions => this.onResizeEmitter.fire(dimensions));
            this.session.onCommand(cmd => this.onCommandEmitter.fire(cmd));
            this.session.onStateChange(state => this.onStateChangeEmitter.fire(state));

            // Initialize the session
            try {
                await this.session.initialize();
                this.logger.info('Terminal session initialized successfully');
            } catch (error: unknown) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                this.logger.error(`Failed to initialize terminal session: ${errorMessage}`);
                throw error;
            }
        }
        return this.session;
    }

    async execute(
        command: string,
        args: string[],
        options: {
            cwd?: string;
            cancellationToken?: vscode.CancellationToken;
            timeout?: number;
            background?: boolean;
            workingDirectory?: string;
        } = {}
    ): Promise<CommandResult> {
        const session = await this.ensureSession(options.cwd || options.workingDirectory);
        try {
            const commandString = [command, ...args].join(' ');
            const result = await session.executeCommandAdvanced(commandString, {
                workingDirectory: options.cwd || options.workingDirectory,
                timeout: options.timeout || 30000, // 30 second default timeout
                background: options.background || false
            });

            return {
                command: commandString,
                output: result.output,
                exitCode: result.exitCode,
                duration: result.duration,
                success: result.success,
                error: result.error,
                completedAt: new Date()
            };
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.logger.error(`Failed to execute command: ${errorMessage}`);
            throw new TerminalError(`Command execution failed: ${errorMessage}`);
        }
    }

    /**
     * Executes a shell command and returns the output.
     * 
     * @param command The command to execute
     * @param options Command execution options
     * @returns The command output as a string
     */
    async executeSimple(
        command: string,
        options: {
            cwd?: string;
            timeout?: number;
            expectPrompt?: boolean;
            background?: boolean;
        } = {}
    ): Promise<string> {
        const session = await this.ensureSession(options.cwd);
        try {
            return await session.executeCommand(command, {
                workingDirectory: options.cwd,
                timeout: options.timeout,
                expectPrompt: options.expectPrompt,
                background: options.background
            });
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            this.logger.error(`Failed to execute simple command: ${errorMessage}`);
            throw new TerminalError(`Command execution failed: ${errorMessage}`);
        }
    }


    async createSession(options: { cwd?: string } = {}): Promise<IInteractiveSession> {
        const session = await this.ensureSession(options.cwd);

        // Create a wrapper that implements IInteractiveSession
        const wrapper: IInteractiveSession = {
            id: session.id,
            onData: this.onDataEmitter.event,
            onExit: this.onExitEmitter.event,
            write: (data: string) => session.write(data),
            resize: (cols: number, rows: number) => session.resize(cols, rows),
            dispose: () => {
                session.dispose();
                this.session = null;
            }
        };

        // Add additional methods with type assertions
        Object.assign(wrapper, {
            writeLn: (data: string) => session.writeLn(data),
            clear: () => session.clear(),
            reset: () => session.reset(),
            sendSignal: (signal: string) => session.sendSignal(signal),
            getWorkingDirectory: () => session.getWorkingDirectory(),
            setWorkingDirectory: (path: string) => session.setWorkingDirectory(path),
            getCommandHistory: () => session.getCommandHistory(),
            clearCommandHistory: () => session.clearCommandHistory(),
            enableLogging: (logPath?: string) => session.enableLogging(logPath),
            disableLogging: () => session.disableLogging(),
            state: session.state,
            stats: session.stats,
            events: {
                onData: this.onDataEmitter.event,
                onExit: this.onExitEmitter.event,
                onReady: this.onReadyEmitter.event,
                onResize: this.onResizeEmitter.event,
                onCommand: this.onCommandEmitter.event,
                onError: this.onErrorEmitter.event,
                onStateChange: this.onStateChangeEmitter.event
            }
        });
        
        return wrapper;
    }

    /**
     * Disposes of the terminal service and all associated resources.
     * This includes the terminal session, all event emitters, and any other resources.
     */
    dispose() {
        if (this.session) {
            this.session.dispose();
            this.session = null;
        }

        // Dispose all event emitters
        this.onDataEmitter.dispose();
        this.onExitEmitter.dispose();
        this.onErrorEmitter.dispose();
        this.onReadyEmitter.dispose();
        this.onResizeEmitter.dispose();
        this.onCommandEmitter.dispose();
        this.onStateChangeEmitter.dispose();
    }

    // ======================
    // Additional Methods
    // ======================

    /**
     * Gets the current working directory of the terminal.
     * @returns A promise that resolves to the current working directory
     */
    async getWorkingDirectory(): Promise<string> {
        const session = await this.ensureSession();
        return session.getWorkingDirectory();
    }

    /**
     * Changes the working directory of the terminal.
     * @param path The new working directory path
     * @returns A promise that resolves when the directory is changed
     */
    async setWorkingDirectory(path: string): Promise<void> {
        const session = await this.ensureSession();
        return session.setWorkingDirectory(path);
    }

    /**
     * Gets the command history for the current session.
     * @returns An array of previously executed commands
     */
    getCommandHistory(): string[] {
        if (!this.session) {
            return [];
        }
        return this.session.getCommandHistory();
    }

    /**
     * Clears the command history for the current session.
     */
    clearCommandHistory(): void {
        if (this.session) {
            this.session.clearCommandHistory();
        }
    }

    /**
     * Enables logging of terminal output to a file.
     * @param logPath Optional path to the log file. If not provided, a default path is used.
     */
    enableLogging(logPath?: string): void {
        if (this.session) {
            this.session.enableLogging(logPath);
        } else {
            // Store the log path to be used when session is created
            this.ensureSession().then(session => {
                session.enableLogging(logPath);
            });
        }
    }

    /**
     * Disables logging of terminal output.
     */
    disableLogging(): void {
        if (this.session) {
            this.session.disableLogging();
        }
    }

    /**
     * Resets the terminal to its initial state.
     * This clears the screen and resets the terminal settings.
     */
    async reset(): Promise<void> {
        const session = await this.ensureSession();
        return session.reset();
    }

    /**
     * Sends a signal to the terminal process.
     * @param signal The signal to send (e.g., 'SIGINT', 'SIGTERM')
     */
    async sendSignal(signal: string): Promise<void> {
        const session = await this.ensureSession();
        return session.sendSignal(signal);
    }

    /**
     * Gets the current terminal state.
     * @returns The current terminal state or null if no session exists
     */
    getState(): TerminalState | null {
        return this.session?.state || null;
    }

    /**
     * Gets terminal statistics.
     * @returns Terminal statistics or null if no session exists
     */
    getStats(): TerminalStats | null {
        return this.session?.stats || null;
    }

    /**
     * Writes data to the terminal followed by a newline.
     * @param data The data to write
     */
    async writeLn(data: string): Promise<void> {
        const session = await this.ensureSession();
        return session.writeLn(data);
    }

    /**
     * Writes data to the terminal.
     * @param data The data to write
     */
    async write(data: string): Promise<void> {
        const session = await this.ensureSession();
        return session.write(data);
    }

    /**
     * Clears the terminal screen.
     */
    async clear(): Promise<void> {
        const session = await this.ensureSession();
        return session.clear();
    }

    // ======================
    // Event Registration
    // ======================

    /**
     * Event that fires when data is received from the terminal.
     */
    get onData() {
        return this.onDataEmitter.event;
    }

    /**
     * Event that fires when the terminal exits.
     */
    get onExit() {
        return this.onExitEmitter.event;
    }

    /**
     * Event that fires when an error occurs.
     */
    get onError() {
        return this.onErrorEmitter.event;
    }

    /**
     * Event that fires when the terminal is ready for input.
     */
    get onReady() {
        return this.onReadyEmitter.event;
    }

    /**
     * Event that fires when the terminal is resized.
     */
    get onResize() {
        return this.onResizeEmitter.event;
    }

    /**
     * Event that fires when a command is executed.
     */
    get onCommand() {
        return this.onCommandEmitter.event;
    }

    /**
     * Event that fires when the terminal state changes.
     */
    get onStateChange() {
        return this.onStateChangeEmitter.event;
    }

    /**
     * Streams the output of a command in real-time using a callback function.
     *
     * This method executes a command and provides real-time output streaming through
     * a callback function. It's ideal for long-running commands where you need to
     * process output as it's generated, such as build processes or file watching.
     *
     * The streaming supports:
     * - Real-time data callback for processing output chunks
     * - Cancellation support for stopping long-running commands
     * - Cross-platform command execution
     * - Proper resource cleanup when streaming completes
     * - Error handling for initialization failures
     *
     * @param command The command to execute
     * @param args Array of command-line arguments
     * @param options Streaming options
     * @returns A promise that resolves with the exit code when the command completes
     */
    async stream(
        command: string,
        args: string[],
        options: { cwd?: string; onData: (data: string) => void; cancellationToken?: vscode.CancellationToken }
    ): Promise<{ exitCode: number }> {
        const session = await this.ensureSession(options.cwd);
        const commandString = [command, ...args].join(' ');

        return new Promise<{ exitCode: number }>((resolve) => {
            const disposables: vscode.Disposable[] = [];

            // Handle cancellation
            if (options.cancellationToken) {
                const cancelDisposable = options.cancellationToken.onCancellationRequested(() => {
                    session.dispose();
                    resolve({ exitCode: -1 });
                });
                disposables.push(cancelDisposable);
            }

            // Handle process exit
            const exitDisposable = session.onExit((exitCode: number) => {
                disposables.forEach(d => d.dispose());
                resolve({ exitCode });
            });
            disposables.push(exitDisposable);

            // Forward output to the provided callback
            const dataDisposable = session.onData((data: string) => {
                options.onData(data);
            });
            disposables.push(dataDisposable);

            // Start the command
            session.initialize()
                .then(() => session.writeLn(commandString))
                .catch(error => {
                    this.logger.error('Failed to initialize or write to terminal stream:', error);
                    resolve({ exitCode: -1 });
                });
        });
    }
}

/**
 * VS Code implementation of the IClipboardService interface.
 *
 * This service provides secure, cross-platform clipboard operations within the VS Code
 * environment, supporting both text and image data with proper error handling and
 * type safety. It serves as a bridge between the tool framework and VS Code's
 * clipboard APIs, ensuring consistent behavior across different platforms.
 *
 * Key features:
 * - Cross-platform clipboard access (Windows, macOS, Linux)
 * - Support for both text and image clipboard content
 * - Multimodal data handling with proper MIME type support
 * - Base64 encoding for image data transport
 * - Comprehensive error handling and validation
 * - Integration with VS Code's clipboard security model
 * - Support for JPEG, PNG, and WebP image formats
 *
 * The service delegates to a specialized clipboard service implementation that
 * handles the platform-specific details of clipboard interaction while providing
 * a clean, consistent API for tools to use.
 *
 * @class VSCodeClipboardService
 * @implements {IClipboardService}
 *
 * @example
 * ```typescript
 * const clipboardService = new VSCodeClipboardService(logger);
 *
 * // Read text from clipboard
 * try {
 *   const text = await clipboardService.readText();
 *   console.log('Clipboard text:', text);
 * } catch (error) {
 *   console.error('Failed to read clipboard:', error);
 * }
 *
 * // Write text to clipboard
 * await clipboardService.writeText('Hello, World!');
 *
 * // Read image from clipboard
 * const imageData = await clipboardService.readImage();
 * if (imageData) {
 *   console.log(`Image (${imageData.mimeType}):`, imageData.base64Data.substring(0, 50) + '...');
 * }
 * ```
 */
export class VSCodeClipboardService implements IClipboardService {
    /** Reference to the underlying clipboard service implementation */
    private clipboardService!: IClipboardService;

    /**
     * Creates a new VS Code clipboard service instance.
     *
     * This constructor initializes the clipboard service by dynamically loading
     * and instantiating the specialized clipboard service implementation. It
     * accepts an optional logger for tracking clipboard operations and debugging.
     *
     * The service uses dynamic require to load the clipboard service implementation,
     * allowing for modular architecture and easier testing. The logger is passed
     * to the underlying service for consistent logging across the framework.
     *
     * @param {ILogger} [logger] - Optional logger instance for tracking clipboard operations
     *
     * @example
     * ```typescript
     * // Create with logger for debugging
     * const logger = new VSCodeOutputChannelLogger('Clipboard');
     * const clipboardService = new VSCodeClipboardService(logger);
     *
     * // Create without logger
     * const clipboardService = new VSCodeClipboardService();
     * ```
     */
    constructor(logger?: ILogger) {
        import('./clipboardService').then(service => {
            this.clipboardService = new service.VSCodeClipboardService(logger);
        });
    }

    /**
     * Reads plain text from the system clipboard.
     *
     * This method retrieves text content from the system clipboard, if available.
     * It handles various text encodings and provides proper error handling for
     * cases where the clipboard doesn't contain text or access is denied.
     *
     * The method supports:
     * - Plain text clipboard content
     * - Unicode text with proper encoding handling
     * - Cross-platform clipboard access
     * - Comprehensive error reporting for access issues
     *
     * @returns {Promise<string>} A promise that resolves with the text content of the clipboard
     * @throws {Error} If reading from the clipboard fails or if the clipboard doesn't contain text
     *
     * @example
     * ```typescript
     * try {
     *   const text = await clipboardService.readText();
     *   console.log('Clipboard contains:', text);
     *
     *   // Process the text (e.g., for code generation)
     *   const generatedCode = await generateCodeFromDescription(text);
     *   await fileSystem.writeFile('generated.ts', generatedCode);
     * } catch (error) {
     *   if (error.message.includes('No text')) {
     *     console.log('No text in clipboard');
     *   } else {
     *     console.error('Clipboard access failed:', error);
     *   }
     * }
     * ```
     */
    public async readText(): Promise<string> {
        return this.clipboardService.readText();
    }

    /**
     * Reads an image from the clipboard if available.
     *
     * This method attempts to retrieve image data from the system clipboard.
     * If an image is present, it returns the image data encoded as base64 with
     * proper MIME type information. If no image is available, it returns null.
     *
     * The method supports:
     * - JPEG, PNG, and WebP image formats
     * - Base64 encoding for safe transport
     * - Proper MIME type detection and reporting
     * - Graceful handling when no image is present
     * - Cross-platform image clipboard access
     *
     * @returns {Promise<MultimodalData | null>} A promise that resolves with the image data
     *         or null if the clipboard doesn't contain an image
     * @throws {Error} If reading from the clipboard fails or if the image format is unsupported
     *
     * @example
     * ```typescript
     * try {
     *   const imageData = await clipboardService.readImage();
     *
     *   if (imageData) {
     *     console.log(`Found image (${imageData.mimeType})`);
     *
     *     // Use with multimodal AI processing
     *     const prompt: LanguageModelPrompt = [
     *       { type: 'text', text: 'Describe this image:' },
     *       {
     *         type: 'image',
     *         source: {
     *           type: 'base64',
     *           media_type: imageData.mimeType,
     *           data: imageData.base64Data
     *         }
     *       }
     *     ];
     *
     *     const description = await llm.generateResponse(prompt, options);
     *     console.log('Image description:', description);
     *   } else {
     *     console.log('No image in clipboard');
     *   }
     * } catch (error) {
     *   console.error('Failed to read image from clipboard:', error);
     * }
     * ```
     */
    public async readImage(): Promise<MultimodalData | null> {
        return this.clipboardService.readImage();
    }

    /**
     * Writes text to the system clipboard.
     *
     * This method writes the specified text content to the system clipboard,
     * making it available for pasting in other applications. It handles text
     * encoding properly and provides error handling for clipboard access issues.
     *
     * The method supports:
     * - Plain text content of any length
     * - Unicode text with proper encoding
     * - Cross-platform clipboard writing
     * - Error handling for access denied scenarios
     *
     * @param {string} text - The text to write to the clipboard
     * @returns {Promise<void>} A promise that resolves when the operation is complete
     * @throws {Error} If writing to the clipboard fails
     *
     * @example
     * ```typescript
     * try {
     *   // Copy generated code to clipboard
     *   const generatedCode = await generateTypeScriptCode();
     *   await clipboardService.writeText(generatedCode);
     *
     *   console.log('Code copied to clipboard successfully');
     * } catch (error) {
     *   console.error('Failed to copy to clipboard:', error);
     * }
     *
     * // Copy user-friendly messages
     * await clipboardService.writeText('Operation completed successfully!');
     * ```
     */
    public async writeText(text: string): Promise<void> {
        return this.clipboardService.writeText(text);
    }
}
