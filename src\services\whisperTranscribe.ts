import * as vscode from 'vscode';
import * as https from 'https';
import * as path from 'path';
import * as fs from 'fs';

export async function transcribeWithOpenAIWhisper(audioData: Uint8Array, apiKey: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const boundary = '----WebKitFormBoundary' + Math.random().toString(16).slice(2);
    const audioBuffer = Buffer.from(audioData);
    const filename = 'audio.wav';
    const formDataStart = Buffer.from(
      `--${boundary}\r\n` +
            `Content-Disposition: form-data; name="file"; filename="${filename}"\r\n` +
            'Content-Type: audio/wav\r\n\r\n'
    );
    const formDataEnd = Buffer.from(`\r\n--${boundary}--\r\n`);
    const body = Buffer.concat([formDataStart, audioBuffer, formDataEnd]);

    const options = {
      hostname: 'api.openai.com',
      path: '/v1/audio/transcriptions',
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': `multipart/form-data; boundary=${boundary}`,
        'Content-Length': body.length
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const json = JSON.parse(data);
          if (json.text) {
            resolve(json.text);
          } else {
            reject(json.error?.message || 'No transcription result');
          }
        } catch (err) {
          reject('Failed to parse Whisper API response: ' + err);
        }
      });
    });
    req.on('error', (err) => {
      reject('Whisper API request failed: ' + err);
    });
    req.write(body);
    req.end();
  });
}
