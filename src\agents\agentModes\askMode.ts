// Remove unused vscode import
import { OperationMode, ContextSource, ContextType } from './operationMode';
import { Agent } from '../agentUtilities/agent';
import { LLMGenerateParams } from '../../llm/types';
import { Logger } from '../../logger';
import { contextManager } from './contextManager';
import { promptManager } from '../../prompts/promptManager';

/**
 * Ask Mode - Default mode that uses the entire codebase as context
 */
export class AskMode extends OperationMode {
  readonly id = 'ask';
  readonly displayName = 'Ask';
  readonly description = 'Ask questions about your codebase';
  readonly icon = '$(question)';
  readonly defaultContextType = ContextType.ENTIRE_CODEBASE;
  readonly requiresHumanVerification = false;
  readonly supportsMultipleAgents = false;

  /**
     * Process a user message in Ask mode
     */
  async processMessage(
    message: string,
    agent: Agent,
    contextSource: ContextSource,
    _additionalParams?: Record<string, unknown>
  ): Promise<string> {
    try {
      Logger.instance.info(`Processing message in Ask mode: ${message}`);

      // Get context content
      const contextContent = await contextManager.getContextContent(contextSource);

      // Add memory context if available
      let memoryContext = '';
      try {
        const agentMemory = agent.getMemory();
        if (agentMemory) {
          const relevantMemories = await agentMemory.getRelevantMemories(message);
          if (relevantMemories && relevantMemories.length > 0) {
            memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);
            Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to ask context`);
          }
        }
      } catch (memoryError) {
        Logger.instance.warn('Failed to retrieve memory context for ask:', memoryError);
        // Continue without memory context
      }

      // Prepare the prompt using promptManager
      const prompt = promptManager.renderPrompt('mode.ask', {
        contextContent,
        memoryContext,
        message
      });

      // Generate response using the agent
      const response = await agent.generate(prompt, this.getLLMParams());

      // Store the conversation in memory
      try {
        const agentMemory = agent.getMemory();
        if (agentMemory) {
          await agentMemory.addMessage('user', message);
          await agentMemory.addMessage('assistant', response);
        }
      } catch (memoryError) {
        Logger.instance.warn('Failed to store conversation in memory:', memoryError);
        // Continue without storing in memory
      }

      return response;
    } catch (error) {
      Logger.instance.error('Error processing message in Ask mode:', error);
      return `Error processing your question: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
     * Get LLM parameters specific to Ask mode
     */
  getLLMParams(): LLMGenerateParams {
    return {
      prompt: '',
      modelId: '',
      temperature: 0.7,
      maxTokens: 2000,
      mode: 'task'
    };
  }

  /**
     * Get the system prompt for Ask mode
     */
  async getSystemPrompt(
    _agent: Agent,
    _contextSource: ContextSource
  ): Promise<string> {
    return promptManager.renderPrompt('mode.ask', {});
  }

  /**
     * Get UI components specific to Ask mode
     */
  getUIComponents(): {
    controlPanel?: string;
    contextPanel?: string;
    messageInput?: string;
  } {
    return {
      contextPanel: `
<div class="context-panel">
    <div class="context-header">
        <h3>Context</h3>
        <div class="context-controls">
            <button id="btn-refresh-context" title="Refresh Context"><i class="codicon codicon-refresh"></i></button>
            <button id="btn-select-files" title="Select Files"><i class="codicon codicon-file-code"></i></button>
            <button id="btn-select-folders" title="Select Folders"><i class="codicon codicon-folder"></i></button>
        </div>
    </div>
    <div class="context-type">
        <select id="context-type-selector">
            <option value="entire_codebase">Entire Codebase</option>
            <option value="selected_files">Selected Files</option>
            <option value="current_file">Current File</option>
            <option value="custom">Custom</option>
        </select>
    </div>
    <div id="context-files-list" class="context-files-list"></div>
</div>
`,
      messageInput: `
<div class="message-input-container">
    <textarea id="message-input" placeholder="Ask a question about your code..."></textarea>
    <button id="btn-send" title="Send"><i class="codicon codicon-send"></i></button>
</div>
`
    };
  }
}
