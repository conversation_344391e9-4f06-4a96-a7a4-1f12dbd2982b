import * as vscode from 'vscode';
import { ITool, ToolInput, ToolResult } from './tool.ts.backup';
import { AgentContext } from '../types/agent';
import { z } from 'zod';

export class BatchLintTool implements ITool {
  readonly id = 'batchLint';
  readonly name = 'Batch Lint';
  readonly description = 'Run lint on multiple files or the entire workspace.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({
    files: z.array(z.string()).optional().describe('Files to lint. If empty, lint all.'),
    linter: z.string().optional().describe('Linter to use (e.g., eslint, stylelint, etc.).')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      files: { type: 'array', items: { type: 'string' }, description: 'Files to lint. If empty, lint all.' },
      linter: { type: 'string', description: '<PERSON><PERSON> to use (e.g., eslint, stylelint, etc.).' }
    },
    required: []
  };
  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    try {
      // Validate input
      const parsedInput = this.singleActionSchema.safeParse(input);
      if (!parsedInput.success) {
        return {
          success: false,
          error: `Invalid input: ${parsedInput.error.message}`,
          toolId: this.id,
          actionName
        };
      }

      const files = input.files as string[] | undefined;
      const linter = input.linter as string || 'eslint';

      // Validate linter name to prevent command injection
      if (!/^[a-zA-Z0-9_\-]+$/.test(linter)) {
        return {
          success: false,
          error: 'Invalid linter name. Only alphanumeric characters, hyphens, and underscores are allowed.',
          toolId: this.id,
          actionName
        };
      }

      // Check if workspace is available
      if (!vscode.workspace.workspaceFolders || vscode.workspace.workspaceFolders.length === 0) {
        return {
          success: false,
          error: 'No workspace folder open. Linting requires an open workspace.',
          toolId: this.id,
          actionName
        };
      }

      // Track results for each file
      const results: Array<{file: string, success: boolean, error?: string}> = [];

      if (files && files.length > 0) {
        // Lint specific files
        for (const file of files) {
          try {
            // Validate file path
            const uri = vscode.Uri.file(file);
            let exists = false;
            try {
              await vscode.workspace.fs.stat(uri);
              exists = true;
            } catch {
              exists = false;
            }

            if (!exists) {
              results.push({
                file,
                success: false,
                error: 'File does not exist'
              });
              continue;
            }

            // Run the lint task
            await vscode.commands.executeCommand('workbench.action.tasks.runTask', `${linter}:lint`, { file });

            results.push({
              file,
              success: true
            });
          } catch (err: any) {
            results.push({
              file,
              success: false,
              error: err.message || 'Unknown error'
            });
          }
        }

        const successCount = results.filter(r => r.success).length;

        return {
          success: successCount > 0,
          output: {
            message: `Linted ${successCount}/${files.length} files with ${linter}.`,
            results
          },
          toolId: this.id,
          actionName,
          metadata: {
            linter,
            fileCount: files.length,
            successCount,
            failureCount: files.length - successCount
          }
        };
      } else {
        // Lint entire workspace
        try {
          await vscode.commands.executeCommand('workbench.action.tasks.runTask', `${linter}:lint`);

          return {
            success: true,
            output: `Linted entire workspace with ${linter}.`,
            toolId: this.id,
            actionName,
            metadata: {
              linter,
              workspace: vscode.workspace.workspaceFolders[0].uri.fsPath
            }
          };
        } catch (err: any) {
          return {
            success: false,
            error: `Failed to lint workspace: ${err.message || 'Unknown error'}`,
            toolId: this.id,
            actionName
          };
        }
      }
    } catch (error: any) {
      return {
        success: false,
        error: `Batch lint failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }
}

export class AutoFixAllTool implements ITool {
  readonly id = 'autoFixAll';
  readonly name = 'Auto Fix All';
  readonly description = 'Automatically fix all fixable lint errors in given files or workspace.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({
    files: z.array(z.string()).optional().describe('Files to fix. If empty, fix all.'),
    linter: z.string().optional().describe('Linter to use (e.g., eslint, stylelint, etc.).')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      files: { type: 'array', items: { type: 'string' }, description: 'Files to fix. If empty, fix all.' },
      linter: { type: 'string', description: 'Linter to use (e.g., eslint, stylelint, etc.).' }
    },
    required: []
  };
  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    try {
      // Validate input
      const parsedInput = this.singleActionSchema.safeParse(input);
      if (!parsedInput.success) {
        return {
          success: false,
          error: `Invalid input: ${parsedInput.error.message}`,
          toolId: this.id,
          actionName
        };
      }

      const files = input.files as string[] | undefined;
      const linter = input.linter as string || 'eslint';

      // Validate linter name to prevent command injection
      if (!/^[a-zA-Z0-9_\-]+$/.test(linter)) {
        return {
          success: false,
          error: 'Invalid linter name. Only alphanumeric characters, hyphens, and underscores are allowed.',
          toolId: this.id,
          actionName
        };
      }

      // Check if workspace is available
      if (!vscode.workspace.workspaceFolders || vscode.workspace.workspaceFolders.length === 0) {
        return {
          success: false,
          error: 'No workspace folder open. Auto-fixing requires an open workspace.',
          toolId: this.id,
          actionName
        };
      }

      // Track results for each file
      const results: Array<{file: string, success: boolean, error?: string}> = [];

      if (files && files.length > 0) {
        // Auto-fix specific files
        for (const file of files) {
          try {
            // Validate file path
            const uri = vscode.Uri.file(file);
            let exists = false;
            try {
              await vscode.workspace.fs.stat(uri);
              exists = true;
            } catch {
              exists = false;
            }

            if (!exists) {
              results.push({
                file,
                success: false,
                error: 'File does not exist'
              });
              continue;
            }

            // Run the fix task
            await vscode.commands.executeCommand('workbench.action.tasks.runTask', `${linter}:fix`, { file });

            results.push({
              file,
              success: true
            });
          } catch (err: any) {
            results.push({
              file,
              success: false,
              error: err.message || 'Unknown error'
            });
          }
        }

        const successCount = results.filter(r => r.success).length;

        return {
          success: successCount > 0,
          output: {
            message: `Auto-fixed ${successCount}/${files.length} files with ${linter}.`,
            results
          },
          toolId: this.id,
          actionName,
          metadata: {
            linter,
            fileCount: files.length,
            successCount,
            failureCount: files.length - successCount
          }
        };
      } else {
        // Auto-fix entire workspace
        try {
          await vscode.commands.executeCommand('workbench.action.tasks.runTask', `${linter}:fix`);

          return {
            success: true,
            output: `Auto-fixed entire workspace with ${linter}.`,
            toolId: this.id,
            actionName,
            metadata: {
              linter,
              workspace: vscode.workspace.workspaceFolders[0].uri.fsPath
            }
          };
        } catch (err: any) {
          return {
            success: false,
            error: `Failed to auto-fix workspace: ${err.message || 'Unknown error'}`,
            toolId: this.id,
            actionName
          };
        }
      }
    } catch (error: any) {
      return {
        success: false,
        error: `Auto-fix all failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }
}

export class LintSummaryTool implements ITool {
  readonly id = 'lintSummary';
  readonly name = 'Lint Summary';
  readonly description = 'Generate a summary report of lint errors for the workspace.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({});
  readonly inputSchema = {
    type: 'object',
    properties: {},
    required: []
  };
  async execute(actionName: string | undefined, _input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    try {
      const diagnostics = vscode.languages.getDiagnostics();

      // Initialize counters
      let totalIssues = 0;
      let totalFiles = 0;
      let errorCount = 0;
      let warningCount = 0;
      let infoCount = 0;
      let hintCount = 0;

      // Map to store file-specific diagnostics
      const fileIssues: Array<{
                file: string;
                issues: number;
                errors: number;
                warnings: number;
                infos: number;
                hints: number;
            }> = [];

      // Process each file's diagnostics
      for (const [uri, diags] of diagnostics) {
        if (diags.length === 0) continue;

        totalFiles++;
        totalIssues += diags.length;

        // Count by severity for this file
        let fileErrors = 0;
        let fileWarnings = 0;
        let fileInfos = 0;
        let fileHints = 0;

        for (const diag of diags) {
          switch (diag.severity) {
          case vscode.DiagnosticSeverity.Error:
            errorCount++;
            fileErrors++;
            break;
          case vscode.DiagnosticSeverity.Warning:
            warningCount++;
            fileWarnings++;
            break;
          case vscode.DiagnosticSeverity.Information:
            infoCount++;
            fileInfos++;
            break;
          case vscode.DiagnosticSeverity.Hint:
            hintCount++;
            fileHints++;
            break;
          }
        }

        // Add file details to the map
        fileIssues.push({
          file: uri.fsPath,
          issues: diags.length,
          errors: fileErrors,
          warnings: fileWarnings,
          infos: fileInfos,
          hints: fileHints
        });
      }

      // Sort files by total issues (most issues first)
      fileIssues.sort((a, b) => b.issues - a.issues);

      // Create a structured summary
      const summaryObject = {
        overview: {
          totalFiles,
          totalIssues,
          byType: {
            errors: errorCount,
            warnings: warningCount,
            information: infoCount,
            hints: hintCount
          }
        },
        fileDetails: fileIssues,
        topIssueFiles: fileIssues.slice(0, 10) // Top 10 files with most issues
      };

      // Create a human-readable summary text
      const summaryText = [
        'Lint Summary:',
        '-------------',
        `Total Files with Issues: ${totalFiles}`,
        `Total Issues: ${totalIssues}`,
        `  - Errors: ${errorCount}`,
        `  - Warnings: ${warningCount}`,
        `  - Information: ${infoCount}`,
        `  - Hints: ${hintCount}`,
        '',
        'Top 5 Files with Most Issues:',
        ...fileIssues.slice(0, 5).map(f =>
          `  ${f.file}: ${f.issues} issues (${f.errors} errors, ${f.warnings} warnings, ${f.infos} info, ${f.hints} hints)`
        )
      ].join('\n');

      return {
        success: true,
        output: {
          text: summaryText,
          data: summaryObject
        },
        toolId: this.id,
        actionName,
        metadata: {
          totalIssues,
          errorCount,
          warningCount
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: `Lint summary failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }
}

export class DiagnosticsSearchTool implements ITool {
  readonly id = 'diagnosticsSearch';
  readonly name = 'Diagnostics Search';
  readonly description = 'Search/filter diagnostics by message or severity.';
  readonly type = 'single-action'; // Required by ITool
  readonly actions: Record<string, any> = {}; // Required by ITool
  readonly singleActionSchema = z.object({
    query: z.string().optional().describe('Text to search for in diagnostics.'),
    severity: z.number().optional().describe('Severity to filter (0=Error, 1=Warning, 2=Info, 3=Hint).')
  });
  readonly inputSchema = {
    type: 'object',
    properties: {
      query: { type: 'string', description: 'Text to search for in diagnostics.' },
      severity: { type: 'number', description: 'Severity to filter (0=Error, 1=Warning, 2=Info, 3=Hint).' }
    },
    required: []
  };
  async execute(actionName: string | undefined, input: ToolInput, _context?: AgentContext): Promise<ToolResult> {
    const query = input.query as string | undefined;
    const severity = input.severity as number | undefined;
    try {
      const diagnostics = vscode.languages.getDiagnostics();
      const results: any[] = [];
      let totalDiagnostics = 0;
      let matchedDiagnostics = 0;

      // Map severity numbers to human-readable names
      const severityNames = {
        0: 'Error',
        1: 'Warning',
        2: 'Information',
        3: 'Hint'
      };

      for (const [uri, diags] of diagnostics) {
        totalDiagnostics += diags.length;
        const fileMatches: any[] = [];

        for (const diag of diags) {
          // Apply filters
          const queryMatches = !query || diag.message.toLowerCase().includes(query.toLowerCase());
          const severityMatches = severity === undefined || diag.severity === severity;

          if (queryMatches && severityMatches) {
            matchedDiagnostics++;

            // Get the line of code where the diagnostic occurs
            let codeSnippet = '';
            try {
              const document = await vscode.workspace.openTextDocument(uri);
              const line = document.lineAt(diag.range.start.line);
              codeSnippet = line.text.trim();
            } catch (_err) {
              codeSnippet = '<Could not retrieve code>';
            }

            fileMatches.push({
              message: diag.message,
              severity: diag.severity,
              severityName: severityNames[diag.severity as keyof typeof severityNames] || 'Unknown',
              line: diag.range.start.line + 1, // 1-based line number for human readability
              column: diag.range.start.character + 1, // 1-based column number
              code: codeSnippet,
              range: {
                start: { line: diag.range.start.line, character: diag.range.start.character },
                end: { line: diag.range.end.line, character: diag.range.end.character }
              }
            });
          }
        }

        if (fileMatches.length > 0) {
          results.push({
            file: uri.fsPath,
            diagnostics: fileMatches
          });
        }
      }

      // Format the results in a more readable way
      const formattedResults = {
        summary: {
          totalFiles: diagnostics.length, // Use length instead of size for array
          totalDiagnostics,
          matchedDiagnostics,
          filters: {
            query: query || 'none',
            severity: severity !== undefined ? `${severityNames[severity as keyof typeof severityNames]} (${severity})` : 'none'
          }
        },
        results
      };

      return {
        success: true,
        output: formattedResults,
        toolId: this.id,
        actionName,
        metadata: {
          matchCount: matchedDiagnostics,
          fileCount: results.length
        }
      };
    } catch (error: any) {
      return {
        success: false,
        error: `Diagnostics search failed: ${error.message || error}`,
        toolId: this.id,
        actionName
      };
    }
  }
}
