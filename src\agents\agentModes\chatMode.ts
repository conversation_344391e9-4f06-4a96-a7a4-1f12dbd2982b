import * as vscode from 'vscode';
import { OperationMode, ContextSource, ContextType } from './operationMode';
import { Agent } from '../agentUtilities/agent';
import { LLMGenerateParams } from '../../llm/types';
import { Logger } from '../../logger';
import { AgentContext } from '../../types/agent';
import { promptManager } from '../../prompts/promptManager';

/**
 * Chat Mode - General chat with no specific context
 */
export class ChatMode extends OperationMode {
  readonly id = 'chat';
  readonly displayName = 'Chat';
  readonly description = 'General chat with the AI assistant';
  readonly icon = '$(comment)';
  readonly defaultContextType = ContextType.NONE;
  readonly requiresHumanVerification = false;
  readonly supportsMultipleAgents = false;

  /**
     * Process a user message in Chat mode
     */
  async processMessage(
    message: string,
    agent: Agent,
    // @ts-ignore - Parameter required by interface but not used in this implementation
    _contextSource: ContextSource,
    additionalParams?: Record<string, any> & { cancellationToken?: vscode.CancellationToken }
  ): Promise<string> {
    try {
      Logger.instance.info(`Processing message in Chat mode: ${message}`);

      // Get memory context
      let memoryContext = '';
      try {
        const agentMemory = agent.getMemory();
        if (agentMemory) {
          const relevantMemories = await agentMemory.getRelevantMemories(message);
          if (relevantMemories && relevantMemories.length > 0) {
            memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);
            Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to chat context`);
          }
        }
      } catch (memoryError) {
        Logger.instance.warn('Failed to retrieve memory context for chat:', memoryError);
        // Continue without memory context
      }

      // Use promptManager to create the enhanced message
      const enhancedMessage = promptManager.renderPrompt('mode.chat', {
        contextContent: '',
        memoryContext,
        message
      });

      // Pass the enhanced message to the agent
      const llmParams = this.getLLMParams();
      const response = await agent.generate(
        enhancedMessage,
        llmParams,
        additionalParams?.cancellationToken as AgentContext
      );

      // Store the conversation in memory
      try {
        const agentMemory = agent.getMemory();
        if (agentMemory) {
          await agentMemory.addMessage('user', message);
          await agentMemory.addMessage('assistant', response);
        }
      } catch (memoryError) {
        Logger.instance.warn('Failed to store conversation in memory:', memoryError);
        // Continue without storing in memory
      }

      return response;
    } catch (error) {
      // Check if the operation was cancelled
      if (additionalParams?.cancellationToken?.isCancellationRequested) {
        Logger.instance.info('Chat mode message processing cancelled by user');
        return 'Operation cancelled by user.';
      }

      Logger.instance.error('Error processing message in Chat mode:', error);
      return `Error processing your message: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
     * Get LLM parameters specific to Chat mode
     */
  getLLMParams(): LLMGenerateParams {
    return {
      prompt: '',
      modelId: '',
      temperature: 0.7,
      maxTokens: 2000,
      mode: 'chat'
    };
  }

  /**
     * Get the system prompt for Chat mode
     */
  async getSystemPrompt(
    // @ts-ignore - Parameter required by interface but not used in this implementation
    _agent: Agent,
    // @ts-ignore - Parameter required by interface but not used in this implementation
    _contextSource: ContextSource
  ): Promise<string> {
    return promptManager.renderPrompt('mode.chat', {});
  }

  /**
     * Get UI components specific to Chat mode
     */
  getUIComponents(): {
    controlPanel?: string;
    contextPanel?: string;
    messageInput?: string;
  } {
    return {
      messageInput: `
<div class="message-input-container">
    <textarea id="message-input" placeholder="Chat with the AI assistant..."></textarea>
    <button id="btn-send" title="Send"><i class="codicon codicon-send"></i></button>
</div>
`
    };
  }
}
