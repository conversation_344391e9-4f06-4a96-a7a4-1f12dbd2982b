"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.AudioService = void 0;
const vscode = __importStar(require("vscode"));
const logger_1 = require("../logger");
const ttsSettings_1 = require("../ui/settings/ttsSettings");
const whisperTranscribe_1 = require("./whisperTranscribe");
const systemTTS_1 = require("../ui/settings/systemTTS");
class AudioService {
    context;
    static instance;
    _mediaRecorder;
    _audioChunks = [];
    _settingsManager;
    _currentVoice = null;
    _isRecording = false;
    _lastRecording = null;
    constructor(context) {
        this.context = context;
        this._settingsManager = ttsSettings_1.TTSSettingsManager.getInstance(context);
        this._initializeSpeechSynthesis();
    }
    static getInstance(context) {
        if (!AudioService.instance) {
            AudioService.instance = new AudioService(context);
        }
        return AudioService.instance;
    }
    _initializeSpeechSynthesis() {
        if ('speechSynthesis' in window) {
            // Load voices when they become available
            if (speechSynthesis.getVoices().length > 0) {
                this._loadVoices();
            }
            speechSynthesis.onvoiceschanged = () => this._loadVoices();
        }
    }
    _loadVoices() {
        const voices = speechSynthesis.getVoices();
        const edgeVoices = voices.filter(voice => voice.name.includes('Microsoft'));
        this._settingsManager.updateVoices(ttsSettings_1.TTSProvider.EDGE, edgeVoices.map(voice => ({
            id: voice.voiceURI,
            name: voice.name,
            language: voice.lang,
            gender: voice.name.includes('Female') ? 'Female' : 'Male',
            provider: ttsSettings_1.TTSProvider.EDGE
        })));
    }
    async startRecording() {
        if (this._isRecording) {
            throw new Error('Recording is already in progress');
        }
        try {
            // Request microphone access with enhanced options
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 44100,
                    channelCount: 1
                }
            });
            // Use high-quality recording options
            const options = {
                mimeType: 'audio/webm;codecs=opus',
                audioBitsPerSecond: 128000
            };
            // Fallback for browsers that don't support webm
            if (!MediaRecorder.isTypeSupported(options.mimeType)) {
                options.mimeType = 'audio/wav';
            }
            this._mediaRecorder = new MediaRecorder(stream, options);
            this._audioChunks = [];
            this._mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this._audioChunks.push(event.data);
                }
            };
            this._mediaRecorder.onstop = async () => {
                try {
                    const audioBlob = new Blob(this._audioChunks, { type: this._mediaRecorder?.mimeType || 'audio/wav' });
                    // Convert to array buffer for processing
                    const arrayBuffer = await audioBlob.arrayBuffer();
                    const uint8Array = new Uint8Array(arrayBuffer);
                    // Store for later transcription
                    this._lastRecording = uint8Array;
                    logger_1.Logger.instance.info(`Recording completed: ${uint8Array.length} bytes`);
                }
                catch (error) {
                    logger_1.Logger.instance.error('Error processing recorded audio:', error);
                }
                finally {
                    // Stop all tracks
                    stream.getTracks().forEach(track => track.stop());
                }
            };
            this._mediaRecorder.onerror = (event) => {
                logger_1.Logger.instance.error('MediaRecorder error:', event);
                this._isRecording = false;
            };
            // Start recording with time slicing for better performance
            this._mediaRecorder.start(1000); // 1 second chunks
            this._isRecording = true;
            logger_1.Logger.instance.info('Recording started successfully');
        }
        catch (error) {
            logger_1.Logger.instance.error('Error starting recording:', error);
            this._isRecording = false;
            throw error;
        }
    }
    stopRecording() {
        return new Promise((resolve) => {
            if (this._mediaRecorder && this._isRecording) {
                this._mediaRecorder.onstop = async () => {
                    const audioBlob = new Blob(this._audioChunks, { type: 'audio/wav' });
                    const reader = new FileReader();
                    reader.onloadend = () => {
                        const base64data = reader.result;
                        resolve(base64data);
                    };
                    reader.readAsDataURL(audioBlob);
                };
                this._mediaRecorder.stop();
                this._isRecording = false;
            }
            else {
                resolve(null);
            }
        });
    }
    isRecording() {
        return this._isRecording;
    }
    async speak(text) {
        const settings = this._settingsManager.getSettings();
        if (!settings.enabled) {
            return;
        }
        try {
            switch (settings.provider) {
                case ttsSettings_1.TTSProvider.SYSTEM:
                    await this._speakWithSystem(text, settings);
                    break;
                case ttsSettings_1.TTSProvider.EDGE:
                    await this._speakWithEdge(text, settings);
                    break;
                case ttsSettings_1.TTSProvider.MICROSOFT:
                    await this._speakWithMicrosoft(text, settings);
                    break;
                case ttsSettings_1.TTSProvider.GOOGLE:
                    await this._speakWithGoogle(text, settings);
                    break;
                case ttsSettings_1.TTSProvider.OPENAI:
                    await this._speakWithOpenAI(text, settings);
                    break;
                case ttsSettings_1.TTSProvider.ELEVENLABS:
                    await this._speakWithElevenLabs(text, settings);
                    break;
                default:
                    await this._speakWithSystem(text, settings);
            }
        }
        catch (error) {
            logger_1.Logger.instance.error('Error speaking text:', error);
            throw error;
        }
    }
    async _speakWithMicrosoft(text, settings) {
        // Use SystemTTS for Windows
        await systemTTS_1.SystemTTS.speak(text, settings.voice, settings.rate, settings.pitch, settings.volume);
    }
    async _speakWithEdge(text, settings) {
        // Use browser speechSynthesis if available, else fallback to SystemTTS
        if (typeof window !== 'undefined' && 'speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(text);
            const voices = speechSynthesis.getVoices();
            const selectedVoice = voices.find(v => v.voiceURI === settings.voice || v.name === settings.voice);
            if (selectedVoice)
                utterance.voice = selectedVoice;
            utterance.rate = settings.rate;
            utterance.pitch = settings.pitch;
            utterance.volume = settings.volume;
            return new Promise((resolve, reject) => {
                utterance.onend = () => resolve();
                utterance.onerror = (error) => reject(error);
                speechSynthesis.speak(utterance);
            });
        }
        else {
            await systemTTS_1.SystemTTS.speak(text, settings.voice, settings.rate, settings.pitch, settings.volume);
        }
    }
    async _speakWithSystem(text, settings) {
        // Use system TTS (default)
        await systemTTS_1.SystemTTS.speak(text, settings.voice, settings.rate, settings.pitch, settings.volume);
    }
    async _speakWithGoogle(text, settings) {
        // Google Cloud TTS API implementation
        if (!settings.apiKeys?.google) {
            throw new Error('Google TTS API key not configured');
        }
        try {
            const https = require('https');
            const postData = JSON.stringify({
                input: { text },
                voice: {
                    languageCode: 'en-US',
                    name: settings.voice || 'en-US-Standard-A'
                },
                audioConfig: {
                    audioEncoding: 'MP3',
                    speakingRate: settings.rate || 1.0,
                    pitch: settings.pitch || 0.0,
                    volumeGainDb: (settings.volume - 1) * 20 || 0.0
                }
            });
            const options = {
                hostname: 'texttospeech.googleapis.com',
                path: '/v1/text:synthesize',
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${settings.apiKeys.google}`,
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(postData)
                }
            };
            return new Promise((resolve, reject) => {
                const req = https.request(options, (res) => {
                    let data = '';
                    res.on('data', (chunk) => data += chunk);
                    res.on('end', () => {
                        try {
                            const response = JSON.parse(data);
                            if (response.audioContent) {
                                // Play the audio content
                                this._playAudioContent(response.audioContent);
                                resolve();
                            }
                            else {
                                reject(new Error('No audio content received'));
                            }
                        }
                        catch (error) {
                            reject(error);
                        }
                    });
                });
                req.on('error', reject);
                req.write(postData);
                req.end();
            });
        }
        catch (error) {
            logger_1.Logger.instance.error('Google TTS error:', error);
            throw error;
        }
    }
    async _speakWithOpenAI(text, settings) {
        // OpenAI TTS API implementation
        if (!settings.apiKeys?.openai) {
            throw new Error('OpenAI TTS API key not configured');
        }
        try {
            const https = require('https');
            const postData = JSON.stringify({
                model: 'tts-1',
                input: text,
                voice: settings.voice || 'alloy',
                response_format: 'mp3',
                speed: settings.rate || 1.0
            });
            const options = {
                hostname: 'api.openai.com',
                path: '/v1/audio/speech',
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${settings.apiKeys.openai}`,
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(postData)
                }
            };
            return new Promise((resolve, reject) => {
                const req = https.request(options, (res) => {
                    const chunks = [];
                    res.on('data', (chunk) => chunks.push(chunk));
                    res.on('end', () => {
                        try {
                            const audioBuffer = Buffer.concat(chunks);
                            this._playAudioBuffer(audioBuffer);
                            resolve();
                        }
                        catch (error) {
                            reject(error);
                        }
                    });
                });
                req.on('error', reject);
                req.write(postData);
                req.end();
            });
        }
        catch (error) {
            logger_1.Logger.instance.error('OpenAI TTS error:', error);
            throw error;
        }
    }
    async _speakWithElevenLabs(text, settings) {
        // ElevenLabs TTS API implementation
        if (!settings.apiKeys?.elevenlabs) {
            throw new Error('ElevenLabs TTS API key not configured');
        }
        try {
            const https = require('https');
            const voiceId = settings.voice || 'rachel';
            const postData = JSON.stringify({
                text,
                model_id: 'eleven_monolingual_v1',
                voice_settings: {
                    stability: 0.5,
                    similarity_boost: 0.5,
                    style: 0.0,
                    use_speaker_boost: true
                }
            });
            const options = {
                hostname: 'api.elevenlabs.io',
                path: `/v1/text-to-speech/${voiceId}`,
                method: 'POST',
                headers: {
                    'xi-api-key': settings.apiKeys.elevenlabs,
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(postData)
                }
            };
            return new Promise((resolve, reject) => {
                const req = https.request(options, (res) => {
                    const chunks = [];
                    res.on('data', (chunk) => chunks.push(chunk));
                    res.on('end', () => {
                        try {
                            const audioBuffer = Buffer.concat(chunks);
                            this._playAudioBuffer(audioBuffer);
                            resolve();
                        }
                        catch (error) {
                            reject(error);
                        }
                    });
                });
                req.on('error', reject);
                req.write(postData);
                req.end();
            });
        }
        catch (error) {
            logger_1.Logger.instance.error('ElevenLabs TTS error:', error);
            throw error;
        }
    }
    _playAudioContent(base64Audio) {
        // Convert base64 to buffer and play
        const audioBuffer = Buffer.from(base64Audio, 'base64');
        this._playAudioBuffer(audioBuffer);
    }
    _playAudioBuffer(audioBuffer) {
        // Use system audio player to play the buffer
        const fs = require('fs');
        const path = require('path');
        const os = require('os');
        const tempFile = path.join(os.tmpdir(), `codessa_tts_${Date.now()}.mp3`);
        fs.writeFileSync(tempFile, audioBuffer);
        // Play using system command
        const { exec } = require('child_process');
        const platform = process.platform;
        let command = '';
        if (platform === 'win32') {
            command = `powershell -c "(New-Object Media.SoundPlayer '${tempFile}').PlaySync()"`;
        }
        else if (platform === 'darwin') {
            command = `afplay "${tempFile}"`;
        }
        else if (platform === 'linux') {
            command = `aplay "${tempFile}" || paplay "${tempFile}" || mpg123 "${tempFile}"`;
        }
        if (command) {
            exec(command, (error) => {
                // Clean up temp file
                fs.unlink(tempFile, () => { });
                if (error) {
                    logger_1.Logger.instance.error('Error playing audio:', error);
                }
            });
        }
    }
    setTTSEnabled(enabled) {
        this._settingsManager.updateSettings({ enabled });
    }
    isTTSEnabled() {
        return this._settingsManager.getSettings().enabled;
    }
    async speechToText(audioData) {
        // Use provided audio data or last recording
        const dataToTranscribe = audioData || this._lastRecording;
        if (!dataToTranscribe) {
            throw new Error('No audio data available for transcription');
        }
        // Get OpenAI API key from settings or context
        let apiKey = vscode.workspace.getConfiguration('codessa').get('openAIApiKey');
        if (!apiKey) {
            // Try to get from context storage
            apiKey = this.context.globalState.get('openAIApiKey') || '';
        }
        if (!apiKey) {
            throw new Error('OpenAI API key is not set. Please set codessa.openAIApiKey in your settings.');
        }
        try {
            const result = await (0, whisperTranscribe_1.transcribeWithOpenAIWhisper)(dataToTranscribe, apiKey);
            // Clear the last recording after successful transcription
            this._lastRecording = null;
            return result;
        }
        catch (err) {
            logger_1.Logger.instance.error('Whisper transcription failed:', err);
            throw new Error('Speech-to-text failed: ' + (err?.message || err));
        }
    }
    getLastRecording() {
        return this._lastRecording;
    }
    clearLastRecording() {
        this._lastRecording = null;
    }
}
exports.AudioService = AudioService;
//# sourceMappingURL=audioService.js.map