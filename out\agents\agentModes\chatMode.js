"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatMode = void 0;
const operationMode_1 = require("./operationMode");
const logger_1 = require("../../logger");
const promptManager_1 = require("../../prompts/promptManager");
/**
 * Chat Mode - General chat with no specific context
 */
class ChatMode extends operationMode_1.OperationMode {
    id = 'chat';
    displayName = 'Chat';
    description = 'General chat with the AI assistant';
    icon = '$(comment)';
    defaultContextType = operationMode_1.ContextType.NONE;
    requiresHumanVerification = false;
    supportsMultipleAgents = false;
    /**
       * Process a user message in Chat mode
       */
    async processMessage(message, agent, 
    // @ts-ignore - Parameter required by interface but not used in this implementation
    _contextSource, additionalParams) {
        try {
            logger_1.Logger.instance.info(`Processing message in Chat mode: ${message}`);
            // Get memory context
            let memoryContext = '';
            try {
                const agentMemory = agent.getMemory();
                if (agentMemory) {
                    const relevantMemories = await agentMemory.getRelevantMemories(message);
                    if (relevantMemories && relevantMemories.length > 0) {
                        memoryContext = agentMemory.formatMemoriesForPrompt(relevantMemories);
                        logger_1.Logger.instance.debug(`Added ${relevantMemories.length} relevant memories to chat context`);
                    }
                }
            }
            catch (memoryError) {
                logger_1.Logger.instance.warn('Failed to retrieve memory context for chat:', memoryError);
                // Continue without memory context
            }
            // Use promptManager to create the enhanced message
            const enhancedMessage = promptManager_1.promptManager.renderPrompt('mode.chat', {
                contextContent: '',
                memoryContext,
                message
            });
            // Pass the enhanced message to the agent
            const llmParams = this.getLLMParams();
            const response = await agent.generate(enhancedMessage, llmParams, additionalParams?.cancellationToken);
            // Store the conversation in memory
            try {
                const agentMemory = agent.getMemory();
                if (agentMemory) {
                    await agentMemory.addMessage('user', message);
                    await agentMemory.addMessage('assistant', response);
                }
            }
            catch (memoryError) {
                logger_1.Logger.instance.warn('Failed to store conversation in memory:', memoryError);
                // Continue without storing in memory
            }
            return response;
        }
        catch (error) {
            // Check if the operation was cancelled
            if (additionalParams?.cancellationToken?.isCancellationRequested) {
                logger_1.Logger.instance.info('Chat mode message processing cancelled by user');
                return 'Operation cancelled by user.';
            }
            logger_1.Logger.instance.error('Error processing message in Chat mode:', error);
            return `Error processing your message: ${error instanceof Error ? error.message : String(error)}`;
        }
    }
    /**
       * Get LLM parameters specific to Chat mode
       */
    getLLMParams() {
        return {
            prompt: '',
            modelId: '',
            temperature: 0.7,
            maxTokens: 2000,
            mode: 'chat'
        };
    }
    /**
       * Get the system prompt for Chat mode
       */
    async getSystemPrompt(
    // @ts-ignore - Parameter required by interface but not used in this implementation
    _agent, 
    // @ts-ignore - Parameter required by interface but not used in this implementation
    _contextSource) {
        return promptManager_1.promptManager.renderPrompt('mode.chat', {});
    }
    /**
       * Get UI components specific to Chat mode
       */
    getUIComponents() {
        return {
            messageInput: `
<div class="message-input-container">
    <textarea id="message-input" placeholder="Chat with the AI assistant..."></textarea>
    <button id="btn-send" title="Send"><i class="codicon codicon-send"></i></button>
</div>
`
        };
    }
}
exports.ChatMode = ChatMode;
//# sourceMappingURL=chatMode.js.map