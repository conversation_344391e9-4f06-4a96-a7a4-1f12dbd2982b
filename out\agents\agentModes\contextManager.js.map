{"version": 3, "file": "contextManager.js", "sourceRoot": "", "sources": ["../../../src/agents/agentModes/contextManager.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,uCAAyB;AACzB,2CAA6B;AAC7B,mDAA6D;AAC7D,yCAAsC;AAEtC;;GAEG;AACH,MAAa,cAAc;IACjB,MAAM,CAAC,QAAQ,CAAiB;IAChC,OAAO,CAAsC;IAC7C,aAAa,CAAqB;IAE1C;QACE,qBAAqB;QACrB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtF,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC;QACvE,CAAC;IACH,CAAC;IAED;;SAEK;IACE,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;YAC7B,cAAc,CAAC,QAAQ,GAAG,IAAI,cAAc,EAAE,CAAC;QACjD,CAAC;QACD,OAAO,cAAc,CAAC,QAAQ,CAAC;IACjC,CAAC;IAED;;SAEK;IACE,UAAU,CAAC,OAAgC;QAChD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,iBAAiB,CAAC,aAA4B;QACzD,IAAI,CAAC;YACH,QAAQ,aAAa,CAAC,IAAI,EAAE,CAAC;gBAC3B,KAAK,2BAAW,CAAC,IAAI;oBACnB,OAAO,EAAE,CAAC;gBAEZ,KAAK,2BAAW,CAAC,eAAe;oBAC9B,OAAO,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAE/C,KAAK,2BAAW,CAAC,cAAc;oBAC7B,OAAO,MAAM,IAAI,CAAC,uBAAuB,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;gBAEvE,KAAK,2BAAW,CAAC,YAAY;oBAC3B,OAAO,MAAM,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAE5C,KAAK,2BAAW,CAAC,MAAM;oBACrB,OAAO,aAAa,CAAC,aAAa,IAAI,EAAE,CAAC;gBAE3C;oBACE,OAAO,EAAE,CAAC;YACd,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;;;SAIK;IACG,KAAK,CAAC,wBAAwB;QACpC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,OAAO,oBAAoB,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC;YACH,0CAA0C;YAC1C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAE5D,gDAAgD;YAChD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAE5D,OAAO;;EAEX,QAAQ;;;EAGR,QAAQ;CACT,CAAC;QACE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YACvE,OAAO,iCAAiC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,uBAAuB,CAAC,SAAmB;QACvD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3B,OAAO,oBAAoB,CAAC;QAC9B,CAAC;QAED,IAAI,CAAC;YACH,IAAI,OAAO,GAAG,EAAE,CAAC;YAEjB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,IAAI,CAAC;oBACH,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;oBAC9D,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;oBAEjG,OAAO,IAAI;QACb,YAAY;;EAElB,OAAO;;;CAGR,CAAC;gBACM,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,sBAAsB,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;oBAChE,OAAO,IAAI;QACb,QAAQ;;;CAGf,CAAC;gBACM,CAAC;YACH,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;YACtE,OAAO,uCAAuC,CAAC;QACjD,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,qBAAqB;QACjC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAE9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,eAAe,CAAC;QACzB,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;YACjC,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;YACnC,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAEjG,OAAO;QACL,YAAY;;EAElB,OAAO;;CAER,CAAC;QACE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;YACpE,OAAO,qCAAqC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,WAAW,CAAC,QAAgB,EAAE,KAAK,GAAG,CAAC;QACnD,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAClD,IAAI,IAAI,GAAG,EAAE,CAAC;YAEd,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,gCAAgC;gBAChC,IAAI,IAAI,KAAK,cAAc,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;oBACtF,SAAS;gBACX,CAAC;gBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAC3C,MAAM,KAAK,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBAE/C,IAAI,KAAK,CAAC,WAAW,EAAE,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;oBACrC,IAAI,IAAI,GAAG,IAAI,KAAK,CAAC;oBACrB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;oBAC5D,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;gBACzE,CAAC;qBAAM,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;oBAC1B,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC;gBACtB,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,0BAA0B,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;SAEK;IACG,KAAK,CAAC,WAAW,CAAC,QAAgB;QACxC,IAAI,CAAC;YACH,wBAAwB;YACxB,MAAM,eAAe,GAAG;gBACtB,cAAc;gBACd,eAAe;gBACf,WAAW;gBACX,cAAc;gBACd,cAAc;gBACd,aAAa;gBACb,aAAa;gBACb,YAAY;gBACZ,YAAY;aACb,CAAC;YAEF,IAAI,eAAe,GAAG,EAAE,CAAC;YAEzB,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE,CAAC;gBACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;gBAE9C,IAAI,CAAC;oBACH,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC5B,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;wBAC9D,eAAe,IAAI;QACvB,OAAO;;EAEb,OAAO;;;CAGR,CAAC;oBACQ,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,0BAA0B,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;gBACrE,CAAC;YACH,CAAC;YAED,OAAO,eAAe,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACzD,OAAO,0BAA0B,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,0BAA0B;QACrC,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;QAE9C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,EAAE,IAAI,EAAE,2BAAW,CAAC,IAAI,EAAE,CAAC;QACpC,CAAC;QAED,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QACjC,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;QAEnC,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACtB,oCAAoC;YACpC,OAAO;gBACL,IAAI,EAAE,2BAAW,CAAC,YAAY;aAC/B,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,wBAAwB;YACxB,MAAM,YAAY,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAEjD,OAAO;gBACL,IAAI,EAAE,2BAAW,CAAC,MAAM;gBACxB,aAAa,EAAE,YAAY;aAC5B,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,2BAA2B;QACtC,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;YAC/C,cAAc,EAAE,IAAI;YACpB,gBAAgB,EAAE,KAAK;YACvB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,0BAA0B;SACtC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,EAAE,IAAI,EAAE,2BAAW,CAAC,IAAI,EAAE,CAAC;QACpC,CAAC;QAED,OAAO;YACL,IAAI,EAAE,2BAAW,CAAC,cAAc;YAChC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;SACtC,CAAC;IACJ,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,6BAA6B;QACxC,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC;YACjD,cAAc,EAAE,KAAK;YACrB,gBAAgB,EAAE,IAAI;YACtB,aAAa,EAAE,IAAI;YACnB,SAAS,EAAE,4BAA4B;SACxC,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrC,OAAO,EAAE,IAAI,EAAE,2BAAW,CAAC,IAAI,EAAE,CAAC;QACpC,CAAC;QAED,OAAO;YACL,IAAI,EAAE,2BAAW,CAAC,cAAc;YAChC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC;SAC9C,CAAC;IACJ,CAAC;IAED;;SAEK;IACE,KAAK,CAAC,kCAAkC,CAAC,IAAc;QAC5D,OAAO;YACL,IAAI,EAAE,2BAAW,CAAC,MAAM;YACxB,iBAAiB,EAAE,IAAI;SACxB,CAAC;IACJ,CAAC;CACF;AA5TD,wCA4TC;AAED,4BAA4B;AACf,QAAA,cAAc,GAAG,cAAc,CAAC,WAAW,EAAE,CAAC", "sourcesContent": ["import * as vscode from 'vscode';\nimport * as fs from 'fs';\nimport * as path from 'path';\nimport { ContextSource, ContextType } from './operationMode';\nimport { Logger } from '../../logger';\n\n/**\n * Manages context for operation modes\n */\nexport class ContextManager {\n  private static instance: ContextManager;\n  private context: vscode.ExtensionContext | undefined;\n  private workspaceRoot: string | undefined;\n\n  private constructor() {\n    // Get workspace root\n    if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders.length > 0) {\n      this.workspaceRoot = vscode.workspace.workspaceFolders[0].uri.fsPath;\n    }\n  }\n\n  /**\n     * Get the singleton instance\n     */\n  public static getInstance(): ContextManager {\n    if (!ContextManager.instance) {\n      ContextManager.instance = new ContextManager();\n    }\n    return ContextManager.instance;\n  }\n\n  /**\n     * Initialize the context manager\n     */\n  public initialize(context: vscode.ExtensionContext): void {\n    this.context = context;\n  }\n\n  /**\n     * Get context content based on context source\n     */\n  public async getContextContent(contextSource: ContextSource): Promise<string> {\n    try {\n      switch (contextSource.type) {\n        case ContextType.NONE:\n          return '';\n\n        case ContextType.ENTIRE_CODEBASE:\n          return await this.getEntireCodebaseContext();\n\n        case ContextType.SELECTED_FILES:\n          return await this.getSelectedFilesContext(contextSource.files || []);\n\n        case ContextType.CURRENT_FILE:\n          return await this.getCurrentFileContext();\n\n        case ContextType.CUSTOM:\n          return contextSource.customContent || '';\n\n        default:\n          return '';\n      }\n    } catch (error) {\n      Logger.instance.error('Error getting context content:', error);\n      return '';\n    }\n  }\n\n  /**\n     * Get context for the entire codebase\n     * Note: This is a simplified implementation. In a real-world scenario,\n     * you would want to use a more sophisticated approach to handle large codebases.\n     */\n  private async getEntireCodebaseContext(): Promise<string> {\n    if (!this.workspaceRoot) {\n      return 'No workspace open.';\n    }\n\n    try {\n      // Get a summary of the codebase structure\n      const fileTree = await this.getFileTree(this.workspaceRoot);\n\n      // Get content of key files (this is simplified)\n      const keyFiles = await this.getKeyFiles(this.workspaceRoot);\n\n      return `\nWorkspace structure:\n${fileTree}\n\nKey files:\n${keyFiles}\n`;\n    } catch (error) {\n      Logger.instance.error('Error getting entire codebase context:', error);\n      return 'Error getting codebase context.';\n    }\n  }\n\n  /**\n     * Get context for selected files\n     */\n  private async getSelectedFilesContext(filePaths: string[]): Promise<string> {\n    if (filePaths.length === 0) {\n      return 'No files selected.';\n    }\n\n    try {\n      let context = '';\n\n      for (const filePath of filePaths) {\n        try {\n          const content = await fs.promises.readFile(filePath, 'utf-8');\n          const relativePath = this.workspaceRoot ? path.relative(this.workspaceRoot, filePath) : filePath;\n\n          context += `\nFile: ${relativePath}\n\\`\\`\\`\n${content}\n\\`\\`\\`\n\n`;\n        } catch (error) {\n          Logger.instance.error(`Error reading file ${filePath}:`, error);\n          context += `\nFile: ${filePath}\nError reading file.\n\n`;\n        }\n      }\n\n      return context;\n    } catch (error) {\n      Logger.instance.error('Error getting selected files context:', error);\n      return 'Error getting selected files context.';\n    }\n  }\n\n  /**\n     * Get context for the current file\n     */\n  private async getCurrentFileContext(): Promise<string> {\n    const editor = vscode.window.activeTextEditor;\n\n    if (!editor) {\n      return 'No file open.';\n    }\n\n    try {\n      const document = editor.document;\n      const content = document.getText();\n      const fileName = document.fileName;\n      const relativePath = this.workspaceRoot ? path.relative(this.workspaceRoot, fileName) : fileName;\n\n      return `\nFile: ${relativePath}\n\\`\\`\\`\n${content}\n\\`\\`\\`\n`;\n    } catch (error) {\n      Logger.instance.error('Error getting current file context:', error);\n      return 'Error getting current file context.';\n    }\n  }\n\n  /**\n     * Get a file tree representation of the workspace\n     */\n  private async getFileTree(rootPath: string, depth = 3): Promise<string> {\n    try {\n      const files = await fs.promises.readdir(rootPath);\n      let tree = '';\n\n      for (const file of files) {\n        // Skip node_modules, .git, etc.\n        if (file === 'node_modules' || file === '.git' || file === 'dist' || file === 'build') {\n          continue;\n        }\n\n        const filePath = path.join(rootPath, file);\n        const stats = await fs.promises.stat(filePath);\n\n        if (stats.isDirectory() && depth > 0) {\n          tree += `${file}/\\n`;\n          const subTree = await this.getFileTree(filePath, depth - 1);\n          tree += subTree.split('\\n').map(line => `  ${line}`).join('\\n') + '\\n';\n        } else if (stats.isFile()) {\n          tree += `${file}\\n`;\n        }\n      }\n\n      return tree;\n    } catch (error) {\n      Logger.instance.error('Error getting file tree:', error);\n      return 'Error getting file tree.';\n    }\n  }\n\n  /**\n     * Get content of key files in the workspace\n     */\n  private async getKeyFiles(rootPath: string): Promise<string> {\n    try {\n      // Key files to look for\n      const keyFilePatterns = [\n        'package.json',\n        'tsconfig.json',\n        'README.md',\n        'src/index.ts',\n        'src/index.js',\n        'src/main.ts',\n        'src/main.js',\n        'src/app.ts',\n        'src/app.js'\n      ];\n\n      let keyFilesContent = '';\n\n      for (const pattern of keyFilePatterns) {\n        const filePath = path.join(rootPath, pattern);\n\n        try {\n          if (fs.existsSync(filePath)) {\n            const content = await fs.promises.readFile(filePath, 'utf-8');\n            keyFilesContent += `\nFile: ${pattern}\n\\`\\`\\`\n${content}\n\\`\\`\\`\n\n`;\n          }\n        } catch (error) {\n          Logger.instance.debug(`Error reading key file ${pattern}:`, error);\n        }\n      }\n\n      return keyFilesContent;\n    } catch (error) {\n      Logger.instance.error('Error getting key files:', error);\n      return 'Error getting key files.';\n    }\n  }\n\n  /**\n     * Create a context source from a selection\n     */\n  public async createContextFromSelection(): Promise<ContextSource> {\n    const editor = vscode.window.activeTextEditor;\n\n    if (!editor) {\n      return { type: ContextType.NONE };\n    }\n\n    const document = editor.document;\n    const selection = editor.selection;\n\n    if (selection.isEmpty) {\n      // No selection, use the entire file\n      return {\n        type: ContextType.CURRENT_FILE\n      };\n    } else {\n      // Use the selected text\n      const selectedText = document.getText(selection);\n\n      return {\n        type: ContextType.CUSTOM,\n        customContent: selectedText\n      };\n    }\n  }\n\n  /**\n     * Create a context source from a file picker\n     */\n  public async createContextFromFilePicker(): Promise<ContextSource> {\n    const files = await vscode.window.showOpenDialog({\n      canSelectFiles: true,\n      canSelectFolders: false,\n      canSelectMany: true,\n      openLabel: 'Select Files for Context'\n    });\n\n    if (!files || files.length === 0) {\n      return { type: ContextType.NONE };\n    }\n\n    return {\n      type: ContextType.SELECTED_FILES,\n      files: files.map(file => file.fsPath)\n    };\n  }\n\n  /**\n     * Create a context source from a folder picker\n     */\n  public async createContextFromFolderPicker(): Promise<ContextSource> {\n    const folders = await vscode.window.showOpenDialog({\n      canSelectFiles: false,\n      canSelectFolders: true,\n      canSelectMany: true,\n      openLabel: 'Select Folders for Context'\n    });\n\n    if (!folders || folders.length === 0) {\n      return { type: ContextType.NONE };\n    }\n\n    return {\n      type: ContextType.SELECTED_FILES,\n      folders: folders.map(folder => folder.fsPath)\n    };\n  }\n\n  /**\n     * Create a context source from external resources\n     */\n  public async createContextFromExternalResources(urls: string[]): Promise<ContextSource> {\n    return {\n      type: ContextType.CUSTOM,\n      externalResources: urls\n    };\n  }\n}\n\n// Export singleton instance\nexport const contextManager = ContextManager.getInstance();\n"]}